import { DateTime } from 'luxon';
import { ScheduleShiftDto } from '@gioa/api/src/schemas';
import {useNow} from "@/hooks/useToday";
import {some} from "lodash";

/**
 * Determines if a user is currently on shift based on their shifts and current time
 * @param shifts - Array of shifts assigned to the user
 * @param now - Current time
 * @param timezone - Timezone of the store
 * @returns boolean indicating if user is currently on shift
 */
export function isUserCurrentlyOnShift(shifts: ScheduleShiftDto[], now: DateTime, timezone: string): boolean {
  return some(shifts, shift => {
    if (!shift.startAbs || !shift.endAbs) {
      return false;
    }

    const shiftStart = DateTime.fromJSDate(shift.startAbs).setZone(timezone);
    const shiftEnd = DateTime.fromJSDate(shift.endAbs).setZone(timezone);

    // Check if current time is within the shift range
    return now >= shiftStart && now <= shiftEnd;
  });
}
