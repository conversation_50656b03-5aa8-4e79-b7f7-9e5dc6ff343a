import React from "react";
import Svg, {Path} from "react-native-svg";

export const AssignmentAddIcon: React.FC<React.SVGProps<SVGSVGElement>> = ({}) => {
  return (
    <Svg width="17" height="18" viewBox="0 0 17 18" fill="none">
      <Path
        d="M8.0013 2.62285C8.18186 2.62285 8.33116 2.56382 8.44922 2.44577C8.56727 2.32771 8.6263 2.17841 8.6263 1.99785C8.6263 1.8173 8.56727 1.66799 8.44922 1.54993C8.33116 1.43188 8.18186 1.37285 8.0013 1.37285C7.82075 1.37285 7.67144 1.43188 7.55339 1.54993C7.43533 1.66799 7.3763 1.8173 7.3763 1.99785C7.3763 2.17841 7.43533 2.32771 7.55339 2.44577C7.67144 2.56382 7.82075 2.62285 8.0013 2.62285ZM13.0013 17.7991C11.9607 17.7991 11.0756 17.4342 10.3459 16.7045C9.61616 15.9748 9.2513 15.0897 9.2513 14.0491C9.2513 13.0085 9.61616 12.1234 10.3459 11.3937C11.0756 10.664 11.9607 10.2991 13.0013 10.2991C14.0419 10.2991 14.927 10.664 15.6567 11.3937C16.3864 12.1234 16.7513 13.0085 16.7513 14.0491C16.7513 15.0897 16.3864 15.9748 15.6567 16.7045C14.927 17.4342 14.0419 17.7991 13.0013 17.7991ZM2.42443 16.0843C2.01012 16.0843 1.65547 15.9368 1.36047 15.6418C1.06547 15.3468 0.917969 14.9922 0.917969 14.5779V3.4241C0.917969 3.0098 1.06547 2.65514 1.36047 2.36014C1.65547 2.06514 2.01012 1.91764 2.42443 1.91764H6.13276C6.18512 1.455 6.38707 1.0616 6.73859 0.737434C7.08998 0.413129 7.51221 0.250977 8.00526 0.250977C8.49832 0.250977 8.92061 0.413129 9.27214 0.737434C9.62366 1.0616 9.8229 1.455 9.86984 1.91764H13.5782C13.9925 1.91764 14.3471 2.06514 14.6421 2.36014C14.9371 2.65514 15.0846 3.0098 15.0846 3.4241V7.78139C15.0846 8.01112 14.9938 8.1898 14.8121 8.31744C14.6305 8.44507 14.4296 8.47792 14.2096 8.41598C14.0216 8.36264 13.8255 8.32396 13.6215 8.29994C13.4175 8.27591 13.2107 8.26389 13.0013 8.26389C12.7844 8.26389 12.5766 8.27667 12.378 8.30223C12.1792 8.32792 11.981 8.36799 11.7834 8.42244C11.7406 8.41119 11.6893 8.4 11.6294 8.38889C11.5814 8.38889 11.5253 8.38674 11.4613 8.38244C11.3971 8.37813 11.3309 8.37598 11.2626 8.37598H4.66797C4.49089 8.37598 4.34248 8.43591 4.22276 8.55577C4.1029 8.67563 4.04297 8.8241 4.04297 9.00119C4.04297 9.17841 4.1029 9.32681 4.22276 9.44639C4.34248 9.56612 4.49089 9.62598 4.66797 9.62598H9.33943C9.00401 9.88875 8.70623 10.1871 8.44609 10.521C8.18596 10.8549 7.96609 11.219 7.78651 11.6133H4.66797C4.49089 11.6133 4.34248 11.6731 4.22276 11.7929C4.1029 11.9127 4.04297 12.0613 4.04297 12.2385C4.04297 12.4156 4.1029 12.564 4.22276 12.6837C4.34248 12.8033 4.49089 12.8631 4.66797 12.8631H7.39068C7.34693 13.0501 7.31491 13.2363 7.29464 13.4216C7.27436 13.607 7.26422 13.8001 7.26422 14.001C7.26422 14.2093 7.27276 14.4144 7.28984 14.6164C7.30693 14.8183 7.3395 15.0186 7.38755 15.2172C7.43561 15.432 7.39672 15.6301 7.27089 15.8118C7.14491 15.9935 6.97491 16.0843 6.76089 16.0843H2.42443ZM12.6328 14.4176V16.1324C12.6328 16.2306 12.6696 16.3166 12.7434 16.3904C12.817 16.4641 12.903 16.501 13.0013 16.501C13.0996 16.501 13.1856 16.4641 13.2592 16.3904C13.333 16.3166 13.3698 16.2306 13.3698 16.1324V14.4176H15.0846C15.183 14.4176 15.2689 14.3808 15.3426 14.307C15.4163 14.2333 15.4532 14.1473 15.4532 14.0491C15.4532 13.9508 15.4163 13.8647 15.3426 13.791C15.2689 13.7174 15.183 13.6806 15.0846 13.6806H13.3698V11.9658C13.3698 11.8674 13.333 11.7814 13.2592 11.7076C13.1856 11.634 13.0996 11.5972 13.0013 11.5972C12.903 11.5972 12.817 11.634 12.7434 11.7076C12.6696 11.7814 12.6328 11.8674 12.6328 11.9658V13.6806H10.918C10.8196 13.6806 10.7337 13.7174 10.6601 13.791C10.5863 13.8647 10.5494 13.9508 10.5494 14.0491C10.5494 14.1473 10.5863 14.2333 10.6601 14.307C10.7337 14.3808 10.8196 14.4176 10.918 14.4176H12.6328ZM4.66797 6.38868H11.2626C11.4078 6.38868 11.5603 6.32882 11.7201 6.2091C11.8798 6.08924 11.9596 5.9407 11.9596 5.76348C11.9596 5.58639 11.8997 5.43799 11.7798 5.31827C11.6601 5.19868 11.5117 5.13889 11.3346 5.13889H4.66797C4.49089 5.13889 4.34248 5.19882 4.22276 5.31869C4.1029 5.43841 4.04297 5.58688 4.04297 5.7641C4.04297 5.94119 4.1029 6.08959 4.22276 6.20931C4.34248 6.32889 4.49089 6.38868 4.66797 6.38868Z"
        fill="#FF6F4C"/>
    </Svg>
  )
}

export const SportsIcon: React.FC<React.SVGProps<SVGSVGElement>> = ({}) => {
  return (
    <Svg width="20" height="12" viewBox="0 0 20 12" fill="none">
      <Path
        d="M8.51314 11.4433C7.00681 11.4433 5.72322 10.9128 4.66239 9.85201C3.60139 8.79101 3.07089 7.50735 3.07089 6.00101C3.07089 5.79418 3.08406 5.58735 3.11039 5.38051C3.13672 5.17368 3.16972 4.98818 3.20939 4.82401C3.13256 4.85735 3.04539 4.87918 2.94789 4.88951C2.85039 4.89968 2.76481 4.90476 2.69114 4.90476C2.07131 4.90476 1.55014 4.69285 1.12764 4.26901C0.705307 3.84518 0.494141 3.32635 0.494141 2.71251C0.494141 2.09868 0.696057 1.57985 1.09989 1.15601C1.50356 0.73218 2.01381 0.520264 2.63064 0.520264C3.13581 0.520264 3.58514 0.664846 3.97864 0.954013C4.37231 1.24301 4.63514 1.61001 4.76714 2.05501C5.25297 1.59335 5.82281 1.2286 6.47664 0.960764C7.13047 0.692764 7.80931 0.558763 8.51314 0.558763H19.5131V3.55876H13.9554V6.00101C13.9554 7.51268 13.4263 8.79768 12.3681 9.85601C11.31 10.9142 10.025 11.4433 8.51314 11.4433ZM2.68639 3.71251C2.96972 3.71251 3.20722 3.61668 3.39889 3.42501C3.59056 3.23335 3.68639 2.99585 3.68639 2.71251C3.68639 2.42918 3.59056 2.19168 3.39889 2.00001C3.20722 1.80835 2.96972 1.71251 2.68639 1.71251C2.40306 1.71251 2.16556 1.80835 1.97389 2.00001C1.78222 2.19168 1.68639 2.42918 1.68639 2.71251C1.68639 2.99585 1.78222 3.23335 1.97389 3.42501C2.16556 3.61668 2.40306 3.71251 2.68639 3.71251ZM8.51489 7.80876C9.01256 7.80876 9.43797 7.6316 9.79114 7.27726C10.1443 6.92293 10.3209 6.49693 10.3209 5.99926C10.3209 5.50176 10.1437 5.07643 9.78939 4.72326C9.43506 4.36993 9.00914 4.19326 8.51164 4.19326C8.01397 4.19326 7.58856 4.37043 7.23539 4.72476C6.88222 5.0791 6.70564 5.5051 6.70564 6.00276C6.70564 6.50026 6.88281 6.9256 7.23714 7.27876C7.59147 7.6321 8.01739 7.80876 8.51489 7.80876Z"
        fill="#3CD856"/>
    </Svg>
  )
}

export const EditNotificationIcon: React.FC<React.SVGProps<SVGSVGElement>> = ({}) => {
  return (
    <Svg width="15" height="18" viewBox="0 0 15 18" fill="none">
      <Path
        d="M6.9984 17.8854C6.54235 17.8854 6.15247 17.7231 5.82873 17.3986C5.50484 17.0741 5.3429 16.684 5.3429 16.2283H8.6571C8.6571 16.6855 8.4947 17.076 8.1699 17.3998C7.84509 17.7235 7.45459 17.8854 6.9984 17.8854ZM13.1875 15.3116H0.8125C0.617708 15.3116 0.454465 15.2458 0.322771 15.1141C0.190924 14.9822 0.125 14.8188 0.125 14.6239C0.125 14.4291 0.190924 14.2658 0.322771 14.1341C0.454465 14.0026 0.617708 13.9368 0.8125 13.9368H1.7821V7.0969C1.7821 5.86413 2.1626 4.77414 2.92358 3.82692C3.68442 2.8797 4.66128 2.27386 5.85417 2.0094V1.43831C5.85417 1.12008 5.96547 0.849509 6.18806 0.626606C6.41066 0.403856 6.68092 0.29248 6.99885 0.29248C7.31694 0.29248 7.58758 0.403856 7.81079 0.626606C8.03415 0.849509 8.14583 1.12008 8.14583 1.43831V2.0094C8.16936 2.01536 8.19289 2.02124 8.21642 2.02704C8.23979 2.033 8.26324 2.03888 8.28677 2.04469C8.5277 2.12108 8.66551 2.28768 8.70019 2.5445C8.73487 2.80132 8.6529 3.02904 8.45429 3.22765L5.59315 6.06565C5.44128 6.21674 5.32051 6.39526 5.23083 6.60121C5.141 6.80715 5.09608 7.01997 5.09608 7.23967V10.1643C5.09608 10.62 5.25833 11.0101 5.58283 11.3346C5.90733 11.6591 6.29745 11.8214 6.75319 11.8214H9.67781C9.89766 11.8214 10.1107 11.7785 10.317 11.6926C10.5231 11.6069 10.7013 11.4888 10.8518 11.3383L11.1637 11.0262C11.36 10.83 11.5848 10.7842 11.8379 10.8887C12.0912 10.9932 12.2179 11.1848 12.2179 11.4634V13.9368H13.1875C13.3823 13.9368 13.5455 14.0027 13.6772 14.1344C13.8091 14.2662 13.875 14.4296 13.875 14.6246C13.875 14.8194 13.8091 14.9826 13.6772 15.1143C13.5455 15.2458 13.3823 15.3116 13.1875 15.3116ZM7.10588 8.98317V7.73513C7.10588 7.62467 7.12581 7.51711 7.16569 7.41246C7.20572 7.30796 7.26858 7.21751 7.35429 7.14113L12.0822 2.4361C12.1869 2.33741 12.3049 2.26102 12.4363 2.20694C12.5678 2.15285 12.7053 2.12581 12.8488 2.12581C12.9923 2.12581 13.1344 2.15583 13.2753 2.21588C13.4161 2.27592 13.5355 2.35406 13.6335 2.45031L14.4814 3.30694C14.5801 3.42091 14.6565 3.5413 14.7105 3.66811C14.7646 3.79491 14.7917 3.93073 14.7917 4.07556C14.7917 4.22055 14.7646 4.36256 14.7105 4.50158C14.6565 4.64061 14.5801 4.75947 14.4814 4.85817L9.77635 9.56319C9.69065 9.6489 9.59722 9.71177 9.49608 9.75179C9.39479 9.79167 9.29022 9.81161 9.18235 9.81161H7.93431C7.69949 9.81161 7.50272 9.73224 7.34398 9.5735C7.18524 9.41477 7.10588 9.21799 7.10588 8.98317ZM12.8509 4.96038L13.6988 4.07556L12.8509 3.21871L11.98 4.08954L12.8509 4.96038Z"
        fill="#FF947A"/>
    </Svg>
  )
}

export const StylusNoteIcon: React.FC<React.SVGProps<SVGSVGElement>> = ({}) => {
  return (
    <Svg width="20" height="13" viewBox="0 0 20 13" fill="none">
      <Path
        d="M1.69678 9.01104C1.69678 9.50711 1.8672 9.89494 2.20805 10.1745C2.5489 10.4543 3.09424 10.6412 3.84407 10.7353C4.02969 10.7599 4.17888 10.8441 4.29163 10.9881C4.40453 11.1321 4.45625 11.297 4.44678 11.4826C4.43746 11.6777 4.36787 11.8387 4.23801 11.9657C4.10815 12.0925 3.95331 12.1436 3.77349 12.119C2.62994 11.978 1.76881 11.6478 1.19009 11.1283C0.611215 10.6089 0.321777 9.90311 0.321777 9.01104C0.321777 8.08276 0.711361 7.3298 1.49053 6.75215C2.26969 6.17449 3.35266 5.82456 4.73942 5.70233C5.40584 5.64474 5.90565 5.52427 6.23886 5.34094C6.57192 5.1576 6.73844 4.90376 6.73844 4.57942C6.73844 4.17868 6.56481 3.86373 6.21755 3.63456C5.87028 3.4054 5.30065 3.23146 4.50865 3.11275C4.32287 3.088 4.17063 3.00283 4.05192 2.85723C3.93321 2.71148 3.88624 2.54572 3.91099 2.35994C3.93558 2.16499 4.02045 2.00641 4.16559 1.88419C4.31073 1.76197 4.47909 1.71613 4.67067 1.74669C5.83881 1.92406 6.70468 2.2449 7.26828 2.70919C7.83172 3.17333 8.11344 3.79674 8.11344 4.57942C8.11344 5.30679 7.83409 5.88353 7.27538 6.30963C6.71652 6.73557 5.90527 6.99147 4.84163 7.07733C3.79342 7.16549 3.00723 7.36998 2.48305 7.69081C1.95887 8.01165 1.69678 8.45172 1.69678 9.01104ZM11.0839 11.5443L7.94592 8.4065L15.8082 0.553188C16.0784 0.282924 16.3943 0.149243 16.7558 0.152146C17.1171 0.155049 17.4329 0.288729 17.7032 0.553188L18.9372 1.78702C19.2075 2.05744 19.3426 2.37476 19.3426 2.73898C19.3426 3.10335 19.2075 3.42067 18.9372 3.69094L11.0839 11.5443ZM7.32374 12.8753C7.11688 12.9246 6.93476 12.8705 6.7774 12.713C6.61989 12.5555 6.56581 12.3734 6.61515 12.1667L7.21969 9.21752L10.2589 12.2565L7.32374 12.8753Z"
        fill="#007AFF"/>
    </Svg>
  )
}

export const ThumbsUpIcon: React.FC<React.SVGProps<SVGSVGElement>> = ({}) => {
  return (
    <Svg width="16" height="15" viewBox="0 0 16 15" fill="none">
      <Path
        d="M12.2981 14.3747H4.87025V5.37474L9.84613 0.427734L10.4806 1.06223C10.5586 1.14011 10.6233 1.24323 10.6747 1.37161C10.7262 1.49998 10.7519 1.62092 10.7519 1.73442V1.92473L9.95563 5.37474H14.5192C14.8759 5.37474 15.1911 5.51155 15.4646 5.78517C15.7382 6.05867 15.875 6.3738 15.875 6.73055V7.94199C15.875 8.01986 15.8668 8.10399 15.8504 8.19436C15.8341 8.28474 15.8096 8.36892 15.7769 8.44692L13.6278 13.518C13.5202 13.7584 13.3399 13.9613 13.0871 14.1267C12.8342 14.292 12.5712 14.3747 12.2981 14.3747ZM3.74525 5.37474V14.3747H0.875V5.37474H3.74525Z"
        fill="#BF83FF"/>
    </Svg>
  )
}