import {View} from "react-native";
import {cn} from "@/style.util";
import React, {Suspense, useEffect, useState} from "react";
import {ScreenLoadingIndicator} from "@/components/ScreenLoadingIndicator";
import {ScreenErrorBoundary} from "@/components/ScreenErrorBoundary";
import {CorrectiveActionStatus} from "../../../../../../../../api/src/corrective-actions";
import {useLocalSearchParams} from "expo-router";
import {DateTime} from "luxon";
import {useDisclosure} from "@/hooks/useDisclosure";
import {FilterBar} from "@/components/FilterBar";
import {FilterButton} from "@/components/FilterButton";
import {api} from "@/api";
import {AllFeedbackFilterBottomSheet} from "@/components/AllFeedbackFilterBottomSheet";
import {FeedbackType} from "../../../../../../../../api/src/insights.schemas";
import {AllFeedbackList} from "@/components/AllFeedbackList";
import {ExpandableSearchInput} from "@/components/ExpandableSearchInput";
import {PositionScoreList} from "@/components/PositionScoreList";
import {DateRangeFilterBottomSheet} from "@/components/DateRangeFilterBottomSheet";
import SegmentedControl from "@react-native-segmented-control/segmented-control";

export function InsightsFeedbackScreenComp() {
  const {storeId, beforeDateStr, afterDateStr} = useLocalSearchParams<{ storeId: string; beforeDateStr?: string; afterDateStr?: string; }>();
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);
  const [store] = api.user.getStore.useSuspenseQuery({storeId: storeId}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const [filterStatuses, setFilterStatuses] = useState<CorrectiveActionStatus[] | undefined>(["inReview", "unacknowledged"]);
  const [filterFeedbackTypes, setFilterFeedbackTypes] = useState<FeedbackType[] | undefined>();
  const [filterAfter, setFilterAfter] = useState<DateTime>();
  const [filterBefore, setFilterBefore] = useState<DateTime>();
  const feedbackBottomSheet = useDisclosure();

  const [recipientNameInput, setRecipientNameInput] = useState<string>("");
  const [filterRecipientName, setFilterRecipientName] = useState<string>();
  const scoresBottomSheet = useDisclosure();

  useEffect(() => {
    setFilterAfter(afterDateStr ? DateTime.fromISO(afterDateStr) : undefined);
  }, [afterDateStr]);
  useEffect(() => {
    setFilterBefore(beforeDateStr ? DateTime.fromISO(beforeDateStr) : undefined);
  }, [beforeDateStr]);

  const onSubmitRecipientName = (search: string) => {
    setFilterRecipientName(search);
  };

  const onClearRecipientName = () => {
    setRecipientNameInput("");
    setFilterRecipientName(undefined);
  };

  return (
    <View style={[cn("flex-1 bg-slate-100")]}>
      <View style={cn("py-3 px-4")}>
        <SegmentedControl
          style={{height: 40}}
          fontStyle={{
            fontSize: 16,
          }}
          values={["Team Member", "Position Scores"]}
          selectedIndex={selectedTabIndex}
          onChange={(event) => {
            setSelectedTabIndex(event.nativeEvent.selectedSegmentIndex);
          }}
        />
      </View>
      {selectedTabIndex === 0 ? (
        <View>
          <FilterBar style={cn("px-4 py-1")}>
            <FilterButton
              hasFilter={Boolean(filterAfter) || Boolean(filterBefore) || Boolean(filterStatuses) || Boolean(filterFeedbackTypes)}
              onPress={feedbackBottomSheet.onOpen}
              style={cn("border border-gray-300")}
              colorScheme={"white"}
              variant={"solid"}
            />
          </FilterBar>
          <Suspense fallback={<ScreenLoadingIndicator />}>
            <AllFeedbackList
              storeId={storeId}
              after={filterAfter}
              before={filterBefore}
              includeFeedbackTypes={filterFeedbackTypes}
              includeStatuses={filterStatuses}
            />
          </Suspense>
          <AllFeedbackFilterBottomSheet
            isOpen={feedbackBottomSheet.isOpen}
            onClose={feedbackBottomSheet.onClose}
            filterAfter={filterAfter}
            timezone={store.timezone}
            filterBefore={filterBefore}
            filterFeedbackTypes={filterFeedbackTypes}
            onFeedbackTypeFilterChange={setFilterFeedbackTypes}
            filterStatuses={filterStatuses}
            onStatusFilterChange={setFilterStatuses}
            onAfterFilterChange={setFilterAfter}
            onBeforeFilterChange={setFilterBefore}
          />
        </View>
      ) : (
        <View>
          <FilterBar style={cn("px-4 py-1")}>
            <ExpandableSearchInput
              onSubmitEditing={onSubmitRecipientName}
              onClear={onClearRecipientName}
              onChange={setRecipientNameInput}
              value={recipientNameInput}
            />
            <FilterButton
              hasFilter={Boolean(filterAfter) || Boolean(filterBefore)}
              onPress={scoresBottomSheet.onOpen}
              style={cn("border border-gray-300")}
              colorScheme={"white"}
              variant={"solid"}
            />
          </FilterBar>
          <Suspense fallback={<ScreenLoadingIndicator />}>
            <PositionScoreList
              storeId={storeId}
              after={filterAfter}
              before={filterBefore}
              scoredPersonSearch={filterRecipientName}
            />
          </Suspense>
          <DateRangeFilterBottomSheet
            isOpen={scoresBottomSheet.isOpen}
            onClose={scoresBottomSheet.onClose}
            filterAfter={filterAfter}
            timezone={store.timezone}
            filterBefore={filterBefore}
            onAfterFilterChange={setFilterAfter}
            onBeforeFilterChange={setFilterBefore}
          />
        </View>
      )}
    </View>
  );
}

export default function InsightsFeedbackScreen() {
  return (
    <Suspense fallback={<ScreenLoadingIndicator />}>
      <InsightsFeedbackScreenComp />
    </Suspense>
  );
}

export const ErrorBoundary = ScreenErrorBoundary;
