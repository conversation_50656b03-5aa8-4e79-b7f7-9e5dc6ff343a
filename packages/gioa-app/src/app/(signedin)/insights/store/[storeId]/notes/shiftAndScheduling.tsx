import {Refresh<PERSON><PERSON>rol, <PERSON><PERSON>View, View} from "react-native";
import {cn} from "@/style.util";
import React, {Suspense, useCallback, useMemo, useState} from "react";
import {ScreenLoadingIndicator} from "@/components/ScreenLoadingIndicator";
import {ScreenErrorBoundary} from "@/components/ScreenErrorBoundary";
import {useSafeAreaInsets} from "react-native-safe-area-context";
import {useLocalSearchParams} from "expo-router";
import {api} from "@/api";
import {DateTime} from "luxon";
import {NotesThreadHeader} from "@/components/NotesThreadHeader";
import {colors} from "@/styles";
import {NotesThread} from "@/components/NotesThread";
import {Text} from "@/components/Text";
import {compact, filter, isEmpty} from "lodash";

export function ShiftAndSchedulingNotesScreenComp() {
  const {storeId, activeDateStr} = useLocalSearchParams<{ storeId: string; activeDateStr: string }>();
  const [store] = api.user.getStore.useSuspenseQuery({storeId: storeId!}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const timezone = store.timezone;
  const activeDate = DateTime.fromFormat(activeDateStr, "yyyy-MM-dd", {zone: timezone});
  const safeArea = useSafeAreaInsets();

  const [{items: notes}, getNotes] = api.insights.findShiftLeadAndScheduleNotes.useSuspenseQuery({
    storeId,
    range: {
      start: activeDate.startOf("day").toJSDate(),
      end: activeDate.endOf("day").toJSDate()
    },
    orderBy: [{column: "createdAt", direction: "asc"}],
    take: 1000
  });

  const filteredNotes = useMemo(() => compact(filter(notes, note =>
      DateTime.fromJSDate(note.forAbsDate, {zone: timezone}).hasSame(activeDate, "day"))),
    [notes, activeDate]);

  const [refreshing, setRefreshing] = useState(false);
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    getNotes.refetch().then(() => setRefreshing(false));
  }, [getNotes]);

  return <View style={[cn("flex-1 bg-white"), {paddingBottom: Math.max(safeArea.bottom, 16)}]}>
    <View style={cn("flex-1 pt-4")}>
        <NotesThreadHeader title={"Notes"} activeDate={activeDate}/>

        <ScrollView refreshControl={<RefreshControl tintColor={colors.gioaBlue}
                                                    refreshing={refreshing} onRefresh={onRefresh}/>}
                    style={cn("flex-1")}>
          {isEmpty(filteredNotes) ? <Text style={cn("text-center text-gray-500")}>
            No notes are recorded for this day yet.
          </Text> : null}
          <NotesThread notes={filteredNotes} timezone={timezone}/>
        </ScrollView>
    </View>
  </View>
}

export default function ShiftAndSchedulingNotesScreen() {
  return <Suspense fallback={<ScreenLoadingIndicator/>}>
    <ShiftAndSchedulingNotesScreenComp/>
  </Suspense>
}

export const ErrorBoundary = ScreenErrorBoundary;
