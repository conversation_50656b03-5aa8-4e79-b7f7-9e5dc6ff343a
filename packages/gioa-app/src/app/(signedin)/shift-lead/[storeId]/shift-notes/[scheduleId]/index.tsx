import {Re<PERSON>resh<PERSON><PERSON>rol, <PERSON><PERSON>View, View} from "react-native";
import {cn} from "@/style.util";
import React, {Suspense, useCallback, useState} from "react";
import {ScreenLoadingIndicator} from "@/components/ScreenLoadingIndicator";
import {ScreenErrorBoundary} from "@/components/ScreenErrorBoundary";
import {useSafeAreaInsets} from "react-native-safe-area-context";
import {colors} from "@/styles";
import {router, useLocalSearchParams} from "expo-router";
import {api} from "@/api";
import {useToday} from "@/hooks/useToday";
import {DateTime} from "luxon";
import {genEntityNoteId} from '../../../../../../../../api/src/schemas';
import {useForm} from "@tanstack/react-form";
import {zodValidator} from '@tanstack/zod-form-adapter'
import {Button} from "@/components/Button";
import {FormTextarea} from "@/components/newform/FormTextarea";
import {FieldInfo} from "@/components/newform/FieldInfo";
import {z} from "zod";
import {useAnimatedKeyboardAvoidingBottomPadding} from "@/hooks/useAnimatedKeyboardAvoidingBottomPadding";
import Animated from "react-native-reanimated";
import {NotesThreadHeader} from "@/components/NotesThreadHeader";
import {NotesThreadLoadingOrEmpty} from "@/components/NotesThreadLoadingOrEmpty";
import {NotesThread} from "@/components/NotesThread";
import {filter} from "lodash";

export function ShiftLeadNotesScreenComp() {
  const {storeId, scheduleId, activeDateStr} = useLocalSearchParams<{
    storeId: string;
    scheduleId: string;
    activeDateStr?: string;
  }>()
  const [store] = api.user.getStore.useSuspenseQuery({storeId: storeId}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const timezone = store.timezone;
  const [today, setToday] = useToday(timezone);
  const [activeDate, setActiveDate] = React.useState<DateTime>(activeDateStr ? DateTime.fromFormat(activeDateStr, "yyyy-MM-dd", {zone: timezone}) : today);

  const createNote = api.user.createScheduleNote.useMutation();
  const getNotes = api.user.getScheduleNotes.useQuery({scheduleId: scheduleId}, {
    select: notes => {
      return filter(notes, n => n.dayOfWeek === activeDate.weekday);
    }
  });

  const editNote = api.user.editScheduleNote.useMutation({});
  const onEditNote = (noteId: string, noteText: string) => {
    editNote.mutate(
            {
              noteId,
              scheduleId: scheduleId,
              note: noteText,
            }, {onSuccess: () => getNotes.refetch()}
    );
  };

  const deleteNote = api.user.deleteScheduleNote.useMutation({});
  const onDeleteNote = (noteId: string) => {
    deleteNote.mutate(
            {
              noteId,
              scheduleId: scheduleId
            }, {onSuccess: () => getNotes.refetch()}
    );
  };

  const safeArea = useSafeAreaInsets();

  const form = useForm({
    defaultValues: {
      note: ""
    },
    onSubmit: async ({value}) => {
      createNote.mutate({
        noteId: genEntityNoteId(),
        scheduleId: scheduleId,
        dayOfWeek: activeDate.weekday,
        note: value.note
      }, {
        onSuccess: () => {
          form.reset();
          getNotes.refetch();
        }
      })
    },
    validatorAdapter: zodValidator(),
  });

  const bottomPaddingStyle = useAnimatedKeyboardAvoidingBottomPadding();

  const [refreshing, setRefreshing] = useState(false);
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    getNotes.refetch().then(() => setRefreshing(false));
  }, [getNotes]);

  const goBack = () => {
    router.back();
  }


  return <View style={[cn("flex-1 bg-white py-6"), {paddingBottom: Math.max(safeArea.bottom, 16)}]}>
    <NotesThreadHeader title={"Shift Notes"} activeDate={activeDate}/>

    <ScrollView refreshControl={<RefreshControl tintColor={colors.gioaBlue}
                                                refreshing={refreshing} onRefresh={onRefresh}/>}
                style={cn("flex-1")}>
      <NotesThreadLoadingOrEmpty getNotes={getNotes}/>
      <NotesThread onEditNote={onEditNote} onDeleteNote={onDeleteNote} notes={getNotes.data ?? []} timezone={timezone}/>
    </ScrollView>

    <Animated.View style={[cn("border-t border-gray-200"), bottomPaddingStyle]}>
      <View style={cn("px-4 pt-2")}>
        <form.Field name={"note"}
                    validators={{
                      onSubmit: z.string().min(1, "Required")
                    }}
                    children={field => <View>
                      <FormTextarea field={field}
                                    placeholder="Enter your note about this shift..."/>
                      <FieldInfo field={field}/>
                    </View>}/>

        <View style={cn("flex flex-row gap-2 items-center")}>
          <Button onPress={goBack}
                  variant={"outline"} colorScheme={"gray"}
                  pressableStyle={{flex: 0.5}}
                  isDisabled={createNote.isPending}>
            Close
          </Button>
          <Button onPress={form.handleSubmit}
                  pressableStyle={{flex: 0.5}}
                  isLoading={createNote.isPending}>
            Save
          </Button>
        </View>
      </View>


    </Animated.View>
  </View>
}

export default function ShiftLeadNotesScreen() {
  return <Suspense fallback={<ScreenLoadingIndicator/>}>
    <ShiftLeadNotesScreenComp/>
  </Suspense>
}

export const ErrorBoundary = ScreenErrorBoundary;
