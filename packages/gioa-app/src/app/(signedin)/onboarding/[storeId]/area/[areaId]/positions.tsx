import * as React from "react";
import {Suspense} from "react";
import {ScreenLoadingIndicator} from "../../../../../../components/ScreenLoadingIndicator";
import {StoreDelegateChoiceScreen} from "../../../../../../screens/StoreDelegateChoiceScreen";
import {ScreenErrorBoundary} from "../../../../../../components/ScreenErrorBoundary";
import {StoreAreaPositionsScreen} from "../../../../../../screens/StoreAreaPositionsScreen";

export default function StoreEmployees() {
  return <Suspense fallback={<ScreenLoadingIndicator/>}>
    <StoreAreaPositionsScreen/>
  </Suspense>
}

export const ErrorBoundary = ScreenErrorBoundary;
