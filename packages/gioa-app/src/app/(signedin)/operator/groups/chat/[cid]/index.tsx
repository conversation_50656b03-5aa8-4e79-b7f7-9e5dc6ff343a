import {View} from "react-native";
import {cn} from "@/style.util";
import React, {Suspense, useEffect, useState} from "react";
import {ScreenLoadingIndicator} from "@/components/ScreenLoadingIndicator";
import {ScreenErrorBoundary} from "@/components/ScreenErrorBoundary";
import {useSafeAreaInsets} from "react-native-safe-area-context";
import {
  Channel,
  MessageInput,
  MessageList,
  useAttachmentPickerContext
} from "stream-chat-expo";
import {router, useLocalSearchParams} from "expo-router";
import {Text} from "@/components/Text";
import {Button} from "@/components/Button";
import {ThreadContextValue} from "stream-chat-react-native-core";
import {useChatState} from "@/hooks/useChatState";
import {ChannelHeader} from "@/components/ChannelHeader";
import {StatusBar} from "expo-status-bar";
import {api} from "@/api";
import {useChannelMembers} from "@/components/operatorChannelUtil";
import {useChannelName} from "@/components/channelUtil";
import {isDirectMessageChannel} from "../../../../../../../../api/src/chat/channel";
import {useChatAttachmentHandler} from "@/hooks/useChatAttachmentHandler";

export function ChannelScreenComp() {
  const {cid, messageId, backToStoreId} = useLocalSearchParams<{ cid: string; messageId?: string; backToStoreId?: string }>();
  const {setTopInset, setBottomInset} = useAttachmentPickerContext();
  const [headerHeight, setHeaderHeight] = useState<number>(54);
  const safeArea = useSafeAreaInsets();
  const channel = useChatState(s => s.channel);
  const setThread = useChatState(s => s.setThread);
  const [userProfile] = api.user.getUserProfile.useSuspenseQuery(undefined, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const currentPersonId = userProfile.person.id!;

  // Use shared attachment handling logic
  const {CustomFileAttachment, CustomGallery} = useChatAttachmentHandler();

  const goToThread = (thread: ThreadContextValue["thread"]) => {
    if (!thread) return;

    setThread(thread);

    router.navigate({
      pathname: "/(signedin)/operator/groups/chat/[cid]/thread/[parentMessageId]",
      params: {
        cid: cid,
        parentMessageId: thread.id
      }
    })
  }

  const members = useChannelMembers(channel);
  const onHeaderPress = () => {
    router.navigate({
      pathname: "/(signedin)/operator/groups/chat/[cid]/settings",
      params: {
        cid: channel?.cid
      }
    });
  };

  useEffect(() => {
    if (!headerHeight) return;
    setTopInset(headerHeight);
    setBottomInset(safeArea.bottom);
  }, [headerHeight, setTopInset, setBottomInset, safeArea.bottom]);

  const onBack = () => {
    router.dismissTo({
      pathname: "/(signedin)/operator/groups",
      params: backToStoreId ? { backToStoreId } : undefined
    });
  };

  const channelName = useChannelName({channel, members, currentPersonId, isDirectChannel: isDirectMessageChannel(channel)});

  if (!channel) {
    return <View
            style={[cn("flex-1 bg-white"), {paddingBottom: Math.max(safeArea.bottom, 16), paddingTop: safeArea.top}]}>
      <Text>Loading...</Text>
    </View>
  }

  return <View style={[cn("flex-1 bg-white"),
    {paddingBottom: Math.max(safeArea.bottom, 16)}]}>
    <StatusBar style="light"/>

    <ChannelHeader channel={channel} currentPersonId={currentPersonId} channelName={channelName}
                   members={members} onPress={onHeaderPress} onBack={onBack}
                   onHeightChange={setHeaderHeight}/>
    {!channel ? <View>
              <Text>Channel not found</Text>
              <Button onPress={() => router.back()}>
                Go Back
              </Button>
            </View>
            : null}
    <View style={cn("flex-1")}>
      {channel && headerHeight ?
              <Channel channel={channel} messageId={messageId || undefined}
                       keyboardVerticalOffset={headerHeight}
                       FileAttachment={CustomFileAttachment}
                       Gallery={CustomGallery}>
                <MessageList onThreadSelect={goToThread}/>
                <MessageInput/>
              </Channel> : null}
    </View>
  </View>
}

export default function ChannelScreen() {
  return <Suspense fallback={<ScreenLoadingIndicator/>}>
    <ChannelScreenComp/>
  </Suspense>
}

export const ErrorBoundary = ScreenErrorBoundary;
