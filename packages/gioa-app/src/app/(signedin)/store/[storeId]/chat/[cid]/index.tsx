import {View} from "react-native";
import {cn} from "@/style.util";
import React, {Suspense, useCallback, useEffect, useState} from "react";
import {ScreenLoadingIndicator} from "@/components/ScreenLoadingIndicator";
import {ScreenErrorBoundary} from "@/components/ScreenErrorBoundary";
import {useSafeAreaInsets} from "react-native-safe-area-context";
import {
  Channel,
  messageActions as defaultMessageActions,
  MessageActionsParams,
  MessageInput,
  MessageList,
  useAttachmentPickerContext
} from "stream-chat-expo";
import {router, useLocalSearchParams} from "expo-router";
import {Text} from "@/components/Text";
import {Button} from "@/components/Button";
import type {MessageActionType} from "stream-chat-react-native-core";
import {ThreadContextValue} from "stream-chat-react-native-core";
import {useChatState} from "@/hooks/useChatState";
import {ChannelHeader} from "@/components/ChannelHeader";
import {StatusBar} from "expo-status-bar";
import {api} from "@/api";
import {useChannelMembers, useChannelName} from "@/components/channelUtil";
import {isDirectMessageChannel} from "../../../../../../../../api/src/chat/channel";
import {Ionicons} from "@expo/vector-icons";
import {useWarmUpBrowser} from "@/hooks/useWarmUpBrowser";
import {useChatAttachmentHandler} from "@/hooks/useChatAttachmentHandler";

export function ChannelScreenComp() {
  const {cid, storeId, messageId} = useLocalSearchParams<{ cid: string; storeId: string; messageId?: string }>();
  const {setTopInset, setBottomInset} = useAttachmentPickerContext();
  const [headerHeight, setHeaderHeight] = useState<number>(54);
  const safeArea = useSafeAreaInsets();
  const chatState = useChatState();

  // Warm up the browser to improve PDF viewing performance
  useWarmUpBrowser();
  const {channel, setChannel} = chatState;

  // Use shared attachment handling logic
  const {CustomFileAttachment, CustomGallery} = useChatAttachmentHandler();
  const [userProfile] = api.user.getUserProfile.useSuspenseQuery(undefined, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const currentPersonId = userProfile.person.id!;

  const customMessageActions = useCallback((param: MessageActionsParams): MessageActionType[] => {
    const { isMyMessage, dismissOverlay } = param;
    const actions = defaultMessageActions({ ...param });
    const message = param.message;

    // Add custom "View Read By" action only for messages owned by current user
    if (isMyMessage && message) {
      actions.unshift({
        action: () => {
          dismissOverlay();
          router.navigate({
            pathname: "/(signedin)/store/[storeId]/chat/[cid]/read-receipts",
            params: {
              storeId: storeId,
              cid: cid,
              messageId: message.id,
            },
          });
        },
        actionType: "viewReadBy",
        icon: <Ionicons name="eye-outline" size={20} color="#666" />,
        title: "View Read By",
      });
    }

    return actions;
  }, [storeId, cid]);

  const goToThread = useCallback((thread: ThreadContextValue["thread"]) => {
    if (!thread) return;

    chatState.setThread(thread);

    router.navigate({
      pathname: "/(signedin)/store/[storeId]/chat/[cid]/thread/[parentMessageId]",
      params: {
        storeId: storeId,
        cid: cid,
        parentMessageId: thread.id
      }
    })
  }, [storeId, cid]);

  useEffect(() => {
    if (!headerHeight) return;
    setTopInset(headerHeight);
    setBottomInset(safeArea.bottom);
  }, [headerHeight, setTopInset, setBottomInset, safeArea.bottom]);

  const members = useChannelMembers(channel, storeId);
  const onHeaderPress = () => {
    router.navigate({
      pathname: "/(signedin)/store/[storeId]/chat/[cid]/settings",
      params: {
        storeId: storeId,
        cid: channel?.cid
      }
    });
  };

  const onBack = () => router.dismissTo({
    pathname: "/(signedin)/(drawer)/(tabs)/[storeId]/groups",
    params: {
      storeId: storeId
    }
  });
  const channelName = useChannelName({channel, members, currentPersonId, isDirectChannel: isDirectMessageChannel(channel)});

  if (!channel) {
    return <View
            style={[cn("flex-1 bg-white"), {paddingBottom: Math.max(safeArea.bottom, 16), paddingTop: safeArea.top}]}>
      <Text>Loading...</Text>
    </View>
  }

  return <View style={[cn("flex-1 bg-white"),
    {paddingBottom: Math.max(safeArea.bottom, 16)}]}>
    <StatusBar style="light"/>

    <ChannelHeader channel={channel} currentPersonId={currentPersonId} channelName={channelName}
                   members={members} onPress={onHeaderPress} onBack={onBack}
                   onHeightChange={setHeaderHeight}/>
    {!channel ? <View>
              <Text>Channel not found</Text>
              <Button onPress={() => router.back()}>
                Go Back
              </Button>
            </View>
            : null}
    <View style={cn("flex-1")}>
      {channel && headerHeight ?
              <Channel channel={channel} messageId={messageId || undefined}
                       keyboardVerticalOffset={headerHeight}
                       messageActions={customMessageActions}
                       FileAttachment={CustomFileAttachment}
                       Gallery={CustomGallery}>
                <MessageList onThreadSelect={goToThread}/>
                <MessageInput/>
              </Channel> : null}
    </View>
  </View>
}

export default function ChannelScreen() {
  return <Suspense fallback={<ScreenLoadingIndicator/>}>
    <ChannelScreenComp/>
  </Suspense>
}

export const ErrorBoundary = ScreenErrorBoundary;
