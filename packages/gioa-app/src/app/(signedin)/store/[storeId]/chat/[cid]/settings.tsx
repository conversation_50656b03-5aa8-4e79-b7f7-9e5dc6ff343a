import {Al<PERSON>, Pressable, ScrollView, StyleSheet, View} from "react-native";
import {cn} from "@/style.util";
import React, { Suspense, useCallback, useMemo, useState } from "react";
import {ScreenLoadingIndicator} from "@/components/ScreenLoadingIndicator";
import {ScreenErrorBoundary} from "@/components/ScreenErrorBoundary";
import {useSafeAreaInsets} from "react-native-safe-area-context";
import {router, useLocalSearchParams} from "expo-router";
import {Text} from "@/components/Text";
import {Button} from "@/components/Button";
import {useChatState} from "@/hooks/useChatState";
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import {colors} from '@/styles';
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuItemTitle,
  DropdownMenuRoot,
  DropdownMenuTrigger
} from "@/components/PopoverMenu";
import {Avatar} from "@/components/Avatar";
import {find, map} from "lodash";
import {IconButton} from "@/components/IconButton";
import {Ionicons} from "@expo/vector-icons";
import {useChannelMembers, useChannelName, useChannelPermissions} from "@/components/channelUtil";
import Join from "@/components/Join";
import {Divider} from "@/components/Divider";
import {StatusBar} from "expo-status-bar";
import Toast from "react-native-root-toast";
import {getHumanReadableErrorMessageString} from "@/components/ErrorAlert";
import {api} from "@/api";
import {getJobIds, isJobMembershipRule} from "../../../../../../../../api/src/chat/membershipRule";
import {OxfordCommaList} from "@/components/OxfordCommaList";
import {
  getChannelMembershipRule,
  isDirectMessageChannel,
  isLegacyPrivateChannel,
  isPrivateChannel,
  isPublicChannel,
} from "../../../../../../../../api/src/chat/channel";
import {Switch} from "@/components/newform/Switch";

export function ChannelSettingsScreenComp() {
  const {cid, storeId} = useLocalSearchParams<{ cid: string; storeId: string }>();
  const safeArea = useSafeAreaInsets();
  const channel = useChatState(s => s.channel);
  const disableChannel = api.chat.disableChannel.useMutation();
  const [{jobs}] = api.user.getBusiness.useSuspenseQuery(undefined, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const [isLeavingChannel, setIsLeavingChannel] = useState(false);
  const [userProfile] = api.user.getUserProfile.useSuspenseQuery(undefined, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const currentPersonId = userProfile.person.id!;

  const members = useChannelMembers(channel, storeId);
  const channelName = useChannelName({
    channel,
    members,
    currentPersonId,
    isDirectChannel: isDirectMessageChannel(channel)
  });
  const channelDescription = channel?.data?.description ?? "";
  const isPrivate = channel ? isPrivateChannel(channel) : false;
  const isLegacyPrivate = channel ? isLegacyPrivateChannel(channel) : false;

  const {isOwnerOrAdmin, ownerMember} = useChannelPermissions(channel, storeId);
  const muteStatus = useMemo(() => {
    if (!channel) return false;
    const {muted} = channel.muteStatus()
    return muted;
  }, [channel])

  const [notificationsEnabled, _setNotificationsEnabled] = useState(!muteStatus);
  const setNotificationsEnabled = useCallback(async (value: boolean) => {
    if (!channel) return;
    if (value) {
      await channel.unmute()
              .catch(() => {
                Toast.show("Failed to unmute, please try again", {
                  duration: Toast.durations.SHORT,
                  position: Toast.positions.TOP,
                  shadow: true,
                  animation: true,
                  hideOnPress: true,
                  delay: 0
                });
                _setNotificationsEnabled(!value);
              });
    } else {
      await channel.mute()
              .catch(() => {
                Toast.show("Failed to mute, please try again", {
                  duration: Toast.durations.SHORT,
                  position: Toast.positions.TOP,
                  shadow: true,
                  animation: true,
                  hideOnPress: true,
                  delay: 0
                });
                _setNotificationsEnabled(!value);
              });
    }
    _setNotificationsEnabled(value);
  }, [channel])

  const handleBack = () => {
    router.back();
  };

  const handleEdit = () => {
    router.navigate({
      pathname: "/(signedin)/store/[storeId]/chat/[cid]/edit",
      params: {
        storeId: storeId,
        cid: cid
      }
    });
  };

  const handleArchiveChannel = () => {
    Alert.alert(
            "Archive Group",
            "Are you sure you want to archive this group? You can unarchive it later.",
            [
              {text: "Cancel", style: "cancel"},
              {
                text: "Archive",
                style: "destructive",
                onPress: async () => {
                  if (!channel || !channel.id || !channel.data?.type) {
                    Alert.alert("Error", "Unable to archive group at this time");
                    return;
                  }

                  // we don't want to disable a direct message channel, as that would prevent the participates from direct messaging eachother again in future. So just archive it. For group channels, we want to disable it so that the channel is no longer visible in the list of channels and nobody can send messages to it.
                  if (isDirectMessageChannel(channel)) {
                    try {

                      await channel.hide();

                      Toast.show("Group archived", {
                        position: Toast.positions.BOTTOM,
                      });

                      // Navigate back to the groups screen since channel is now archived
                      router.dismissTo({
                        pathname: "/(signedin)/(drawer)/(tabs)/[storeId]/groups",
                        params: {storeId}
                      });
                    } catch (error) {
                      console.error('Error archiving channel:', error);
                      Alert.alert("Error", "Failed to archive group. Please try again: " + getHumanReadableErrorMessageString(error));
                    }
                  } else {
                    await channel.hide();
                    disableChannel.mutate({
                      channelId: channel.id,
                      channelType: channel.data.type as "messaging" | "team"
                    }, {
                      onSuccess() {
                        Toast.show("Group archived", {
                          position: Toast.positions.BOTTOM,
                        });

                        // Navigate back to the groups screen since channel is now disabled
                        router.dismissTo({
                          pathname: "/(signedin)/(drawer)/(tabs)/[storeId]/groups",
                          params: {storeId}
                        });
                      },
                      onError(error) {
                        Alert.alert("Error", "Failed to archive group. Please try again: " + getHumanReadableErrorMessageString(error));
                      }
                    })
                  }
                }
              }
            ]
    );
  };

  const handleLeaveChannel = () => {
    if (isLeavingChannel) return;

    Alert.alert(
            "Leave Group",
            "Are you sure you want to leave this group?",
            [
              {text: "Cancel", style: "cancel"},
              {
                text: "Leave",
                style: "destructive",
                onPress: async () => {
                  if (!channel || !currentPersonId) {
                    Alert.alert("Error", "Unable to leave group at this time");
                    return;
                  }

                  setIsLeavingChannel(true);

                  try {
                    await channel.removeMembers([currentPersonId]);

                    Toast.show("Left group", {
                      position: Toast.positions.BOTTOM,
                    });

                    // Navigate back to the groups screen since user can no longer access this channel
                    router.navigate({
                      pathname: "/(signedin)/(drawer)/(tabs)/[storeId]/groups",
                      params: {storeId}
                    });
                  } catch (error) {
                    console.error('Error leaving group:', error);
                    Alert.alert("Error", "Failed to leave group. Please try again: " + getHumanReadableErrorMessageString(error));
                  } finally {
                    setIsLeavingChannel(false);
                  }
                }
              }
            ]
    );
  };

  const handleAddMembers = () => {
    router.navigate({
      pathname: "/(signedin)/store/[storeId]/chat/[cid]/add-members",
      params: {
        storeId: storeId,
        cid: cid
      }
    });
  };

  const  handleRemoveMembers = () => {
    router.navigate({
      pathname: "/(signedin)/store/[storeId]/chat/[cid]/remove-members",
      params: {
        storeId: storeId,
        cid: cid
      }
    });
  };

  const handleSearch = () => {
    router.navigate({
      pathname: "/(signedin)/store/[storeId]/chat/[cid]/search",
      params: {
        storeId: storeId,
        cid: cid
      }
    });
  };

  const handleShowAllMembers = () => {
    router.navigate({
      pathname: "/(signedin)/store/[storeId]/chat/[cid]/all-members",
      params: {
        storeId: storeId,
        cid: cid
      }
    });
  };

  const handleModifyMembershipFilters = () => {
    router.navigate({
      pathname: "/(signedin)/store/[storeId]/chat/[cid]/edit-filters",
      params: {
        storeId: storeId,
        cid: cid
      }
    });
  }

  const membershipRule = channel ? getChannelMembershipRule(channel) : undefined;

  if (!channel) {
    return <View
            style={[cn("flex-1 bg-white items-center justify-center"), {paddingBottom: Math.max(safeArea.bottom, 16)}]}>
      <Text>Channel not found</Text>
      <Button onPress={handleBack}>
        Go Back
      </Button>
    </View>;
  }

  return (
    <View style={[cn("flex-1 bg-slate-100")]}>
      <StatusBar style={"dark"} />

      {/* Header */}
      <View
        style={[
          cn("bg-white flex-row items-center border-b border-gray-200 justify-between"),
          { paddingTop: safeArea.top },
        ]}
      >
        <Button
          onPress={handleBack}
          hitSlop={8}
          leftIcon={() => <FontAwesome6 name="chevron-left" size={20} color={colors.gray[600]} />}
          variant={"link"}
        >
          Back
        </Button>

        <View style={cn("flex-row items-center gap-3")}>
          {isOwnerOrAdmin && !isDirectMessageChannel(channel) ? (
            <Button onPress={handleEdit} variant={"link"} style={cn("px-1")}>
              Edit
            </Button>
          ) : null}

          {isOwnerOrAdmin || isPrivate ? (
            <DropdownMenuRoot>
              <DropdownMenuTrigger>
                <IconButton
                  accessibilityLabel={"More options"}
                  variant={"link"}
                  colorScheme={"gray"}
                  isLoading={disableChannel.isPending || isLeavingChannel}
                  icon={() => <FontAwesome6 name="ellipsis" size={20} color={colors.gray[600]} />}
                />
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {isOwnerOrAdmin ? (
                  <DropdownMenuItem
                    onSelect={handleArchiveChannel}
                    destructive
                    key="archive"
                    disabled={disableChannel.isPending}
                  >
                    <DropdownMenuItemTitle>
                      {disableChannel.isPending ? "Archiving..." : "Archive group"}
                    </DropdownMenuItemTitle>
                  </DropdownMenuItem>
                ) : null}
                {isPrivate ? (
                  <DropdownMenuItem onSelect={handleLeaveChannel} destructive key="leave" disabled={isLeavingChannel}>
                    <DropdownMenuItemTitle>{isLeavingChannel ? "Leaving..." : "Leave group"}</DropdownMenuItemTitle>
                  </DropdownMenuItem>
                ) : null}
              </DropdownMenuContent>
            </DropdownMenuRoot>
          ) : null}
        </View>
      </View>

      <ScrollView style={cn("flex-1")} contentContainerStyle={cn("pb-16")}>
        {/* Channel Info Section */}
        {!isDirectMessageChannel(channel) ? (
          <View style={cn("bg-white mx-4 mt-4 rounded-lg p-4 gap-3")}>
            <View style={cn("flex-row items-center gap-2")}>
              <Ionicons name={"chatbubbles-outline"} size={24} color={colors.gray[700]} />
              <Text semibold size="lg">
                {channelName}
              </Text>
            </View>
            {channelDescription ? (
              <Text size="sm">
                <Text size={"sm"} muted>
                  Description:{" "}
                </Text>
                <Text size={"sm"}>{channelDescription}</Text>
              </Text>
            ) : isOwnerOrAdmin ? (
              <Button
                variant={"link"}
                onPress={handleEdit}
                colorScheme={"link"}
                size={"sm"}
                pressableStyle={cn("items-start")}
              >
                Add description
              </Button>
            ) : null}
          </View>
        ) : null}

        {/* Members Section */}
        <View style={cn("bg-white mx-4 mt-4 rounded-lg px-4 pb-4 pt-3")}>
          <View style={cn("flex-row items-center justify-between mb-3")}>
            <Text semibold size="md">
              Members ({members.length}
              {members.length === 100 ? "+" : ""})
            </Text>
            <Button onPress={handleShowAllMembers} variant={"link"} size={"sm"} colorScheme={"link"} style={cn("pr-0")}>
              Show all
            </Button>
          </View>

          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={cn("flex-row gap-3")}>
              {map(members.slice(0, 20), (member) => {
                return (
                  <View key={member.id} style={[cn("items-center"), { maxWidth: 60 }]}>
                    <Avatar size="md" source={{ uri: member.profileImageUrl }} />
                    <Text size="xs" style={cn("mt-1 text-center flex-1")} numberOfLines={2}>
                      {member.name}
                    </Text>
                  </View>
                );
              })}
            </View>
          </ScrollView>

          {isPublicChannel(channel) ? (
            <View style={cn("mt-4")}>
              <Text muted size={"sm"}>
                This is a public group. Membership automatically syncs with the store's team members.
              </Text>
            </View>
          ) : null}

          {isPrivateChannel(channel) ? (
            <View style={cn("mt-4")}>
              <Text muted size={"sm"}>
                This is a private group.
              </Text>
            </View>
          ) : null}

          {membershipRule && isJobMembershipRule(membershipRule) ? (
            <View style={cn("mt-4")}>
              <Text muted size={"sm"}>
                Membership automatically syncs with{" "}
                <OxfordCommaList
                  itemClassName={"text-sm font-bold"}
                  items={map(getJobIds(membershipRule), (jobId) => {
                    const job = find(jobs, (j) => j.id === jobId);
                    return job?.title || "Unknown";
                  })}
                />{" "}
                jobs.
              </Text>
            </View>
          ) : null}
        </View>

        {/* Actions Section */}
        <View style={cn("bg-white mx-4 mt-4 rounded-lg")}>
          <Join
            separator={
              <Divider size={"xs"} style={{ backgroundColor: colors.gray[300], height: StyleSheet.hairlineWidth }} />
            }
          >
            <Pressable onPress={handleSearch} style={cn("px-4 py-3")}>
              <Text>Search</Text>
            </Pressable>

            {isLegacyPrivate && isOwnerOrAdmin ? (
                    <Pressable onPress={handleAddMembers} style={cn("px-4 py-3")}>
                      <Text>Add members</Text>
                    </Pressable>
            ) : null}

            {isLegacyPrivate && isOwnerOrAdmin ? (
              <Pressable onPress={handleRemoveMembers} style={cn("px-4 py-3")}>
                <Text>Remove members</Text>
              </Pressable>
            ) : null}

            {isPrivate && !isLegacyPrivate && isOwnerOrAdmin ? (
              <Pressable onPress={handleModifyMembershipFilters} style={cn("px-4 py-3")}>
                <Text>Edit membership</Text>
              </Pressable>
            ) : null}
          </Join>
        </View>

        <View style={cn("bg-white mx-4 mt-4 rounded-lg")}>
          <View style={cn("flex flex-row justify-between px-4 py-3")}>
            <View style={cn("flex-1")}>
              <Text semibold>Notifications</Text>
              <Text muted size={"xs"}>
                {notificationsEnabled ? "Enabled" : "Disabled"}
              </Text>
            </View>
            <Switch value={notificationsEnabled} onChange={setNotificationsEnabled} />
          </View>
        </View>

        {!isDirectMessageChannel(channel) ? (
          <View style={cn("px-4 mt-4")}>
            <View>
              <Text size={"sm"} muted>
                Group managed by
              </Text>
              {ownerMember ? (
                <Text size={"sm"}>{ownerMember.name}</Text>
              ) : (
                <Text size={"sm"} muted>
                  Unknown
                </Text>
              )}
            </View>
          </View>
        ) : null}
      </ScrollView>
    </View>
  );
}

export default function ChannelSettingsScreen() {
  return <Suspense fallback={<ScreenLoadingIndicator/>}>
    <ChannelSettingsScreenComp/>
  </Suspense>
}

export const ErrorBoundary = ScreenErrorBoundary;
