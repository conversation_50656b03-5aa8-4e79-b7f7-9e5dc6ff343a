import {Sc<PERSON>View, View} from "react-native";
import {cn} from "@/style.util";
import {Button} from "@/components/Button";
import {filter} from "lodash";
import React from "react";
import {genEntityNoteId} from "../../../api/src/schemas";
import {useForm} from "@tanstack/react-form";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {api} from "@/api";
import {DayOfWeek} from "@/components/AvailabilityWeek.util";
import {FormInput} from "@/components/newform/FormInput";
import {FieldInfo} from "@/components/newform/FieldInfo";
import {z} from "zod";
import {OrientedSheet} from "@/components/OrientedSheet";
import {NotesThreadHeader} from "@/components/NotesThreadHeader";
import {NotesThreadLoadingOrEmpty} from "@/components/NotesThreadLoadingOrEmpty";
import {NotesThread} from "@/components/NotesThread";
import {DateTime} from "luxon";

export const ScheduleDayNotesBottomSheet = ({isOpen, onClose, date, dayOfWeek, timezone, scheduleId}: {
  isOpen: boolean,
  onClose: () => void,
  date: DateTime,
  timezone: string,
  dayOfWeek: DayOfWeek,
  scheduleId: string;
}) => {
  const createNote = api.user.createScheduleNote.useMutation();
  const getScheduleNotes = api.user.getScheduleNotes.useQuery({scheduleId}, {
    select: notes => {
      return filter(notes, n => n.dayOfWeek === dayOfWeek.dayOfWeek);
    }
  });
  const allNotes = getScheduleNotes.data ?? [];

  const editNote = api.user.editScheduleNote.useMutation({});
  const onEditNote = (noteId: string, noteText: string) => {
    editNote.mutate(
            {
              noteId,
              scheduleId: scheduleId,
              note: noteText,
            }, {onSuccess: () => getScheduleNotes.refetch()}
    );
  };

  const deleteNote = api.user.deleteScheduleNote.useMutation({});
  const onDeleteNote = (noteId: string) => {
    deleteNote.mutate(
            {
              noteId,
              scheduleId: scheduleId
            }, {onSuccess: () => getScheduleNotes.refetch()}
    );
  };

  const form = useForm({
    defaultValues: {
      note: ""
    },
    onSubmit: async ({value}) => {
      createNote.mutate({
        noteId: genEntityNoteId(),
        scheduleId: scheduleId,
        dayOfWeek: dayOfWeek.dayOfWeek,
        note: value.note
      }, {
        onSuccess: () => {
          form.reset();
          getScheduleNotes.refetch();
        }
      })
    },
    validatorAdapter: zodValidator(),
  });

  const [isNoteEmpty, setIsNoteEmpty] = React.useState(true);
  React.useEffect(() => {
    return form.store.subscribe(() => {
      const noteEmpty = form.store.state.values.note.trim().length === 0;
      setIsNoteEmpty(noteEmpty);
    });
  }, [form.store]);

  return <OrientedSheet isOpen={isOpen} onClose={onClose} portraitHeightPercentage={0.8}>
    <NotesThreadHeader title={`Scheduling Notes`} className={"mb-0 pb-4 border-b border-gray-200"}
                       activeDate={date}>
      <View style={cn("grow items-end")}>
        <Button colorScheme={"link"} variant={"link"} onPress={onClose}>
          Back
        </Button>
      </View>
    </NotesThreadHeader>
    <ScrollView contentContainerStyle={cn("pb-6 pt-4")}>
      <NotesThreadLoadingOrEmpty getNotes={getScheduleNotes}/>
      <NotesThread onEditNote={onEditNote} onDeleteNote={onDeleteNote} notes={allNotes} timezone={timezone}/>
    </ScrollView>
    <View style={cn("px-4 border-t border-gray-200 pt-2")}>
      <form.Field name={"note"}
                  validators={{
                    onSubmit: z.string().min(1, "Required")
                  }}
                  children={field => <View>
                    <FormInput field={field} multiline style={{height: 80}}
                               placeholder="Enter notes..."/>
                    <FieldInfo field={field}/>
                  </View>}/>
      <Button isDisabled={isNoteEmpty || createNote.isPending}
              onPress={form.handleSubmit} pressableStyle={cn("mx-16")}
              isLoading={createNote.isPending}>
        Send Note
      </Button>
    </View>
  </OrientedSheet>
}
