import {
  ChecklistItemCompletionDto,
  ChecklistItemInteractionDto,
  LiveChecklistItemDto,
  RequirementCompletionsDto
} from "../../../../api/src/checklists/checklist/checklistDto";
import {filter, some} from "lodash";
import {ChecklistItem, ItemRequirement} from "../../../../api/src/checklists/checklistItem/checklistItemTypes";
import {
  ChecklistItemInteraction,
  RequirementCompletions
} from "../../../../api/src/checklists/checklistItem/checklistItemInteractionTypes";

export function isCompletion(interaction: ChecklistItemInteractionDto): interaction is ChecklistItemCompletionDto {
  return interaction.operation === "complete";
}

export function isItemComplete(item: LiveChecklistItemDto): boolean {
  return item.isComplete;
}

export function isCompleteNonCompliant(item: LiveChecklistItemDto): boolean {
  const completion = item.latestInteraction && isCompletion(item.latestInteraction) ? item.latestInteraction : undefined;

  if (!completion) return false;

  return isCompletionNonCompliant(item.requirements, completion.requirementCompletions);
}

export function isItemCompleteNonCompliant(item: ChecklistItem, latestInteraction: ChecklistItemInteraction | undefined): boolean {
  // Check if the latest interaction is a completion
  if (latestInteraction?.operation !== "complete") return false;

  return isCompletionNonCompliant(item.requirements, latestInteraction.requirementCompletions);
}

function isCompletionNonCompliant(
  requirements: ItemRequirement[] | any[],
  requirementCompletions: RequirementCompletions | RequirementCompletionsDto
): boolean {
  const booleanRequirements = filter(requirements, req => {
    return Boolean(req.type === "inputBoolean" && "conditionals" in req && req.conditionals.length > 0);
  });

  return some(booleanRequirements, req => {
    if (req.type !== "inputBoolean" || !("conditionals" in req)) return false;

    const reqCompletion = requirementCompletions[req.id];
    if (!reqCompletion || reqCompletion.type !== "inputBoolean") return false;

    return some(req.conditionals, conditional => {
      return conditional.if === reqCompletion.booleanInput && conditional.isNonCompliant;
    });
  });
}
