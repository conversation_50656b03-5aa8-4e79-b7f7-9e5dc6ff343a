import React from 'react';
import {Pressable, View} from "react-native";
import {cn} from "@/style.util";
import {colors} from "@/styles";
import {Text} from "@/components/Text";
import {Ionicons} from "@expo/vector-icons";
import {Switch} from "@/components/newform/Switch";
import {ItemRequirementDto} from "../../../../api/src/checklists/checklistTemplate/checklistTemplateDtos";
import {assertUnreachable} from "../../../../api/src/util";
import {capitalize, map} from "lodash";

export interface ChecklistRequirementFormRowProps {
  requirement: ItemRequirementDto
  onRemove: () => void;
  onPress?: () => void;
  isRequired: boolean;
  onRequiredChange: (isRequired: boolean) => void;
}

export const getRequirementDetails = (req: ItemRequirementDto): { label: string; icon?: string } => {
  switch (req.type) {
    case 'addImage':
      return {label: 'Add Image'};
    case 'writeComment':
      return {label: 'Write Comment'};
    case 'inputBoolean':
      return {label: 'Yes/no'};
    case 'inputNumber':
      return {label: 'Number Input'};
    default:
      assertUnreachable(req);
  }
};

export function ChecklistRequirementFormRow({
                                              requirement,
                                              onRemove,
                                              onPress, isRequired, onRequiredChange
                                            }: ChecklistRequirementFormRowProps) {

  const Container = onPress ? Pressable : View;
  const {label} = getRequirementDetails(requirement);
  const isBooleanInput = requirement.type === "inputBoolean";
  const showBooleanInputConditionals = isBooleanInput && requirement.conditionals.length > 0;

  return (<Container style={cn("py-3 pl-4 pr-4 border border-gray-200 rounded-lg mb-2")}
                     onPress={onPress}>
      <View style={cn("flex-row items-center gap-3", {
        "border-b border-gray-200 pb-2 mb-2": showBooleanInputConditionals
      })}>
        <Switch value={isRequired}
                onChange={onRequiredChange}/>

        <View style={cn("flex flex-col gap-0 flex-1")}>
          <Text style={cn("text-gray-700")}>
            {label} {requirement.type === "inputNumber" ? <>
            <Text size={"sm"} muted>
              ({capitalize(requirement.numberType)})
            </Text>
          </> : ""}
          </Text>
          <Text size={"xs"} style={cn("text-gray-500")}>
            {isRequired ? "Required" : "Optional"}
          </Text>
        </View>
        <Pressable
          onPress={onRemove}
          style={({pressed}) => [
            cn("p-2 rounded-full"),
            pressed && cn("bg-gray-200")
          ]}
        >
          <Ionicons name="close-circle" size={20} color={colors.gray[500]}/>
        </Pressable>
      </View>

      {showBooleanInputConditionals ?
        map(requirement.conditionals, (cond, idx) => {
          return <View key={idx}>
            <Text size={"sm"} style={cn("text-gray-700 flex-1 mb-2")}>
              <Text semibold size={"sm"} style={cn("text-gray-700")}>
                If {cond.if ? "Yes" : "No"} → {' '}
              </Text>
              {cond.thenText}
            </Text>
            {map(cond.thenRequirements, (req, reqIdx) => {
              const {label} = getRequirementDetails(req);
              return <View key={req.id}
                           style={cn("ml-14 border border-gray-200 rounded-lg py-2 px-3 mb-2 flex flex-row gap-3 justify-between items-center")}>
                <Text size={"sm"}
                      style={cn("text-gray-700")}>
                  {label}
                </Text>
                <Text size={"xs"} style={cn("text-gray-700")}>
                  {req.isRequired ? "Required" : "Optional"}
                </Text>
              </View>
            })}
            {cond.isNonCompliant ? <View style={cn("ml-14 mt-1")}>
              <View style={cn("flex flex-row gap-1 items-center")}>
                <Ionicons name={"warning"} size={16} color={colors.red[500]}/>
                <Text size={"sm"} colorScheme={"danger"}>Compliance Warning:</Text>
                <Text size={"sm"}>A notification will be sent</Text>
              </View>
            </View>: null}
          </View>
        })
        : null}

    </Container>
  );
}
