import {ScrollView, View} from "react-native";
import {cn} from "@/style.util";
import {Text} from "@/components/Text";
import * as React from "react";
import {Ionicons} from "@expo/vector-icons";
import {colors} from "@/styles";
import {FormTextarea} from "@/components/newform/FormTextarea";
import {useForm} from "@tanstack/react-form";
import {Button} from "@/components/Button";
import {FieldInfo} from "@/components/newform/FieldInfo";
import {isEmpty, map} from "lodash";
import {useAnimatedKeyboardAvoidingBottomPadding} from "@/hooks/useAnimatedKeyboardAvoidingBottomPadding";
import Animated from "react-native-reanimated";
import {OrientedSheet} from "@/components/OrientedSheet";
import {ButtonRow} from "@/components/ButtonRow";
import {BooleanRequirementDto} from "../../../../api/src/checklists/checklistTemplate/checklistTemplateDtos";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {FormRadioGroup} from "@/components/newform/FormRadioGroup";
import {z} from "zod";
import {Label} from "@/components/newform/Label";
import {genChecklistRequirementId} from "../../../../api/src/checklist.schemas";
import {ChecklistRequirementFormRow} from "@/components/checklists2/ChecklistRequirementFormRow";
import {FormSwitch} from "@/components/newform/FormSwitch";
import {Divider} from "@/components/Divider";

export interface BooleanInputBottomSheetProps {
  isOpen: boolean;
  onChange: (value: BooleanRequirementDto) => void;
  onClose: () => void;
  initialValue: BooleanRequirementDto
}

const yesNoOptions = [{
  label: "Yes",
  value: true
}, {
  label: "No",
  value: false
}];


export const BooleanInputBottomSheet: React.FC<BooleanInputBottomSheetProps> = ({
                                                                                  isOpen, initialValue,
                                                                                  onChange,
                                                                                  onClose,
                                                                                }) => {

  const form = useForm({
    defaultValues: initialValue,
    onSubmit: ({value}) => {
      onChange(value);
      onClose();
    },
    validatorAdapter: zodValidator(),
  });

  const buttonContainerAnimatedStyle = useAnimatedKeyboardAvoidingBottomPadding();

  return (
    <OrientedSheet isOpen={isOpen} onClose={onClose} portraitHeightPercentage={0.9}>
      <View style={cn("flex flex-row gap-2 border-b border-gray-200 pb-3 px-4 justify-between items-center")}>
        <Text size={"xl"} bold>
          Provide Yes/No Input
        </Text>
        <Button colorScheme={"link"} onPress={onClose} size={"sm"}>
          Back
        </Button>
      </View>

      <ScrollView contentContainerStyle={cn("pt-4 pb-6 px-4")}>
        <form.Field name={"conditionals"}
                    mode={"array"}
                    children={field => <View>
                      {isEmpty(field.state.value) ? <>
                          <Text style={cn("mb-1")}>
                            Requirement
                          </Text>
                          <View style={cn("bg-gray-100 px-4 py-2 rounded-lg mb-2")}>
                            <Text>
                              Yes/No
                            </Text>
                          </View>
                        </>
                        : null}

                      {map(field.state.value, (cond, condIdx) => {
                        return <View key={condIdx} style={cn("mb-6 border-b border-gray-300 pb-6")}>
                          <Text style={cn("mb-1")}>
                            Requirement
                          </Text>
                          <View style={cn("flex flex-row items-center gap-3 mb-3")}>
                            <View style={cn("bg-gray-100 px-4 py-2 rounded-lg grow")}>
                              <Text>
                                Yes/No
                              </Text>
                            </View>
                            <Button variant={"outline"} colorScheme={"gray"} size={"sm"}
                                    onPress={() => {
                                      field.removeValue(condIdx)
                                    }}>
                              Remove
                            </Button>
                          </View>

                          <form.Field name={`conditionals[${condIdx}].if`}
                                      validators={{
                                        onSubmit: z.boolean()
                                      }}
                                      children={field => {
                                        return <View>
                                          <Label>
                                            If
                                          </Label>
                                          <View>
                                            <FormRadioGroup options={yesNoOptions} className={"mb-1"}
                                                            field={field}/>
                                          </View>
                                          <FieldInfo field={field}/>
                                        </View>;
                                      }}/>

                          <form.Field name={`conditionals[${condIdx}].thenText`}
                                      validators={{
                                        onSubmit: z.string().optional()
                                      }}
                                      children={field => {
                                        return <View>
                                          <Label>
                                            Then
                                          </Label>
                                          <View>
                                            <FormTextarea field={field} placeholder={"Enter text..."}/>
                                          </View>
                                          <FieldInfo field={field}/>
                                        </View>;
                                      }}/>

                          <form.Field name={`conditionals[${condIdx}].thenRequirements`}
                                      mode={"array"}
                                      children={thenReqArrayField => {
                                        return <View>
                                          <Label className={"mb-1"}>
                                            And require
                                          </Label>

                                          <View style={cn("mb-2")}>
                                            {map(thenReqArrayField.state.value, (req, reqIdx) => {
                                              return <ChecklistRequirementFormRow key={req.id}
                                                isRequired={req.isRequired}
                                                onRequiredChange={isRequired => {
                                                  thenReqArrayField.replaceValue(reqIdx, {
                                                    ...req,
                                                    isRequired
                                                  })
                                                }}
                                                requirement={req}
                                                onRemove={() => {
                                                  thenReqArrayField.removeValue(reqIdx);
                                                }}/>
                                            })}
                                          </View>

                                          <View style={cn('flex flex-row gap-2')}>
                                            <Button variant={"outline"}
                                                    onPress={() => {
                                                      const currentCond = field.state.value[condIdx];
                                                      field.replaceValue(condIdx, {
                                                        ...currentCond,
                                                        thenRequirements: [...currentCond.thenRequirements, {
                                                          id: genChecklistRequirementId(),
                                                          type: "addImage",
                                                          isRequired: true
                                                        }]
                                                      });
                                                    }}
                                                    leftIcon={s => <Ionicons name={"add"} size={16} styles={s}/>}
                                                    colorScheme={"gray"} size={"sm"}>
                                              Image
                                            </Button>
                                            <Button variant={"outline"}
                                                    onPress={() => {
                                                      const currentCond = field.state.value[condIdx];
                                                      field.replaceValue(condIdx, {
                                                        ...currentCond,
                                                        thenRequirements: [...currentCond.thenRequirements, {
                                                          id: genChecklistRequirementId(),
                                                          type: "writeComment",
                                                          isRequired: true
                                                        }]
                                                      });
                                                    }}
                                                    leftIcon={s => <Ionicons name={"add"} size={16} styles={s}/>}
                                                    colorScheme={"gray"} size={"sm"}>
                                              Comment
                                            </Button>
                                            <Button variant={"outline"}
                                                    onPress={() => {
                                                      const currentCond = field.state.value[condIdx];
                                                      field.replaceValue(condIdx, {
                                                        ...currentCond,
                                                        thenRequirements: [...currentCond.thenRequirements, {
                                                          id: genChecklistRequirementId(),
                                                          type: "inputNumber",
                                                          isRequired: true,
                                                          numberType: "number"
                                                        }]
                                                      });
                                                    }}
                                                    leftIcon={s => <Ionicons name={"add"} size={16} styles={s}/>}
                                                    colorScheme={"gray"} size={"sm"}>
                                              Number Input
                                            </Button>
                                          </View>
                                        </View>
                                      }}/>


                          <form.Field name={`conditionals[${condIdx}].isNonCompliant`}
                                      validators={{
                                        onSubmit: z.boolean()
                                      }}
                                      children={field => {
                                        return <View style={cn("mt-6")}>
                                          <View style={cn("flex flex-row items-center justify-between gap-3 mb-2")}>
                                            <Label>
                                              Compliance Warning
                                            </Label>
                                            <View>
                                              <FormSwitch field={field}/>
                                            </View>
                                          </View>
                                          <Text>Receive a notification if this item is out of compliance.  </Text>
                                          <FieldInfo field={field}/>
                                        </View>;
                                      }}/>

                        </View>
                      })}

                      <Button leftIcon={() => <Ionicons name={"add"} size={20} color={colors.gray[500]}/>}
                              variant={"outline"} colorScheme={"gray"} size={"sm"}
                              onPress={() => {
                                field.pushValue({
                                  if: true,
                                  thenText: "",
                                  isNonCompliant: false,
                                  thenRequirements: []
                                })
                              }}>
                        Add Conditional
                      </Button>
                    </View>}/>
      </ScrollView>

      <Animated.View
        style={[cn("px-4 pt-3 border-t border-gray-300 flex flex-row items-center gap-2"), buttonContainerAnimatedStyle]}>
        <ButtonRow>
          <Button onPress={onClose}
                  colorScheme={"gray"} variant={"outline"}>
            Cancel
          </Button>
          <Button onPress={form.handleSubmit}
                  colorScheme={"primary"}>
            Save
          </Button>
        </ButtonRow>
      </Animated.View>
    </OrientedSheet>
  );
};
