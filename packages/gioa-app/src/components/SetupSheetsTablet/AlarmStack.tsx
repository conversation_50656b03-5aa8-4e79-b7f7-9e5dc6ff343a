import React from 'react';
import {Pressable, View} from 'react-native';
import {cn} from "@/style.util";
import {PersonDto} from "../../../../api/src/schemas";
import {AlarmCardInner} from "@/components/SetupSheetsTablet/AlarmCardInner";
import {AlarmPip} from "@/components/SetupSheetsTablet/AlarmPip";
import {SetupDayAlarm} from "@/hooks/calculateAlarms";

export interface AlarmStackProps {
  alarms: SetupDayAlarm[];
  people: Record<string, PersonDto>;
  onTap: (topAlarm: SetupDayAlarm, stackCount: number) => void;
  timezone: string;
}

export const AlarmStack: React.FC<AlarmStackProps> = ({alarms, people, onTap, timezone}) => {
  const topAlarm = alarms[0];
  const stackCount = alarms.length;

  return <Pressable onPress={() => onTap(topAlarm, stackCount)}>
    <View style={cn("relative")}>
      {/* Stack background cards */}
      {stackCount > 2 && <View style={[
          cn("absolute bg-gray-100 border border-red-300 rounded-2xl"),
          {
            bottom: -16,
            left: 16,
            right: 16,
            height: 40,
          }
        ]}/>}
      {stackCount > 1 && <View style={[
          cn("absolute bg-gray-50 border border-red-300 rounded-2xl"),
          {
            bottom: -8,
            left: 8,
            right: 8,
            height: 40,
          }
        ]}/>}

      {/* Top notification card */}
      <AlarmCardInner alarm={topAlarm} people={people} timezone={timezone}
                      RightElem={<AlarmPip label={stackCount.toString()}/>}
                      style={cn("z-10 rounded-2xl border-red-300 border")}/>
    </View>
  </Pressable>;
};
