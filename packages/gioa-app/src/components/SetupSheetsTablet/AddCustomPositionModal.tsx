import React, {useCallback} from 'react';
import {View} from 'react-native';
import {cn} from "@/style.util";
import {OrientedSheet} from "@/components/OrientedSheet";
import {Text} from "@/components/Text";
import {Button} from "@/components/Button";
import {useForm} from "@tanstack/react-form";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {z} from "zod";
import {FormInput} from "@/components/newform/FormInput";
import {Label} from "@/components/newform/Label";
import {FieldInfo} from "@/components/newform/FieldInfo";
import {NumberStepper} from "@/components/SetupSheetsTablet/NumberStepper";
import {useAnimatedKeyboardAvoidingBottomPadding} from "@/hooks/useAnimatedKeyboardAvoidingBottomPadding";
import Animated from "react-native-reanimated";

export interface AddCustomPositionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (title: string, count: number) => void;
}

const addCustomPositionSchema = z.object({
  title: z.string().min(1, "Position title is required").max(512, "Position title is too long"),
  count: z.number().int().min(1, "Count must be at least 1").max(10, "Count cannot exceed 10")
});

export const AddCustomPositionModal: React.FC<AddCustomPositionModalProps> = ({
                                                                                isOpen,
                                                                                onClose,
                                                                                onSave
                                                                              }) => {
  const form = useForm({
    defaultValues: {
      title: "",
      count: 1
    },
    onSubmit: async ({value}) => {
      onSave(value.title.trim(), value.count);
      form.reset();
      onClose();
    },
    validatorAdapter: zodValidator(),
    validators: {
      onSubmit: addCustomPositionSchema
    }
  });

  const handleCancel = useCallback(() => {
    form.reset();
    onClose();
  }, [form, onClose]);

  const bottomPadding = useAnimatedKeyboardAvoidingBottomPadding();

  return (
    <OrientedSheet isOpen={isOpen} onClose={handleCancel} portraitHeightPercentage={0.6}>
      <View style={cn("flex-1 px-6")}>
        <View style={cn("flex flex-row gap-2 border-b border-gray-200 pb-4 mb-6 justify-between items-center")}>
          <Text size={"xl"} bold>
            Add Custom Position
          </Text>
          <Button colorScheme={"link"} onPress={handleCancel} size={"sm"}>
            Cancel
          </Button>
        </View>

        <View style={cn("flex-1 gap-6")}>
          <form.Field name="title">
            {field => (
              <View>
                <Label>Position Title</Label>
                <FormInput
                  field={field}
                  placeholder="Enter position name..."
                  autoFocus
                />
                <FieldInfo field={field}/>
              </View>
            )}
          </form.Field>

          <form.Field name="count">
            {field => (
              <View>
                <Label>Count</Label>
                <View style={cn("py-2")}>
                  <NumberStepper
                    value={field.state.value}
                    onChange={field.handleChange}
                    min={1}
                    max={10}
                  />
                </View>
                <FieldInfo field={field}/>
              </View>
            )}
          </form.Field>
        </View>

        <Animated.View style={[cn("pt-4 border-t border-gray-200"), bottomPadding]}>
          <Button colorScheme={"primary"}
                  onPress={form.handleSubmit}>
            {`Add Position${form.state.values.count > 1 ? ` (${form.state.values.count})` : ""}`}
          </Button>
        </Animated.View>
      </View>
    </OrientedSheet>
  );
};
