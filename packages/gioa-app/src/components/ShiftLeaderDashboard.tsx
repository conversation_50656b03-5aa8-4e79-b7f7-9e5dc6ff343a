import React, {forwardRef, useCallback, useImperative<PERSON><PERSON><PERSON>, useMemo, useState} from 'react';
import {Pressable, View} from "react-native";
import {api} from "@/api";
import {DateTime} from "luxon";
import {IsoWeekDateWithDay} from "../../../api/src/timeSchemas";
import {chunk, filter, isEmpty, map, take} from 'lodash';
import {Text} from "@/components/Text";
import {cn} from "@/style.util";
import {ShiftLeaderTeamMemberActionsBottomSheet} from "@/components/ShiftLeaderTeamMemberActionsBottomSheet";
import {ChecklistIconSvg} from "@/components/icons/ChecklistIcon";
import {LinearGradient} from "expo-linear-gradient";
import {PersonDto} from "../../../api/src/schemas";
import {Link, router} from "expo-router";
import {colors} from "@/styles";
import {AdminShiftOfferRecordRow} from "@/components/AdminShiftOfferRecordRow";
import {Button} from "@/components/Button";
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';
import {DashboardEventsAndScheduleModule} from "@/components/DashboardEventsAndScheduleModule";
import {ShiftLeaderFeedbackBottomSheet} from "@/components/ShiftLeaderFeedbackBottomSheet";
import {getDayTeam} from "@/shiftLead.util";
import {TeamCarousel} from "@/components/TeamCarousel";
import {ShiftLeaderPersonDto} from "../../../api/src/shiftLead.schemas";
import {DashboardModule} from "@/components/DashboardModule";
import {goToShiftLeadNotes, goToShiftOfferDetails, goToShiftSwapDetails} from "@/navigationUtil";
import {ShiftSwapRecordRow} from "@/components/ShiftSwapRecordRow";

export interface ShiftLeaderDashboardProps {
  storeId: string;
  activeDate: DateTime;
}

export interface ShiftLeaderDashboardRef {
  refetch: () => Promise<void>;
}

export const ShiftLeaderDashboard = forwardRef<ShiftLeaderDashboardRef, ShiftLeaderDashboardProps>(({
                                                                                                      storeId,
                                                                                                      activeDate
                                                                                                    }, ref) => {
  const isoWeekDisplayed = useMemo((): IsoWeekDateWithDay => {
    const startOfIsoWeek = activeDate
    return {
      week: startOfIsoWeek.weekNumber,
      year: startOfIsoWeek.weekYear,
      day: startOfIsoWeek.weekday
    }
  }, [activeDate]);
  const [personActionsPerson, setPersonActionsPerson] = useState<PersonDto>();

  const [feedbackOpen, setFeedbackOpen] = useState(false);

  const [store] = api.user.getStoreAdmin.useSuspenseQuery({
    storeId: storeId
  }, {
    staleTime: 1000 * 60 * 60
  });
  const timezone = store.timezone;

  const [[
    {
      schedule,
      people,
      scheduleEvents,
      scheduleAnnouncements,
      canViewSchedules,
      canCreateNotes,
      canGiveFeedback,
      canGiveScores
    },
    {items: shiftOffers},
    {items: shiftSwaps},
    {items: checklists},
    {feedback}
  ], [schedQuery, shiftOffersQuery, checklistsQuery, feedbackQuery]] = api.useSuspenseQueries(t => [
    t.shiftLead.getShiftLeadDashboard({
      storeId: storeId,
      week: isoWeekDisplayed,
    }, {
      staleTime: 1000 * 60 * 15, // 15 minutes
      refetchInterval: 1000 * 60 * 15 // 15 minutes
    }),
    t.user.getShiftOffersAdvanced({
      storeId,
      status: "pending",
      dateRange: {
        start: activeDate.startOf("day").toJSDate(),
        end: activeDate.endOf("day").toJSDate(),
      }
    }),
    t.shiftSwap.getShiftSwaps({
      storeId,
      status: ["pending", "accepted"],
      dateRange: {
        start: activeDate.startOf("day").toJSDate(),
        end: activeDate.endOf("day").toJSDate(),
      }
    }),
    t.checklist.getChecklistEventsForShiftLeader({
      storeId: storeId,
      range: {
        start: activeDate.setZone(timezone).startOf("day").toJSDate(),
        end: activeDate.setZone(timezone).endOf("day").toJSDate(),
      }
    }),
    t.shiftLead.getShiftLeadFeedback({
      storeId: storeId,
      week: isoWeekDisplayed,
    })
  ]);

  const firstShiftOffers = take(shiftOffers, 2);
  const firstShiftSwaps = take(shiftSwaps, 2);
  const dayPeopleScheduled = useMemo(() => {
    const peopleScheduled = getDayTeam({
      schedule: schedule,
      timezone: timezone,
      activeDate: activeDate,
      people: people
    })

    return chunk(peopleScheduled, 5);
  }, [schedule, timezone, people, activeDate]);

  const weekday = activeDate.toLocaleString({weekday: "long"});

  useImperativeHandle(ref, () => ({
    refetch: async () => {
      await Promise.all([
        schedQuery.refetch(),
        shiftOffersQuery.refetch(),
        checklistsQuery.refetch(),
        feedbackQuery.refetch()
      ]);
    }
  }), [schedQuery, shiftOffersQuery, checklistsQuery]);

  const viewAllShiftOffers = () => {
    router.push({
      pathname: "/(signedin)/schedule/[storeId]/requests/shift-offer",
      params: {
        storeId: storeId
      }
    })
  }

  const viewAllShiftSwaps = () => {
    router.push({
      pathname: "/(signedin)/schedule/[storeId]/requests/shift-swap",
      params: {
        storeId: storeId
      }
    })
  }

  const viewChecklists = () => {
    router.push({
      pathname: "/(signedin)/shift-lead/(shift-lead-drawer)/[storeId]/(tabs)/checklists",
      params: {
        storeId: storeId
      }
    })
  }

  const startOfDay = activeDate.startOf("day").toJSDate();
  const endOfDay = activeDate.endOf("day").toJSDate();
  const eventsForToday = filter(scheduleEvents, evt => {
    return (evt.range.start >= startOfDay && evt.range.start <= endOfDay)
            || (evt.range.end >= startOfDay && evt.range.end <= endOfDay)
            || (evt.range.start <= startOfDay && evt.range.end >= endOfDay);
  });
  const announcementsForToday = filter(scheduleAnnouncements, evt => {
    return (evt.range.start >= startOfDay && evt.range.start <= endOfDay)
            || (evt.range.end >= startOfDay && evt.range.end <= endOfDay)
            || (evt.range.start <= startOfDay && evt.range.end >= endOfDay);
  });

  const hasEvents = !isEmpty(eventsForToday);
  const hasAnnouncements = !isEmpty(announcementsForToday);
  const getNotes = api.user.getScheduleNotes.useQuery({scheduleId: schedule?.id!},
          {
            select: notes => filter(notes, n => n.dayOfWeek === activeDate.weekday),
            enabled: Boolean(schedule?.id),
          }
  );

  const onOpenNotes = () => {
    if (!schedule) return;

    goToShiftLeadNotes({
      storeId: storeId,
      scheduleId: schedule?.id,
      activeDate: activeDate
    })
  }

  const goToScores = () => {
    router.push({
      pathname: "/(signedin)/shift-lead/[storeId]/scores",
      params: {
        storeId: storeId,
        activeDateStr: activeDate.toFormat("yyyy-MM-dd")
      }
    })
  }

  const onPersonPress = useCallback((person: ShiftLeaderPersonDto) => {
    setPersonActionsPerson(person)
  }, []);

  const numNotes = getNotes.data?.length ?? 0;
  return (
          <>
            <View>
              <View style={cn("flex flex-row justify-between mx-4 items-baseline mb-3")}>
                <Text bold size={"lg"}>
                  {weekday}'s Team
                </Text>
                <Link href={{
                  pathname: "/(signedin)/shift-lead/(shift-lead-drawer)/[storeId]/(tabs)/setup-sheets",
                  params: {
                    storeId: storeId!
                  }
                }} asChild style={cn("flex flex-row items-center gap-1")}>
                </Link>
              </View>

              <View>
                {!canViewSchedules ? <View style={cn("px-4")}>
                  <Text>
                    You do not have permission to view the schedule for this day, since you're not a shift leader on
                    this day.
                  </Text>
                </View> : !schedule ? <View style={cn("px-4")}>
                  <Text>
                    There is no schedule published for this day yet.
                    Team members will appear here when they are scheduled for this day.
                  </Text>
                </View> : schedule && isEmpty(dayPeopleScheduled) ? <View style={cn("px-4")}>
                  <Text>
                    There are no team members scheduled for this day.
                    Team members will appear here when they are scheduled for this day.
                  </Text>
                </View> : null}

                <TeamCarousel team={dayPeopleScheduled}
                              onPersonPress={onPersonPress}/>
              </View>
            </View>

            {!isEmpty(checklists) ?
                    <Pressable onPress={viewChecklists}>
                      <LinearGradient style={cn("px-4 py-4 rounded-xl mx-4 flex flex-row items-center gap-3")}
                                      colors={["#456FE8", "#19B0EC"]}>
                        <ChecklistIconSvg/>
                        <Text colorScheme={"light"} bold size={"lg"} style={cn("grow")}>
                          {weekday}'s Checklists
                        </Text>
                        <View style={cn("rounded-full bg-white w-6 h-6 flex items-center justify-center")}>
                          <Text size={"sm"}>
                            {checklists.length}
                          </Text>
                        </View>
                      </LinearGradient>
                    </Pressable> : null}

            {!isEmpty(firstShiftOffers) ?
                    <DashboardModule>
                      <Text bold center size={"lg"} style={cn("mb-3")}>
                        Pending Shift Offers
                      </Text>

                      <View style={cn("flex gap-3")}>
                        {map(firstShiftOffers, offer => {
                          return <AdminShiftOfferRecordRow key={offer.id} shiftOffer={offer} showDate={false}
                                                           onPressShiftOffer={() => goToShiftOfferDetails({storeId, shiftOfferId: offer.id})}
                                                           timezone={store.timezone ?? null}/>
                        })}

                        {shiftOffers.length > firstShiftOffers.length ?
                                <Button onPressSlow={viewAllShiftOffers} colorScheme={"gray"} variant={"outline"}>
                                  {`View all (${shiftOffers.length})`}
                                </Button> : null}
                      </View>
                    </DashboardModule> : null}

            {!isEmpty(firstShiftSwaps) ?
                    <DashboardModule>
                      <Text bold center size={"lg"} style={cn("mb-3")}>
                        Pending Swaps
                      </Text>

                      <View style={cn("flex gap-3")}>
                        {map(firstShiftSwaps, swap => {
                          return <ShiftSwapRecordRow key={swap.id} shiftSwap={swap}
                                                     timezone={timezone} showAction={false}
                                                     onPressShiftSwap={() => goToShiftSwapDetails({storeId, shiftSwapId: swap.id})}/>
                        })}

                        {shiftSwaps.length > firstShiftSwaps.length ?
                                <Button onPressSlow={viewAllShiftSwaps} colorScheme={"gray"} variant={"outline"}>
                                  {`View all (${shiftSwaps.length})`}
                                </Button> : null}
                      </View>
                    </DashboardModule> : null}

            <View style={cn("flex flex-row gap-3 items-stretch mx-4")}>
              {canCreateNotes ?
                      <Pressable onPress={onOpenNotes}
                                 style={cn("bg-white rounded-xl px-2 py-4 shadow-md flex-1 flex items-center justify-center gap-2")}>
                        <MaterialCommunityIcons name="note-plus-outline" size={36} color={colors.gioaBlue}/>
                        <Text>
                          Notes {numNotes > 0 ? `(${numNotes})` : ""}
                        </Text>
                      </Pressable> : null}
              {canGiveFeedback && schedule ?
                      <Pressable onPress={() => setFeedbackOpen(true)}
                                 style={cn("bg-white rounded-xl px-2 py-4 shadow-md flex-1 flex items-center justify-center gap-2")}>
                        {feedback ?
                                <MaterialCommunityIcons name={"comment-check-outline"} size={36}
                                                        color={colors.green[600]}/> :
                                <MaterialCommunityIcons name="comment-alert-outline" size={36}
                                                        color={colors.gioaBlue}/>}
                        <Text muted={Boolean(feedback)}>
                          Feedback
                        </Text>
                      </Pressable> : null}
              {canGiveScores ?
                      <Pressable onPress={goToScores}
                                 style={cn("bg-white rounded-xl px-2 py-4 shadow-md flex-1 flex items-center justify-center gap-2")}>
                        <FontAwesome5 name="star" size={36} color={colors.gioaBlue}/>
                        <Text>
                          Scores
                        </Text>
                      </Pressable> : null}
            </View>

            {(canViewSchedules && schedule) || hasEvents || hasAnnouncements ?
                    <DashboardEventsAndScheduleModule timezone={timezone} store={store} storeId={storeId}
                                                      todayLabel={weekday}
                                                      events={eventsForToday} className={"mx-4"}
                                                      announcements={announcementsForToday}
                                                      hasSchedule={canViewSchedules && Boolean(schedule)}
                                                      activeDate={activeDate}/> : null}

            {personActionsPerson ?
                    <ShiftLeaderTeamMemberActionsBottomSheet
                            isOpen={Boolean(personActionsPerson)}
                            onClose={() => setPersonActionsPerson(undefined)}
                            person={personActionsPerson}
                            storeId={storeId}/> : null}


            {feedbackOpen && schedule
                    ? <ShiftLeaderFeedbackBottomSheet
                            isOpen={feedbackOpen}
                            onClose={() => setFeedbackOpen(false)}
                            scheduleId={schedule.id}
                            feedback={feedback}
                            activeDate={activeDate}
                            storeId={storeId}/>
                    : null}

          </>
  );
});

ShiftLeaderDashboard.displayName = 'ShiftLeaderDashboard';
