import React from "react";
import {Pressable, View} from "react-native";
import {Text} from "@/components/Text";
import {cn} from "@/style.util";
import {colors} from "@/styles";
import {filter, map, reduce} from "lodash";
import {AvailabilityDay} from "@/components/AvailabilityDay";
import Ionicons from "@expo/vector-icons/Ionicons";
import {Divider} from "@/components/Divider";
import {DraftPersonAvailability, PersonAvailabilityDto,} from "../../../api/src/availabilitySchemas";
import {getDurationHours} from "../../../api/src/timeSchemas.util";

type DayOfWeek = {
  name: string;
  abbr: string;
  dayOfWeek: number;
  isDisabled: boolean;
};

const daysOfWeek: DayOfWeek[] = [
  {name: "Sunday", abbr: "Sun", dayOfWeek: 7, isDisabled: true},
  {name: "Monday", abbr: "Mon", dayOfWeek: 1, isDisabled: false},
  {name: "Tuesday", abbr: "Tue", dayOfWeek: 2, isDisabled: false},
  {name: "Wednesday", abbr: "Wed", dayOfWeek: 3, isDisabled: false},
  {name: "Thursday", abbr: "Thu", dayOfWeek: 4, isDisabled: false},
  {name: "Friday", abbr: "Fri", dayOfWeek: 5, isDisabled: false},
  {name: "Saturday", abbr: "Sat", dayOfWeek: 6, isDisabled: false},
];

interface AvailabilityWeekEditProps {
  firstColumn?: PersonAvailabilityDto | DraftPersonAvailability;
  secondColumn?: DraftPersonAvailability;
  columnTitles: string[];
  onPressDay?: (dayOfWeek: number) => void;
}

export function AvailabilityWeekEdit({
                                         firstColumn,
                                         secondColumn,
                                         columnTitles,
                                         onPressDay,
                                       }: AvailabilityWeekEditProps) {
  const showSecondColumn = secondColumn !== undefined;

  const colDimensions = [
    ...(!showSecondColumn ? [10] : [15]),
    ...(showSecondColumn ? [42.5] : [42.5]),
    ...(showSecondColumn ? [42.5] : [42.5]),
  ];

  const getTotalHours = (data?: PersonAvailabilityDto) =>
    data ? Math.round(reduce(data.ranges, (acc, range) => acc + getDurationHours(range), 0) * 10) / 10 : null;

  const totalApprovedHours = getTotalHours(firstColumn as PersonAvailabilityDto);
  const totalSecondColumnHours = getTotalHours(secondColumn as PersonAvailabilityDto);

  return (
    <View style={cn("gap-2")}>
      <View style={[cn("min-h-10 flex-1 flex-row gap-2")]}>
        <View style={[cn(""), {flex: colDimensions[0]}]}>
          <View style={cn("border-1 border-gray-200 rounded-lg p-1 h-full flex-col items-center justify-center")}>
            <Ionicons name="calendar-outline" size={18} color={colors.gray[500]}/>
          </View>
        </View>
        <View style={[cn("border-1 border-gray-200 justify-center rounded-lg p-1 h-full flex-col"), {flex: 42.5}]}>
          <Text size="sm" center>{columnTitles[0] ?? "Current"}</Text>
        </View>
        {showSecondColumn && (
          <View style={[cn("border-1 border-gray-200 justify-center rounded-lg p-1 h-full flex-col"), {flex: 42.5}]}>
            <Text size="sm" center>{columnTitles[1] ?? "Pending"}</Text>
          </View>
        )}
      </View>

      {map(filter(daysOfWeek, d => !d.isDisabled), (day) => (
        <Pressable key={day.dayOfWeek} style={[cn("min-h-12 flex-1 flex-row gap-2")]}
                   onPress={() => onPressDay?.(day.dayOfWeek)}>
          <View style={[cn(""), {flex: colDimensions[0]}]}>
            <View style={cn("border-1 border-gray-200 rounded-lg p-1 h-full flex-col items-center justify-center")}>
              <Text center size="sm">{day.abbr}</Text>
            </View>
          </View>
          <View style={[cn(""), {flex: colDimensions[2]}]}>
            <AvailabilityDay ranges={filter(firstColumn?.ranges, r => r.dayOfWeek === day.dayOfWeek)}/>
          </View>
          {showSecondColumn && (
            <View style={[cn(""), {flex: colDimensions[2]}]}>
              <AvailabilityDay ranges={filter(secondColumn?.ranges, r => r.dayOfWeek === day.dayOfWeek)}/>
            </View>
          )}
        </Pressable>
      ))}

      <View style={[cn("min-h-10 flex-1 flex-row gap-2")]}>
        <View style={[cn(""), {flex: colDimensions[0]}]}>
          <View style={cn("border-1 border-gray-200 rounded-lg p-1 h-full flex-col items-center justify-center")}>
            <Text center size="sm">Total Hrs</Text>
          </View>
        </View>
          <View style={[cn("border-1 border-gray-200 rounded-lg p-1 h-full flex-col"), {flex: 42.5}]}>
            <View style={cn("p-2")}>
              <View style={cn("flex-row justify-between items-center")}>
                <Text size="sm">Available</Text>
                <Text size="sm">{totalApprovedHours}h</Text>
              </View>
              <Divider size="sm"/>
              <View style={cn("flex-row justify-between items-center")}>
                <Text size="sm">Preferred</Text>
                <Text size="sm">{firstColumn?.maxHoursPreferred ? `${firstColumn.maxHoursPreferred}h` : "--"}</Text>
              </View>
            </View>
          </View>
        {showSecondColumn && (
          <View style={[cn("border-1 border-gray-200 rounded-lg p-1 h-full flex-col"), {flex: 42.5}]}>
            <View style={cn("p-2")}>
              <View style={cn("flex-row justify-between items-center")}>
                <Text size="sm">Available</Text>
                <Text size="sm">{totalSecondColumnHours}h</Text>
              </View>
              <Divider size="sm"/>
              <View style={cn("flex-row justify-between items-center")}>
                <Text size="sm">Preferred</Text>
                <Text
                  size="sm">{secondColumn?.maxHoursPreferred ? `${secondColumn.maxHoursPreferred}h` : "--"}</Text>
              </View>
            </View>
          </View>
        )}
      </View>
    </View>
  );
}