import {ActivityIndicator, Pressable, View} from "react-native";
import {cn} from "@/style.util";
import {colors} from "@/styles";
import {Text} from "@/components/Text";
import {t} from "@lingui/macro";
import * as React from "react";
import {useLingui} from "@lingui/react";
import {loadCatalog} from "@/i18n";
import {Ionicons} from "@expo/vector-icons";
import {api} from "@/api";

export const LanguageChangeButton = () => {
  const {i18n} = useLingui();

  const [{person}, getUserQuery] = api.user.getUserProfile.useSuspenseQuery(undefined, {
    staleTime: 1000 * 60 * 15 // 15 minutes
  });

  const apiUtil = api.useUtils();
  const updatePreferredLanguage = api.user.updatePreferredLanguage.useMutation({
    onSuccess: () => {
      apiUtil.user.getUserProfile.invalidate();
    },
  });

  React.useEffect(() => {
    const personLang = person?.preferredLanguage;
    if (personLang && personLang !== i18n.locale) {
      loadCatalog(personLang);
    }
  }, [person?.preferredLanguage, i18n.locale]);

  const changeLanguage = (lang: "en" | "es") => {
    updatePreferredLanguage.mutate({
      preferredLanguage: lang
    })
  }

  const isLoading = updatePreferredLanguage.isPending || getUserQuery.isPending;

  return <View>
    <Pressable style={cn("flex flex-row items-center")}
               onPress={() => changeLanguage(i18n.locale === "en" ? "es" : "en")}>
      <View style={{marginRight: 12}}>
        {isLoading
          ? <View>
            <ActivityIndicator size={"small"} color={colors.gray[600]}/>
          </View>
          : <Ionicons name={"globe-outline"} size={20} color={colors.gray[600]}/>}
      </View>
      <Text size={"sm"} style={{color: colors.gray[600]}}>
        {i18n.locale === "en" ? t`English` : "Español"}
      </Text>
    </Pressable>
  </View>
}
