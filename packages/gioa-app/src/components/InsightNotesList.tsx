import React, {useCallback, useMemo, useState} from 'react';
import {AmazingListEmptyComponent} from "@/components/AmazingListEmptyComponent";
import {FlatList, Pressable, RefreshControl, View} from "react-native";
import {cn} from "@/style.util";
import {api} from "@/api";
import {filter, flatMap, groupBy, map, orderBy} from "lodash";
import {colors} from "@/styles";
import {DateTime} from "luxon";
import {Button} from "@/components/Button";
import {ScheduleNoteDto} from "../../../api/src/note.schemas";
import {Text} from "@/components/Text";
import {Divider} from "@/components/Divider";
import {goToShiftAndSchedulingNotes} from "@/navigationUtil";
import {DateTimeRange} from "../../../api/src/timeSchemas";
import {useNavigationPadding} from "@/hooks/useNavigationPadding";

export interface InsightNotesListProps {
  storeId: string;
  range: DateTimeRange
}

const ListEmptyComponent = () => <AmazingListEmptyComponent
  title={"No notes found"}
  subtitle={"Notes matching your filters will appear here. "}/>;

interface NoteListItem {
  date: string;
  items: ScheduleNoteDto[];
}

export const InsightNotesList: React.FC<InsightNotesListProps> = ({storeId, range}) => {
  const queryInput = {
    storeId: storeId,
    range: range
  };
  const [notes, query] = api.insights.findShiftLeadAndScheduleNotes.useSuspenseInfiniteQuery(queryInput, {
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    refetchInterval: 1000 * 60 * 15 // 15 minutes
  });
  const [store] = api.user.getStore.useSuspenseQuery({storeId}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const allPositions = useMemo(() => flatMap(store.storeAreas, a => a.positions), [store]);
  const timezone = store.timezone;

  const renderItem = useCallback(({item: {date, items}}: { item: NoteListItem }) => {
    // const shiftNotes = filter(items, note => note.noteType === "shiftLeadNotes");
    const scheduleNotes = filter(items, note => note.noteType === "scheduleComments");
    const dt = DateTime.fromFormat(date, "yyyy-MM-dd", {zone: timezone});

    return <Pressable onPress={() => {
      goToShiftAndSchedulingNotes({
        storeId,
        activeDate: dt
      })
    }}
                      style={cn("mb-2 py-3 rounded-2xl bg-white px-4")}>
      <Text semibold>
        {dt.toLocaleString({
          weekday: "long",
          month: "short",
          day: "numeric",
        })}
      </Text>
      <Divider size={"sm"}/>
      <View style={cn("flex flex-row gap-2 items-center")}>
        <Text size={"sm"}>
          {scheduleNotes.length} Schedule Notes
        </Text>
      </View>
    </Pressable>
  }, [storeId, store, timezone, allPositions]);

  const keyExtractor = useCallback((item: NoteListItem) => item.date, []);

  const apiUtil = api.useUtils();
  const [refreshing, setRefreshing] = useState(false);
  const onRefresh = useCallback(() => {
    setRefreshing(true);

    // reset the infinite query to the first page
    apiUtil.insights.findShiftLeadAndScheduleNotes.setInfiniteData(queryInput, oldData => {
      if (!oldData) return;

      return {
        ...oldData,
        pages: oldData.pages.slice(0, 1),
        pageParams: oldData.pageParams.slice(0, 1),
      }
    });

    query.refetch().then(() => {
      setRefreshing(false);
    });
  }, [queryInput]);

  const listData = useMemo((): NoteListItem[] => {
    const items = flatMap(notes.pages, page => page.items);
    const itemsInRange = filter(items, item => {
      const forDate = item.forAbsDate;
      return forDate >= range.start && forDate <= range.end;
    });
    const sortedItems = orderBy(itemsInRange, note => note.forAbsDate, "desc");
    const dateToItems = groupBy(sortedItems, note => {
      return DateTime.fromJSDate(note.forAbsDate, {zone: timezone}).toFormat("yyyy-MM-dd");
    })

    return map(dateToItems, (items, date) => {
      return {
        date,
        items: items
      }
    });
  }, [notes.pages, timezone, range]);

  const LoadMoreListFooterComponent = useMemo(() => <View style={cn("px-16 py-6")}>
    {query.hasNextPage ? <Button isLoading={query.isFetchingNextPage} colorScheme={"gray"}
                                 onPressSlow={() => query.fetchNextPage()}>
      Load More
    </Button> : null}
  </View>, [query]);

  const navigationPadding = useNavigationPadding();

  return (
    <FlatList data={listData} renderItem={renderItem}
              refreshControl={<RefreshControl tintColor={colors.gioaBlue}
                                              style={{zIndex: 2000}}
                                              refreshing={refreshing} onRefresh={onRefresh}/>}
              contentContainerStyle={[cn("pt-4 mx-4"), {paddingBottom: navigationPadding}]}
              ListEmptyComponent={ListEmptyComponent}
              ListFooterComponent={LoadMoreListFooterComponent}
              keyExtractor={keyExtractor}/>
  );
}
