import React from 'react';
import {OrientedSheet} from "@/components/OrientedSheet";
import {Text} from "@/components/Text";
import {cn} from "@/style.util";
import {ScrollView, View} from "react-native";
import {Button} from "@/components/Button";
import {DateTime} from "luxon";
import {includes, isEmpty, map} from "lodash";
import {CorrectiveActionStatus} from "../../../api/src/corrective-actions";
import {toggleInArray} from "@/types";
import {TogglePill} from "@/components/newform/TogglePill";
import {statusToLabel} from "@/components/CorrectiveActionStatusBadge";
import {FeedbackType} from "../../../api/src/insights.schemas";
import {DateRangeFilterControls} from "@/components/DateRangeFilterControls";

export interface AllFeedbackFilterBottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  filterAfter: DateTime | undefined;
  filterBefore: DateTime | undefined;
  onBeforeFilterChange: (before: DateTime | undefined) => void;
  onAfterFilterChange: (after: DateTime | undefined) => void;
  timezone: string;

  filterStatuses: CorrectiveActionStatus[] | undefined;
  onStatusFilterChange: (statuses: CorrectiveActionStatus[] | undefined) => void;

  filterFeedbackTypes: FeedbackType[] | undefined;
  onFeedbackTypeFilterChange: (feedbackTypes: FeedbackType[] | undefined) => void;
}

const statuses: CorrectiveActionStatus[] = [
  "inReview",
  "unacknowledged",
  "acknowledged",
  "reviewed",
]

const feedbackTypes: FeedbackType[] = [
  "actionableItem",
  "correctiveAction",
  "coaching",
  "positive-feedback",
  "general"
]

const feedbackTypeToLabel: Record<FeedbackType, string> = {
  actionableItem: "Actionable Item",
  correctiveAction: "Corrective Action",
  coaching: "Coaching",
  "positive-feedback": "Positive",
  general: "General"
}

export const AllFeedbackFilterBottomSheet: React.FC<AllFeedbackFilterBottomSheetProps> = ({
                                                                                            isOpen,
                                                                                            onClose,
                                                                                            filterAfter,
                                                                                            filterBefore,
                                                                                            filterStatuses,
                                                                                            onAfterFilterChange,
                                                                                            onBeforeFilterChange,
                                                                                            onStatusFilterChange,
                                                                                            filterFeedbackTypes,
                                                                                            onFeedbackTypeFilterChange,
                                                                                            timezone
                                                                                          }) => {

  const onToggleStatus = (status: string) => {
    if (status === "all") {
      onStatusFilterChange(undefined);
      return;
    }
    const newStatuses = toggleInArray(filterStatuses ?? [], status);
    onStatusFilterChange(newStatuses as CorrectiveActionStatus[]);
  }

  const onToggleFeedbackType = (feedbackType: string) => {
    if (feedbackType === "all") {
      onFeedbackTypeFilterChange(undefined);
      return;
    }
    const newFeedbackTypes = toggleInArray(filterFeedbackTypes ?? [], feedbackType);
    onFeedbackTypeFilterChange(newFeedbackTypes as FeedbackType[]);
  }

  const onReset = () => {
    onFeedbackTypeFilterChange(undefined);
    onStatusFilterChange(undefined);
    onBeforeFilterChange(undefined);
    onAfterFilterChange(undefined);
  }

  return (
    <OrientedSheet isOpen={isOpen} onClose={onClose} portraitHeightPercentage={0.9}>
      <View style={cn("flex flex-row gap-2 border-b border-gray-200 pb-3 px-4 justify-between items-center")}>
        <Text size={"xl"} bold>
          Filter
        </Text>
        <Button colorScheme={"link"} onPress={onReset} size={"sm"}>
          Reset Filters
        </Button>
      </View>

      <ScrollView contentContainerStyle={cn("pb-6")}>
        <View style={cn("mx-4")}>
          <Text bold style={cn("my-3")}>
            Status
          </Text>

          <View style={cn("flex flex-row gap-2 flex-wrap items-center mb-2")}>
            {map(statuses, status => {
              const isActive = includes(filterStatuses ?? [], status);
              return <TogglePill key={status} isActive={isActive} value={status}
                                 onToggle={onToggleStatus}>
                {statusToLabel[status]}
              </TogglePill>
            })}
            <TogglePill isActive={isEmpty(filterStatuses)} value={"all"}
                        onToggle={onToggleStatus}>
              Show All
            </TogglePill>
          </View>
        </View>

        <View style={cn("mx-4")}>
          <Text bold style={cn("my-3")}>
            Type
          </Text>

          <View style={cn("flex flex-row gap-2 flex-wrap items-center mb-2")}>
            {map(feedbackTypes, feedbackType => {
              const isActive = includes(filterFeedbackTypes ?? [], feedbackType);
              return <TogglePill key={feedbackType} isActive={isActive} value={feedbackType}
                                 onToggle={onToggleFeedbackType}>
                {feedbackTypeToLabel[feedbackType]}
              </TogglePill>
            })}
            <TogglePill isActive={isEmpty(filterFeedbackTypes)} value={"all"}
                        onToggle={onToggleFeedbackType}>
              Show All
            </TogglePill>
          </View>
        </View>

        <DateRangeFilterControls timezone={timezone} filterAfter={filterAfter} filterBefore={filterBefore}
                                 onAfterFilterChange={onAfterFilterChange} onBeforeFilterChange={onBeforeFilterChange}/>
      </ScrollView>
      <View style={cn("px-16 pt-2 border-t border-gray-300")}>
        <Button onPress={onClose}>
          View Results
        </Button>
      </View>
    </OrientedSheet>
  );
}
