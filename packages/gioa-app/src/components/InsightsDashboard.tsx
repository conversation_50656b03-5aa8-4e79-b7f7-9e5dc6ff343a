import React, {useCallback, useMemo} from 'react';
import {Pressable, View} from "react-native";
import {cn} from "@/style.util";
import {chunk, compact, filter, find, groupBy, isEmpty, map} from "lodash";
import {DashboardCorrectiveActions} from "@/components/DashboardCorrectiveActions";
import {api} from "@/api";
import {DateTime} from "luxon";
import {Text} from "@/components/Text";
import {TeamCarousel} from "@/components/TeamCarousel";
import {DayPerson} from "@/shiftLead.util";
import {PersonDto} from "../../../api/src/schemas";
import {IsoWeekDate} from "../../../api/src/timeSchemas";
import {PositionScoreDto} from '@gioa/api/src/insights.schemas';
import {Badge} from "@/components/Badge";
import {colors} from "@/styles";
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import {goToScheduleNotes} from "@/navigationUtil";
import {router} from "expo-router";

export interface InsightsDashboardProps {
  storeId: string;
  activeDate: DateTime;
}


interface InsightPersonDto extends PersonDto {
  positionScores: PositionScoreDto[];
}

export const InsightsDashboard: React.FC<InsightsDashboardProps> = ({storeId, activeDate}) => {
  const isoWeekDisplayed = useMemo((): IsoWeekDate => {
    const startOfIsoWeek = activeDate;
    return {
      week: startOfIsoWeek.weekNumber,
      year: startOfIsoWeek.weekYear,
    }
  }, [activeDate]);
  const [store] = api.user.getStore.useSuspenseQuery({storeId}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const [{schedule}] = api.user.getPublishedScheduleForThisWeek.useSuspenseQuery({storeId, week: isoWeekDisplayed});
  const [{items: correctiveActionAttentionList}] = api.correctiveAction.getAttentionList.useSuspenseQuery({storeId}, {
    refetchInterval: 1000 * 60 * 15 // 15 minutes
  });
  const [{items: positionScores}] = api.insights.findPositionScores.useSuspenseQuery({
    storeId,
    after: activeDate.startOf("day").toJSDate(),
    before: activeDate.endOf("day").toJSDate()
  })
  const timezone = store.timezone;

  const scoredTeam = useMemo((): DayPerson<InsightPersonDto>[][] => {
    const personToScores = groupBy(positionScores, (ps) => ps.personId);
    const people = compact(map(personToScores, (scores, personId): DayPerson<InsightPersonDto> | undefined => {
      const emp = find(store.employees, e => e.id === personId);
      if (!emp) return;
      return {
        person: {
          ...emp,
          positionScores: scores
        },
        shifts: []
      }
    }));

    return chunk(people, 5);
  }, [positionScores, store, schedule]);

  const onPersonPositionScorePress = (person: InsightPersonDto) => {
    router.push({
      pathname: "/(signedin)/person/[storeId]/[personId]/view-position-evaluation",
      params: {
        storeId: storeId,
        personId: person.id,
        positionId: person.positionScores[0].positionId,
        isTrainingRequestUpdate: "false"
      }
    })
  }

  const rightElem = useCallback(({person}: DayPerson<InsightPersonDto>) => {
    return <View style={cn("items-center justify-center")}>
      {isEmpty(person.positionScores)
        ? <Badge label={"Not scored"} color={"warning"}/>
        : <Badge label={"Scored"} color={"success"}/>}
    </View>
  }, []);

  const onOpenNotes = () => {
    if (!schedule) return;
    goToScheduleNotes({
      activeDate: activeDate,
      scheduleId: schedule.id,
      storeId: storeId
    })
  }

  const [{items: notes}] = api.insights.findShiftLeadAndScheduleNotes.useSuspenseQuery({
    storeId,
    range: {
      start: activeDate.startOf("day").toJSDate(),
      end: activeDate.endOf("day").toJSDate()
    },
    orderBy: [{column: "createdAt", direction: "asc"}],
    take: 1000
  });

  const filteredNotes = useMemo(() => compact(filter(notes, note =>
                  DateTime.fromJSDate(note.forAbsDate, {zone: timezone}).hasSame(activeDate, "day"))),
          [notes, activeDate]);

  return (
    <View>
      {!isEmpty(correctiveActionAttentionList) ?
        <DashboardCorrectiveActions storeId={storeId} headerTextSize={"xl"}
                                    timezone={timezone} className={"mx-4 px-4 mb-4"}
                                    correctiveActions={correctiveActionAttentionList}/> : null}

      {!isEmpty(scoredTeam) ?
        <>
          <Text size={"xl"} bold style={[cn("mx-4 mb-3"), {color: "#495A6D"}]}>
            Position Scores
          </Text>

          <TeamCarousel team={scoredTeam}
                        getRightElem={rightElem}
                        onPersonPress={onPersonPositionScorePress}/>
        </> : null}

      <View style={cn("flex flex-row gap-3 items-stretch mx-4 mt-4")}>

        <Pressable onPress={onOpenNotes}
                   style={cn("bg-white rounded-xl px-2 py-4 shadow-md flex-1 flex items-center justify-center gap-2")}>
          <MaterialCommunityIcons name="note-outline" size={36} color={colors.gioaBlue}/>
          <Text>
            Notes {filteredNotes?.length ? `(${filteredNotes.length})` : ""}
          </Text>
        </Pressable>
      </View>

    </View>
  );
}
