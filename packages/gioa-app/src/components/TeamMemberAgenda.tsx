import {ActivityIndicator, View} from "react-native";
import {filter, flatMap, isEmpty, map, orderBy} from "lodash";
import {Divider} from "@/components/Divider";
import {cn} from "@/style.util";
import {Text} from "@/components/Text";
import {PressableCard} from "@/components/PressableCard";
import {colors} from "@/styles";
import {goToShift} from "@/navigationUtil";
import {to12HourTime} from "@/util";
import {DurationText} from "@/components/DurationText";
import {getDurationHours} from "../../../api/src/timeSchemas.util";
import {dtTo24HourTime} from "../../../api/src/date.util";
import {Card} from "@/components/Card";
import React, {useMemo} from "react";
import {Activity, ShiftActivityType} from "../../../api/src/scheduleSchemas";
import {ScheduleShiftDto} from "../../../api/src/schemas";
import {SetupDay3} from "../../../api/src/setupSheets/setupSheetDay/setupSheetDaySchemas";
import * as SD from "../../../api/src/setupSheets/setupSheetDay/setupDay3";
import {DateTime} from "luxon";
import * as TF from "../../../api/src/setupSheets/setupSheetDay/timeFrame3";
import {SetupActivities} from "../../../api/src/setupSheets/setupSheetDay/setupActivitiesSchemas";
import {api} from "@/api";
import * as A from "../../../api/src/setupSheets/setupSheetDay/setupActivity";
import {SetupActivity} from "../../../api/src/setupSheets/setupSheetDay/setupActivitySchemas";
import * as PA from "../../../api/src/setupSheets/setupSheetDay/positionAssignments";

export interface ShiftActivitiesListProps {
  setupDay: SetupDay3 | undefined;
  setupActivities: SetupActivities | undefined;
  isSetupActivitiesLoading: boolean;
  isEmployed: boolean;
  currentPersonId: string;
  shiftsToday: ScheduleShiftDto[];
  storeId: string;
  now: DateTime;
}

export const TeamMemberAgenda: React.FC<ShiftActivitiesListProps> = ({
                                                                       setupDay, isEmployed,
                                                                       shiftsToday,
                                                                       setupActivities,
                                                                       isSetupActivitiesLoading,
                                                                       currentPersonId,
                                                                       storeId,
                                                                       now
                                                                     }) => {

  const timeFrames = setupDay ? SD.getTimeFrames(setupDay) : null;
  const firstTimeFrame = timeFrames?.[0];
  const setupDayStart = firstTimeFrame ? TF.getStart(firstTimeFrame) : null;
  const nowTime = dtTo24HourTime(now);
  const hasSetupDayStarted = setupDayStart ? nowTime >= setupDayStart : false;

  // really there should only ever be one current position
  const currentPositions = setupDay && hasSetupDayStarted
          ? SD.getPersonAssignments(setupDay, currentPersonId)
          : [];
  const currentPosition = currentPositions[0];

  const activities = useMemo((): Array<SetupActivity | Activity & { shift: ScheduleShiftDto }> => {
    const unifiedActivities = [
      ...flatMap(shiftsToday, shift => map(filter(shift.activities, a => a.activityType !== "setups" && a.activityType !== "breaks"), a => ({
        ...a,
        shift
      }))),
      ...setupActivities ?? []
    ];

    return orderBy(unifiedActivities, activity => {
      if ("range" in activity) {
        return activity.range.start;
      } else {
        return A.getStart(activity);
      }
    }, "asc");
  }, [setupActivities, shiftsToday]);

  // const currentAssignment = SD.getPersonAssignments()

  return <View>
    {currentPosition ? <View style={cn("border border-gray-300 rounded-xl px-4 py-4")}>
      <View
              style={cn("flex flex-row gap-2 flex-wrap justify-between items-center mb-2")}>
        <View>
          <Text bold>
            Current Position
          </Text>
          <Text size={"sm"} muted>
            Since {to12HourTime(PA.getAssignmentStartTime(currentPosition))}
          </Text>
        </View>

        <View style={cn("bg-blue-100 border border-blue-200 rounded-lg px-3 py-1")}>
          <Text>
            {currentPosition.positionTitle}
          </Text>
        </View>
      </View>
    </View> : null}

    {!isEmpty(activities) ? <View style={cn("mt-4")}>
      <Text bold style={[cn("mb-2"), {color: "#495A6D"}]}>
        Your Agenda Today
      </Text>
    </View> : null}

    {isSetupActivitiesLoading ? <ActivityIndicator size={"large"}/> : null}

    {map(activities, (activity) => {
      if ("countsTowardsLabor" in activity) { // if is a shift Activity
        if (activity.activityType !== "setups" && activity.activityType !== "breaks") {
          const colorScheme = activityTypeToColorScheme[activity.activityType];
          const range = activity.range;
          return <PressableCard color={colorScheme?.cardColor ?? colors.gray[300]}
                                onPress={() => goToShift({
                                  viewerPersonId: currentPersonId,
                                  shiftId: activity.shift.id,
                                  storeId: storeId,
                                  assignedPersonId: activity.shift.assignedPersonId ?? "",
                                })}
                                key={activity.id} className={"mb-2"}>
            <Text>{activity.title}</Text>
            <Text size={"sm"}>{to12HourTime(range.start)} - {to12HourTime(range.end)} (<DurationText
                    durationHours={getDurationHours(range)}/>)</Text>
          </PressableCard>
        }
      } else { // else is a SetupActivity
        if (A.isSetupPositionActivity(activity)) {
          const positionTitle = A.getSetupPositionTitle(activity);
          const colorScheme = activityTypeToColorScheme["setups"];
          const range = A.getRange(activity);

          return <Card color={colorScheme?.cardColor ?? colors.gray[300]}
                       rightElem={() => <View
                               style={[cn(colorScheme.containerClassName, "border rounded-md px-1"), {alignSelf: "flex-start"}]}>
                         <Text size={"xs"} style={cn("text-gray-700")}>
                           Setup
                         </Text>
                       </View>}
                       key={A.getId(activity)} className={"mb-2"}>
            <View style={cn("flex flex-row flex-wrap justify-between items-center")}>
              <Text>{positionTitle}</Text>
            </View>
            <Text size={"sm"}>{to12HourTime(range.start)} - {to12HourTime(range.end)} (<DurationText
                    durationHours={getDurationHours(range)}/>)</Text>
          </Card>
        } else if (A.isBreakActivity(activity)) {
          const colorScheme = activityTypeToColorScheme["breaks"];
          const range = A.getRange(activity);
          const title = A.getTitle(activity) ?? "Break";

          return <Card color={colorScheme?.cardColor ?? colors.gray[300]}
                       rightElem={() => <View
                               style={[cn(colorScheme.containerClassName, "border rounded-md px-1"), {alignSelf: "flex-start"}]}>
                         <Text size={"xs"} style={cn("text-gray-700")}>
                           Break
                         </Text>
                       </View>}
                       key={A.getId(activity)} className={"mb-2"}>
            <View style={cn("flex flex-row flex-wrap justify-between items-center")}>
              <Text>{title}</Text>
            </View>
            <Text size={"sm"}>{to12HourTime(range.start)} - {to12HourTime(range.end)} (<DurationText
                    durationHours={getDurationHours(range)}/>)</Text>
          </Card>
        }

      }
    })}
  </View>
}

export const activityTypeToColorScheme: Record<ShiftActivityType, {
  containerClassName: string;
  cardColor: string;
}> = {
  admin: {
    containerClassName: "bg-green-100 border-green-200",
    cardColor: colors.green[300]
  },
  setups: {
    containerClassName: "bg-blue-100 border-blue-200",
    cardColor: colors.blue[300]
  },
  breaks: {
    containerClassName: "bg-purple-100 border-purple-200",
    cardColor: colors.purple[300]
  },
  training: {
    containerClassName: "bg-orange-100 border-orange-200",
    cardColor: colors.orange[300]
  },
  custom: {
    containerClassName: "bg-gray-100 border-gray-200",
    cardColor: colors.gray[300]
  },
}

