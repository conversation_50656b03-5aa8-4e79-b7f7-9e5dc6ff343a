import React from 'react';
import {cn} from "@/style.util";
import {Button} from "./Button";
import {Text} from "./Text";
import {CoreValueDto, PersonDetailDto, StoreDto} from "../../../api/src/schemas";
import {Pressable, View} from 'react-native';
import {Feather} from "@expo/vector-icons";
import {parsePhoneNumberFromString} from 'libphonenumber-js';
import {find, map} from 'lodash';
import {PlayerCardTabContent} from "@/components/PlayerCardTabContent";
import * as Clipboard from 'expo-clipboard';
import Toast from "react-native-root-toast";
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuItemTitle,
  DropdownMenuRoot,
  DropdownMenuTrigger
} from "@/components/PopoverMenu";

export interface PlayerDetailsTabContentProps {
  coreValues: CoreValueDto[];
  person: PersonDetailDto;
  storeId: string;
  StatusBarComponent?: React.ComponentType<{ person: PersonDetailDto; storeId: string }>;
  onEditEmployeeDetails: () => void;
  onEditEmployeeAbout: () => void;
  onEditPersonBio: () => void;
  onEditEmployeeCoreValues: (coreValueId: string) => void;
  canEditPerson: boolean;
  canEditCoreValues: boolean;
  businessStores: StoreDto[];
  canViewEmergencyContact: boolean;
  canEditEmergencyContact: boolean;
}

const longDash = "―"

export const PlayerDetailsTabContent = React.memo(({
                                                     coreValues,
                                                     person,
                                                     storeId,
                                                     StatusBarComponent,
                                                     onEditEmployeeDetails,
                                                     onEditEmployeeAbout,
                                                     onEditPersonBio,
                                                     onEditEmployeeCoreValues,
                                                     canEditPerson,
                                                     canEditEmergencyContact,
                                                     canEditCoreValues,
                                                     businessStores, canViewEmergencyContact,
                                                   }: PlayerDetailsTabContentProps) => {

  return (
    <PlayerCardTabContent>
      {StatusBarComponent ? <StatusBarComponent person={person} storeId={storeId}/> : null}
      <View style={cn("px-4 mb-4")}>
        <View style={cn("flex flex-row justify-between items-center mb-2")}>
          <Text size={"lg"}>
            Team Member Details
          </Text>
          {canEditPerson ? <Button size={"sm"} rightIcon={s => <Feather name={"edit-3"} style={s}/>}
                                   sharp colorScheme={"gray"} onPress={onEditEmployeeDetails}>
            Edit
          </Button> : undefined}
        </View>

        <View style={cn("bg-white rounded-2xl px-4 py-3")}>
          <View style={cn("flex flex-row gap-2 justify-between border-b border-gray-200 pb-2 mb-2")}>
            <Text muted>
              Full Name
            </Text>
            <Text style={{flex: 1}} right>
              {person.firstName} {person.lastName}
            </Text>
          </View>

          <View style={cn("flex flex-row gap-2 justify-between border-b border-gray-200 pb-2 mb-2")}>
            <Text muted>
              Phone Number
            </Text>
            {person.phoneNumber ? (
              <View style={cn("flex-1")}>
                <DropdownMenuRoot>
                  <DropdownMenuTrigger>
                    <Pressable style={{flex: 1}}>
                      <Text right>
                        {parsePhoneNumberFromString(person.phoneNumber)?.formatNational()}
                      </Text>
                    </Pressable>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem
                      onSelect={async () => {
                        const phoneNumber = parsePhoneNumberFromString(person.phoneNumber!)?.formatNational();
                        if (phoneNumber) {
                          await Clipboard.setStringAsync(phoneNumber);
                          Toast.show("Phone number copied to clipboard!", {
                            duration: Toast.durations.SHORT,
                            position: Toast.positions.TOP,
                          });
                        }
                      }}
                      key={"copy-phone-number"}>
                      <DropdownMenuItemTitle>
                        Copy Phone Number
                      </DropdownMenuItemTitle>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenuRoot>
              </View>
            ) : (
              <Text style={{flex: 1}} right>
                {person.phoneNumber ? parsePhoneNumberFromString(person.phoneNumber)?.formatNational() : longDash}
              </Text>
            )}
          </View>

          <View style={cn("flex flex-row gap-2 justify-between border-b border-gray-200 pb-2 mb-2")}>
            <Text muted>
              Email Address
            </Text>
            {person.email ? (
              <View style={cn("flex-1")}>
                <DropdownMenuRoot>
                  <DropdownMenuTrigger>
                    <Text style={cn("flex-1")} right>
                      {person.email || longDash}
                    </Text>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem
                      onSelect={async () => {
                        await Clipboard.setStringAsync(person.email!);
                        Toast.show("Email copied to clipboard!", {
                          duration: Toast.durations.SHORT,
                          position: Toast.positions.TOP,
                        });
                      }}
                      key={"copy-email"}>
                      <DropdownMenuItemTitle>Copy Email Address</DropdownMenuItemTitle>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenuRoot>
              </View>
            ) : (
              <Text style={cn("flex-1")} right>
                {person.email || longDash}
              </Text>
            )}
          </View>

          <View style={cn("flex flex-row gap-2 justify-between border-b border-gray-200 pb-2 mb-2")}>
            <Text muted>
              {`Store${person.employments.storeIds.length + person.employments.requestedStoreIds.length > 1 ? 's' : ''}`}
            </Text>
            <View style={cn("flex-1 flex flex-col")}>
              {map([...person.employments.storeIds, ...person.employments.requestedStoreIds], storeId => {
                const store = find(businessStores, store => store.id === storeId);
                return <Text right key={store?.id}>{store?.title} ({store?.chickfilaStoreId})</Text>
              })}
            </View>
          </View>

          <View style={cn("flex flex-row gap-2 justify-between border-b border-gray-200 pb-2 mb-2")}>
            <Text muted>
              Job Title
            </Text>
            <Text style={{flex: 1}} right>
              {person.jobTitle || longDash}
            </Text>
          </View>

          <View style={cn("flex flex-row gap-2 justify-between")}>
            <Text muted>
              Date Started
            </Text>
            <Text style={{flex: 1}} right>
              {person.metadata?.playerDateHired || longDash}
            </Text>
          </View>
        </View>
      </View>

      {canViewEmergencyContact ?
        <View style={cn("px-4")}>
          <View style={cn("flex flex-row justify-between items-center mb-2")}>
            <Text size={"lg"}>
              Emergency Contact
            </Text>
            {canEditEmergencyContact ? <Button size={"sm"} rightIcon={s => <Feather name={"edit-3"} style={s}/>}
                                               sharp colorScheme={"gray"} onPress={onEditEmployeeDetails}>
              Edit
            </Button> : undefined}
          </View>
          <View style={cn("bg-white rounded-2xl px-4 py-3")}>
            <View style={cn("flex flex-row gap-2 justify-between border-b border-gray-200 pb-2 mb-2")}>
              <Text muted>
                Emergency Contact
              </Text>
              <Text style={{flex: 1}} right>
                {person.metadata?.emergencyContactName || longDash}
              </Text>
            </View>

            <View style={cn("flex flex-row gap-2 justify-between")}>
              <Text muted>
                Emergency Phone
              </Text>
              <Text style={{flex: 1}} right>
                {person.metadata?.emergencyContactPhoneNumber || longDash}
              </Text>
            </View>
          </View>
        </View> : null}
    </PlayerCardTabContent>
  );
});
