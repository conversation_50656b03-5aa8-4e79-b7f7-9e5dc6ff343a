import React, {useEffect, useRef, useState} from "react";
import {Platform, Pressable, View} from "react-native";
import {useDisclosure} from "../../hooks/useDisclosure";
import {Ionicons} from "@expo/vector-icons";
import {FieldApi} from "@tanstack/react-form";
import {find, isEmpty, map} from "lodash";
import {cn} from "../../style.util";
import {VariantProps} from "class-variance-authority";
import {Text} from "../Text";
import {colors} from "../../styles";
import {useSafeAreaInsets} from "react-native-safe-area-context";
import {Picker} from '@react-native-picker/picker';
import {BottomSheet} from "../BottomSheet";
import {inputContainerVariants, inputTextVariants} from "@/components/newform/formInputStyles";
import {useScreenOrientation} from "@/hooks/useScreenOrientation";

export interface PickerOption {
  value: string;
  label: string
}

export interface FormPickerBottomSheetProps extends VariantProps<typeof inputContainerVariants> {
  isDisabled?: boolean;
  field: FieldApi<any, any, any, any>;
  placeholder?: string;
  options: Array<PickerOption>;
  label?: string;
  icon?: string;
  getOptionButtonLabel?: (option: PickerOption) => string;
  onChange?: (value?: string) => void;
  canUnselect?: boolean; // Add this line
}

export const FormPickerBottomSheet: React.FC<FormPickerBottomSheetProps> = ({
                                                                              isDisabled,
                                                                              size,
                                                                              colorScheme,
                                                                              field, options,
                                                                              placeholder = "Select a date...",
                                                                              label,
                                                                              icon,
                                                                              getOptionButtonLabel,
                                                                              onChange,
                                                                              canUnselect,
                                                                            }) => {
  const hasErrors = !isEmpty(field.state.meta.errors);
  const textClassNames = inputTextVariants({colorScheme});
  const safeArea = useSafeAreaInsets();
  const bottomSheet = useDisclosure();
  const {isLandscape, screenHeight} = useScreenOrientation();
  const [localValue, setLocalValue] = useState(field.state.value ?? null);
  const defaultValue = options[0]?.value;

  useEffect(() => {
    if (field.state.value !== localValue) {
      setLocalValue(field.state.value);
    }
  }, [field.state.value]);

  const openBottomSheet = () => {
    if (!localValue && !isEmpty(options)) {
      setLocalValue(options[0].value);
    }

    bottomSheet.onOpen();
  }

  const closeBottomSheet = () => {
    bottomSheet.onClose();
    field.handleBlur();
  }

  const selectedOption = find(options, o => o.value === field.state.value);
  const buttonLabel = selectedOption ? getOptionButtonLabel ? getOptionButtonLabel(selectedOption) : selectedOption?.label : " ";

  const onConfirm = () => {
    if (!localValue && !canUnselect) {
      setLocalValue(defaultValue);
      field.handleChange(defaultValue ?? null);
      onChange?.(defaultValue);
    } else {
      field.handleChange(localValue ?? null);
      onChange?.(localValue);
    }
    closeBottomSheet();
  }

  const pickerRef = useRef<any>();

  if (Platform.OS === "ios") {
    return <View>
      <Pressable style={cn(inputContainerVariants({
        size, colorScheme, isDisabled, hasError: hasErrors
      }))}
                 onPress={() => {
                   if (isDisabled) return;
                   openBottomSheet();
                 }}>

        <View style={cn("flex-1")}>
          {localValue
                  ? <Text numberOfLines={1} ellipsizeMode={"tail"}
                          style={cn(textClassNames, "grow py-2.5")}>{buttonLabel ?? ""}</Text>
                  : <Text numberOfLines={1} ellipsizeMode={"tail"}
                          style={cn("text-gray-600 grow py-2.5")}>{placeholder}</Text>}
        </View>
        {icon ? <Ionicons name={icon as any}
                          size={20} color={colors.primary[600]}/> : null}
      </Pressable>

      <BottomSheet isOpen={bottomSheet.isOpen} onClose={closeBottomSheet}
                   supportedOrientations={["portrait", "landscape"]}>
        <View style={[
          cn("justify-end mx-3"),
          {
            height: screenHeight,
            paddingBottom: Math.max(safeArea.bottom, 16),
          },
        ]}>
          <View style={cn(`${isLandscape ? "flex-1" : ""} bg-white rounded-xl mb-2`)} onTouchStart={e => {
            e.preventDefault();
            e.stopPropagation();
          }}>
            <Text muted center style={cn("pt-3")} numberOfLines={1} ellipsizeMode={"tail"}>
              {label ?? "Select an option..."}
            </Text>

            <View>
              <Picker
                      selectedValue={localValue}
                      onValueChange={(itemValue, itemIndex) => {
                        const selectedOption = find(options, opt => opt.value === itemValue);
                        setLocalValue(selectedOption?.value);
                      }}>
                <Picker.Item label={placeholder ?? "Select an option..."} color={"gray"} value={null}
                             enabled={canUnselect}/>
                {map(options, option => {
                  return <Picker.Item key={option.value} label={option.label} value={option.value}/>
                })}
              </Picker>
            </View>
            <Pressable onPress={onConfirm} style={cn("py-3 border-t border-gray-200")}>
              <Text colorScheme={"link"} center size={"lg"}>
                Confirm
              </Text>
            </Pressable>
          </View>
          <Pressable onPress={closeBottomSheet} style={cn("py-3 bg-white rounded-xl")}>
            <Text colorScheme={"link"} center size={"lg"} semibold>
              Cancel
            </Text>
          </Pressable>
        </View>
      </BottomSheet>
    </View>;
  } else {
    return <View>
      <Pressable style={cn(inputContainerVariants({
        size, colorScheme, isDisabled, hasError: hasErrors
      }))}
                 onPress={() => {
                   if (isDisabled) return;
                   pickerRef.current?.focus();
                 }}>
        {localValue
                ? <Text style={cn(textClassNames, "grow py-2.5")}>{buttonLabel ?? ""}</Text>
                : <Text style={cn("text-gray-400 grow py-2.5")}>{placeholder}</Text>}
        {icon ? <Ionicons name={icon as any}
                          size={20} color={colors.primary[600]}/> : null}
      </Pressable>
      <Picker ref={pickerRef} style={{display: "none", opacity: 0}}
              prompt={placeholder} dropdownIconColor={"rgba(0, 0, 0, 0)"}
              selectedValue={localValue}
              onValueChange={(itemValue) => {
                const selectedOption = find(options, opt => opt.value === itemValue);
                setLocalValue(selectedOption?.value);
                field.handleChange(selectedOption?.value ?? null);
                onChange?.(selectedOption?.value);
              }}>
        <Picker.Item label={placeholder ?? "Select an option..."} color={"gray"} value={null} enabled={canUnselect}/>
        {map(options, option => {
          return <Picker.Item key={option.value} label={option.label} value={option.value}/>
        })}
      </Picker>
    </View>;
  }
};

