import React from 'react';
import {View, TouchableOpacity} from "react-native";
import {cn} from "@/style.util";
import {map, sortBy} from "lodash";
import {Text} from "@/components/Text";
import {Avatar} from "@/components/Avatar";
import {formatDistanceToNow} from 'date-fns';
import {ScheduleNoteDto} from "../../../api/src/note.schemas";
import {DateTime} from "luxon";
import {Button} from "@/components/Button";
import {FormInput} from "@/components/newform/FormInput";
import {FieldInfo} from "@/components/newform/FieldInfo";
import {OrientedSheet} from "@/components/OrientedSheet";
import {colors} from "@/styles";
import {FontAwesome} from "@expo/vector-icons";
import {useForm} from "@tanstack/react-form";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {z} from "zod";

export interface NotesThreadProps {
  notes: ScheduleNoteDto[];
  className?: string;
  timezone: string;
  onEditNote?: (noteId: string, noteText: string) => void;
  onDeleteNote?: (noteId: string) => void;
}

export const NotesThread: React.FC<NotesThreadProps> = ({notes, className, timezone, onEditNote, onDeleteNote}) => {
  const renderDateTime = (date: Date) => {
    const noteDate = DateTime.fromJSDate(date, {zone: timezone});
    const relativeTime = formatDistanceToNow(date);
    const absoluteTime = noteDate.toLocaleString({
      hour: "numeric",
      minute: "numeric",
    });
    return `${relativeTime} ago (${absoluteTime})`;
  };
  const sortedNotes = sortBy(notes, note => note.createdAt).reverse();

  const [editingNote, _setEditingNote] = React.useState<ScheduleNoteDto | null>(null);
  const setEditingNote = (note: ScheduleNoteDto | null) => {
    _setEditingNote(note);
    if (note) {
      form.setFieldValue("note", note.note);
    }
  };

  const form = useForm({
    defaultValues: {
      note: "",
    },
    onSubmit: async ({value}) => {
      if (editingNote) {
        onEditNote?.(editingNote.id, value.note);
        setEditingNote(null);
      }
    },
    validatorAdapter: zodValidator(),
  });

  return <>
    {map(sortedNotes, note => (
            <View key={note.id} style={cn("mb-4 mx-4", className)}>
              <View style={cn("flex-row items-center mb-2 gap-2")}>
                {note.createdByPerson
                        ? <>
                          <Avatar src={note.createdByPerson.profileImageUrl}/>
                          <View>
                            <Text semibold>
                              {note.createdByPerson.firstName} {note.createdByPerson.lastName} {note.noteType === "shiftLeadNotes" ? " (Shift Lead)" : ""}
                            </Text>
                            <Text style={cn("text-gray-500 text-sm")}>
                              {renderDateTime(note.createdAt)}
                            </Text>
                          </View>
                        </> : <Text semibold>
                          System
                        </Text>}
              </View>
              <View style={cn("flex-row")}>
                <View style={cn("w-10 items-center")}>
                  <View style={[cn("bg-gray-300 flex-1"), {width: 2}]}/>
                </View>
                <Text style={cn("flex-1 pl-2")}>
                  {note.note}
                </Text>

                {onEditNote ?
                        <TouchableOpacity onPress={() => setEditingNote(note)}>
                          <FontAwesome name="edit" size={22} color={colors.gray[400]}/>
                        </TouchableOpacity>
                        : null}
              </View>
            </View>
    ))}

    {/*Edit notes bottom sheet*/}
    <OrientedSheet isOpen={!!editingNote} onClose={() => setEditingNote(null)} portraitHeightPercentage={0.3}>
      <View style={cn("flex-row justify-between mb-2 px-4")}>
        <Text style={cn("pt-2 pb-2")} bold size={"xl"}>
          Edit Note
        </Text>
        <Button colorScheme={"link"} variant={"link"} onPress={() => setEditingNote(null)}>
          Cancel
        </Button>
      </View>

      <View style={cn("border-t border-gray-200 pt-2")}>
        <form.Field name={"note"}
                    validators={{
                      onSubmit: z.string().min(1, "Required")
                    }}
                    children={field => <View style={cn("px-4")}>
                      <FormInput field={field} multiline style={{height: 80}}/>
                      <FieldInfo field={field}/>
                    </View>}/>

        <View style={cn("flex-row justify-center gap-4")}>
          {onDeleteNote ?
                  <Button variant={"outline"}
                          onPress={() => {
                            onDeleteNote(editingNote!.id);
                            setEditingNote(null);
                          }}
                  >
                    Delete Note
                  </Button>
                  : null}
          <Button onPress={form.handleSubmit}>
            Update Note
          </Button>
        </View>
      </View>
    </OrientedSheet>
  </>
}
