import {router, useLocalSearchParams, useNavigation} from 'expo-router';
import React, {useLayoutEffect} from 'react';
import {Alert, Pressable, ScrollView, View} from 'react-native';
import {Text} from '@/components/Text';
import {cn} from "@/style.util";
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {api} from "@/api";
import {Label} from "@/components/newform/Label";
import {FieldInfo} from "@/components/newform/FieldInfo";
import {Button} from "@/components/Button";
import {FieldApi, useForm} from "@tanstack/react-form";
import {zodValidator} from '@tanstack/zod-form-adapter'
import Toast from "react-native-root-toast";
import {z} from "zod";
import {ErrorAlert} from "@/components/ErrorAlert";
import {filter, find, isEmpty, map} from "lodash";
import {FormTextarea} from "@/components/newform/FormTextarea";
import {useAnimatedKeyboardAvoidingBottomPadding} from "@/hooks/useAnimatedKeyboardAvoidingBottomPadding";
import Animated from 'react-native-reanimated';
import {FormRadioGroup} from "@/components/newform/FormRadioGroup";
import {FormSwitch} from "@/components/newform/FormSwitch";
import {Badge} from "@/components/Badge";
import {Ionicons} from "@expo/vector-icons";
import {selectOrCreateConfig} from "@/app/(signedin)/person/[storeId]/[personId]/select-or-create";
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuItemTitle,
  DropdownMenuRoot,
  DropdownMenuTrigger
} from "@/components/PopoverMenu";
import * as DropdownMenu from "zeego/dropdown-menu";
import {AddImageButton} from "@/components/newform/AddImageButton";
import {SelectedMedia} from "@/onPickMediaFromLibrary";
import {Image} from "expo-image";
import {useUploadFile} from "@/useUploadFile";

import {inferMimeTypeFromUri} from "../../../api/src/inferMimeTypeFromUri";

export interface AddEditNoteScreenProps {
}

export const sensitivityLevelOptions = [{
  label: "Low",
  value: "low"
}, {
  label: "Medium",
  value: "medium"
}, {
  label: "High",
  value: "high"
}];

export const AddEditNoteScreen: React.FC<AddEditNoteScreenProps> = () => {
  const {personId, storeId, noteId, type} = useLocalSearchParams<{
    personId: string;
    storeId: string;
    noteId: string;
    type: string;
  }>();

  const [person] = api.user.getPersonDetail.useSuspenseQuery({
    personId: personId!,
    storeId: storeId!,
  }, {
    staleTime: 5 * 60 * 1000
  });

  const note = find(person.notes, n => n.id === noteId!);
  const noteType = (type ?? (note && "noteType" in note ? note?.noteType : undefined));
  const safeArea = useSafeAreaInsets();
  const apiUtil = api.useUtils();
  const animatedStyle = useAnimatedKeyboardAvoidingBottomPadding();

  const getPresignedPost = api.user.getPersonNotesPresignedPost.useMutation();
  const upload = useUploadFile(({contentType}) => getPresignedPost.mutateAsync({contentType, folder: "note"}));
  const upsertNote = api.user.upsertPersonNote.useMutation({
    onSuccess() {
      Toast.show(`Note ${note ? "updated" : "created"} for ${person?.firstName} ${person?.lastName}`, {
        position: Toast.positions.TOP
      });
      apiUtil.user.getPersonDetail.invalidate({
        personId: personId!,
        storeId: storeId!
      });
      apiUtil.user.getPersonNote.invalidate({
        noteId: noteId!
      });
      router.dismiss(2);
    }
  });

  const form = useForm({
    defaultValues: {
      note: note?.note || '',
      sensitivityLevel: (note && "sensitivityLevel" in note ? note?.sensitivityLevel ?? "low" : "low") as string,
      requiresAcknowledgement: note && "requiresAcknowledgement" in note ? note?.requiresAcknowledgement ?? false : false,
      policiesInAction: note && "policiesInAction" in note ? note?.policiesInAction ?? [] : [],
      images: [] as SelectedMedia[],
      existingImages: (note && "images" in note ? note?.images ?? [] : []),
    },
    validatorAdapter: zodValidator(),
    onSubmit: async (event) => {
      // upload all the images first
      const uploadedImageIds: string[] = [];
      for (const image of event.value.images) {
        const mimeType = image.mimeType || inferMimeTypeFromUri(image.uri);
        const uploadResult = await upload.upload({
          uri: image.uri,
          contentType: mimeType,
          mediaType: "image"
        });
        if (!uploadResult) {
          Alert.alert("Error", "Failed to upload image. Please try again.");
          return;
        }
        uploadedImageIds.push(uploadResult.newId);
      }

      upsertNote.mutate({
        personId: personId!,
        noteId: noteId!,
        note: event.value.note,
        type: noteType,
        sensitivityLevel: event.value.sensitivityLevel,
        requiresAcknowledgement: event.value.requiresAcknowledgement,
        policiesInAction: event.value.policiesInAction,
        storeId: storeId!,
        images: map(event.value.images, (image, idx) => {
          const newImageId = uploadedImageIds[idx];
          return {
            imageId: newImageId,
            width: image.width,
            height: image.height,
            mimeType: image.mimeType
          }
        }),
        copyImageIds: map(event.value.existingImages, image => image.id),
      })
    }
  })

  const onGoBack = () => {
    router.back();
  }

  const navigation = useNavigation();

  const isCoachingMoment = (noteType ?? (note && "noteType" in note ? note?.noteType : undefined)) === "coaching";
  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: noteType === "positive-feedback"
        ? "Positive Feedback"
        : noteType === "coaching"
          ? "Coaching Moment"
          : "General Note"
    });
  }, [noteType]);

  const onSelectPolicy = (field: FieldApi<any, "policiesInAction", any, any>) => {
    selectOrCreateConfig.onChange = (policies) => {
      field.handleChange(filter(policies, p => !isEmpty(p)));
    }
    router.push({
      pathname: "/(signedin)/person/[storeId]/[personId]/select-or-create",
      params: {
        storeId: storeId,
        personId: personId,
        initSelectedIdsStr: field.state.value.join(","),
      }
    })
  };

  const isLoading = upsertNote.isPending || upload.isLoading;
  return (
    <View style={[{paddingBottom: Math.max(safeArea.bottom, 16)}, cn("bg-white flex-1")]}>
      <ScrollView style={cn("flex-1 px-6")}>
        <Text style={cn("mb-3 pt-6")}>
          Leave a note about {person?.firstName} {person?.lastName}.
        </Text>
        {upsertNote.isError ? <ErrorAlert error={upsertNote.error}/> : null}
        <form.Field name={"note"}
                    validators={{
                      onSubmit: z.string().min(1, "Required")
                    }}
                    children={field => <View>
                      <Label>Note</Label>
                      <FormTextarea field={field}
                                    placeholder="Enter your note about this person..."/>
                      <FieldInfo field={field}/>
                    </View>}/>

        <form.Field name={"sensitivityLevel"}
                    validators={{
                      onSubmit: z.string()
                    }}
                    children={field => <View>
                      <Label className={"mb-1"}>Sensitivity Level</Label>
                      <FormRadioGroup field={field}
                                      options={sensitivityLevelOptions}/>
                      <FieldInfo field={field}/>
                    </View>}/>

        {noteType !== "actionable-item" ?
          <form.Field name={"requiresAcknowledgement"}
                      children={field => <View>
                        <FormSwitch field={field} rightLabel={<Label>Require Acknowledgement</Label>}/>
                        <FieldInfo field={field}/>
                        <Text size={"sm"} muted>This will require the team member to acknowledge the coaching moment
                          when
                          they open the Nation app.</Text>
                      </View>}/> : null}

        {isCoachingMoment ?
          <form.Field name={"policiesInAction"}
                      validators={{
                        onSubmit: z.array(z.string())
                      }}
                      children={field => <View style={cn("mb-4 mt-4")}>
                        <Label className={"mb-1"}>
                          Policy in action (optional)
                        </Label>
                        {isEmpty(field.state.value) ?
                          <Button variant={"outline"}
                                  onPress={() => onSelectPolicy(field)}>
                            Select Policies...
                          </Button> : <Pressable style={cn("flex flex-row flex-wrap gap-2 items-center")}
                                                 onPress={() => onSelectPolicy(field)}>
                            {map(field.state.value, policy => {
                              return <Badge key={policy} label={policy} size={"lg"} color={"info"}/>
                            })}
                            <Ionicons name={"add"} size={24}/>
                          </Pressable>}
                        <FieldInfo field={field}/>
                      </View>}/> : null}

        <Label className={"mb-1"}>
          Attachments
        </Label>
        <View style={cn("flex flex-row flex-wrap gap-2")}>
          <form.Field name={"existingImages"}
                      mode={"array"}
                      validators={{
                        onSubmit: z.array(z.any()).max(5)
                      }}
                      children={arrField => <>
                        {map(arrField.state.value, (image, idx) => {
                          return <DropdownMenuRoot key={image.id}>
                            <DropdownMenuTrigger>
                              <Pressable>
                                <Image source={`${image.url}?w=100&h=100`} key={image.id}
                                       contentFit={"contain"}
                                       style={{width: 100, height: 100}}/>
                              </Pressable>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem onSelect={() => arrField.removeValue(idx)}
                                                key="remove" destructive={true}>
                                <DropdownMenuItemTitle>Remove</DropdownMenuItemTitle>
                                <DropdownMenu.ItemIcon ios={{name: 'trash',}}/>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenuRoot>
                        })}
                      </>}/>
          <form.Field name={"images"}
                      mode={"array"}
                      validators={{
                        onSubmit: z.array(z.any()).max(5)
                      }}
                      children={arrField => <>
                        {map(arrField.state.value, (image, idx) => {
                          return <DropdownMenuRoot key={image.uri}>
                            <DropdownMenuTrigger>
                              <Pressable>
                                <Image source={{uri: image.uri}} key={image.uri}
                                       contentFit={"contain"}
                                       style={{width: 100, height: 100}}/>
                              </Pressable>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem onSelect={() => arrField.removeValue(idx)}
                                                key="remove" destructive={true}>
                                <DropdownMenuItemTitle>Remove</DropdownMenuItemTitle>
                                <DropdownMenu.ItemIcon ios={{name: 'trash',}}/>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenuRoot>
                        })}
                        <AddImageButton onAdd={arrField.pushValue}/>
                        <FieldInfo field={arrField}/>
                      </>}/>
        </View>

      </ScrollView>
      {Boolean(note) ?
        <Animated.View style={[cn("flex flex-row px-6 pt-3 border-t border-gray-300"), animatedStyle]}>
          <View style={{width: "50%"}}>
            <Button variant={"outline"} onPress={onGoBack}
                    isDisabled={isLoading} isLoading={upsertNote.isPending}
                    style={cn("mr-2")} colorScheme={"gray"}>
              Cancel
            </Button>
          </View>
          <View style={{width: "50%"}}>
            <Button colorScheme={"primary"} isLoading={upsertNote.isPending}
                    isDisabled={isLoading} onPress={form.handleSubmit}
                    style={cn("ml-2")}>
              Save
            </Button>
          </View>
        </Animated.View> :
        <Animated.View style={[cn("px-16 pt-3 border-t border-gray-300"), animatedStyle]}>
          <Button colorScheme={"primary"} isLoading={upsertNote.isPending || upload.isLoading}
                  isDisabled={isLoading} onPress={form.handleSubmit}
                  style={cn("ml-2")}>
            Save
          </Button>
        </Animated.View>
      }
    </View>
  );
}

