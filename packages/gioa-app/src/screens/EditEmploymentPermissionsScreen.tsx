import React from "react";
import {ScrollView, View} from "react-native";
import {CheckedIds, PolicyEditor} from "@/components/PolicyEditor";
import {router, useLocalSearchParams} from "expo-router";
import {api} from "@/api";
import {useForm} from "@tanstack/react-form";
import {zodValidator} from '@tanstack/zod-form-adapter'
import {FieldInfo} from "@/components/newform/FieldInfo";
import {keys, pickBy, reduce} from "lodash";
import {cn} from "@/style.util";
import {Button} from "@/components/Button";
import {useSafeAreaInsets} from "react-native-safe-area-context";
import {ErrorAlert, getHumanReadableErrorMessage} from "@/components/ErrorAlert";
import {Heading} from "@/components/Heading";
import {PackageId} from "../../../api/src/permission.types";

export default function EditEmploymentPermissionsScreen() {
  const {personId, storeId} = useLocalSearchParams<{personId: string; storeId: string}>();

  const [person] = api.user.getPersonDetail.useSuspenseQuery({
    personId: personId!,
    storeId: storeId!,
  })

  const apiUtil = api.useUtils();
  const editEmployeeJob = api.user.editEmployeeJob.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getPersonDetail.invalidate({
        personId: person.id,
        storeId: storeId!
      });
      onContinue();
    }
  })

  const onContinue = () => {
    router.canGoBack() && router.back();
  }

  const checkedPackages = reduce(person.permissionPackages, (acc, v) => {
    acc[v] = true;
    return acc;
  }, {} as CheckedIds);

  const form = useForm({
    defaultValues: {
      permissionPackages: checkedPackages,
    },
    validatorAdapter: zodValidator(),
    onSubmit: async ({value, formApi}) => {
      if (!formApi.state.isDirty) {
        onContinue();
        return;
      }
      editEmployeeJob.mutate({
        personId: person.id!,
        jobId: person.jobId!,
        permissionPackages: keys(pickBy(value.permissionPackages, v => v)) as PackageId[],
      }, {
        onError: (e) => {
          alert(getHumanReadableErrorMessage(e));
        }
      })
    }
  })
  const safeArea = useSafeAreaInsets();

  return <View style={[{ paddingBottom: Math.max(safeArea.bottom, 16) }, cn("bg-white flex flex-col flex-1")]}>
    <ScrollView stickyHeaderIndices={[0]}>
      <Heading center style={cn("pb-3 px-6 bg-white")}>
        {person.firstName} {person.lastName}
      </Heading>
      {editEmployeeJob.isError ? <ErrorAlert error={editEmployeeJob.error}/> : null}
      <form.Field name={"permissionPackages"}
                  children={field => <View>
                    <PolicyEditor value={field.state.value}
                                  onChange={field.handleChange}/>
                    <FieldInfo field={field}/>
                  </View>}/>
    </ScrollView>
    <View style={cn("flex flex-row px-6 pt-3 border-t border-gray-300")}>
      <View style={{ width: "50%" }}>
        <Button variant="outline" onPress={onContinue} style={cn("mr-2")} colorScheme="gray">
          Cancel
        </Button>
      </View>
      <View style={{ width: "50%" }}>
        <Button colorScheme="primary" isLoading={editEmployeeJob.isPending}
                onPress={form.handleSubmit} style={cn("ml-2")}>
          Save
        </Button>
      </View>
    </View>
  </View>
}
