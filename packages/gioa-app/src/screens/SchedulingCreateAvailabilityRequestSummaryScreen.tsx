import React from "react";
import {ScrollView, View} from "react-native";
import {useSafeAreaInsets} from "react-native-safe-area-context";
import {cn} from "@/style.util";
import {api} from "@/api";
import {router, useLocalSearchParams} from "expo-router";
import {useForm} from "@tanstack/react-form";
import {useAvoidKeyboard} from "@/hooks/useAvoidKeyboard";
import {Button} from "@/components/Button";
import Toast from "react-native-root-toast";
import {Text} from "@/components/Text";
import {FormCalendarDatePicker} from "@/components/newform/FormCalendarDatePicker";
import {AvailabilityWeekReview} from "@/components/AvailabilityWeekReview";
import {FormInput} from "@/components/newform/FormInput";
import {DateTime} from "luxon";
import {PersonAvailabilityDto} from "../../../api/src/availabilitySchemas";
import {Divider} from "@/components/Divider";
import {formatDateNoTime} from "@/util";
import {FieldInfo} from "@/components/newform/FieldInfo";

export interface SchedulingCreateAvailabilityRequestSummaryScreenProps {
}

export const SchedulingCreateAvailabilityRequestSummaryScreen: React.FC<SchedulingCreateAvailabilityRequestSummaryScreenProps> = ({}) => {
  const {storeId, personId} = useLocalSearchParams<{
    storeId: string;
    personId: string;
  }>();
  const safeArea = useSafeAreaInsets();
  const [{timezone}] = api.user.getStore.useSuspenseQuery({storeId: storeId!}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });

  const [personAvailability] = api.user.getPersonAvailabilityAdmin.useSuspenseQuery({
    storeId: storeId!,
    personId: personId!
  });
  const hasApprovedAvailability = personAvailability?.approved !== undefined;
  const hasPendingAvailability = personAvailability?.pending !== undefined;

  const [now] = React.useState(DateTime.now().setZone(timezone));
  const goBack = () => {
    router.dismissTo({
      pathname: "schedule/[storeId]/requests/availability",
      params: {
        storeId: storeId,
      }
    })
  };

  const apiUtil = api.useUtils();
  const submitPersonAvailabilityWeek = api.user.submitPersonAvailabilityWeekAdmin.useMutation();
  const approveAvailability = api.user.approveAvailabilityRequest.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getAvailabilityRequestsAdvanced.invalidate();
      await apiUtil.user.getAvailabilityRequests.invalidate();
      Toast.show("Availability approved", {
        position: Toast.positions.TOP
      });
      goBack();
    }
  });

  const form = useForm({
    defaultValues: {
      effectiveAt: now.toJSDate(),
      maxHoursPreferred: undefined as string | undefined,
      maxDaysPreferred: undefined as string | undefined,
    },
    onSubmit: async ({value}) => {
      const id = hasPendingAvailability ? personAvailability.pending!.id! : personAvailability.draft!.id!;

      if (!hasPendingAvailability) {
        await submitPersonAvailabilityWeek.mutateAsync({
          id: id,
          personId: personId!,
          storeId: storeId!,
          effectiveAt: formatDateNoTime(value.effectiveAt),
          maxHoursPreferred: value.maxHoursPreferred ? parseInt(value.maxHoursPreferred) : undefined,
          maxDaysPreferred: value.maxDaysPreferred ? parseInt(value.maxDaysPreferred) : undefined,
        })
      }

      await approveAvailability.mutateAsync({
        personAvailabilityId: id
      })
    },
    validators: {}
  });

  useAvoidKeyboard();

  const isLoading = submitPersonAvailabilityWeek.isPending || approveAvailability.isPending;

  const columns = [
    ...hasApprovedAvailability ? [personAvailability.approved as PersonAvailabilityDto] : [],
    ...(hasPendingAvailability ? [personAvailability.pending as PersonAvailabilityDto] : [personAvailability?.draft as PersonAvailabilityDto]),
  ]

  return (
    <View style={[{paddingBottom: Math.max(safeArea.bottom, 16)}, cn("bg-white flex flex-col flex-1")]}>
      <ScrollView style={cn("flex-1")}>
        <View style={cn("my-2 px-4 pt-2")}>
          <form.Field name={"effectiveAt"}
                      children={field => (
                        <View style={cn("mb-3")}>
                          <Text>Effective Date</Text>
                          <FormCalendarDatePicker
                            field={field}
                            placeholder="Select effective date..."
                            minimumDate={DateTime.now().toJSDate()}
                          />
                        </View>
                      )}
          />
          <AvailabilityWeekReview firstColumn={columns[0]}
                                  secondColumn={columns[1]}
                                  columnTitles={["Current", "Pending"]}/>

          <Divider/>

          <form.Field name={"maxHoursPreferred"}
                      validators={{
                        onSubmit: ({value}) => {
                          if (value && parseInt(value) > 90) {
                            return "Cannot be more than 90 hours"
                          }
                          return undefined
                        }
                      }}
                      children={field => (
                        <View style={cn("flex-1 flex flex-row items-center gap-2")}>
                          <View style={{flex: 2}}>
                            <Text semibold>Preferred Hours</Text>
                            <Text size={"xs"} muted>How many hours do you want to work this week?</Text>
                          </View>
                          <View style={{flex: 1}}>
                            <FormInput
                              isNumber={true}
                              field={field}
                              placeholder="Hours"
                              keyboardType="numeric"
                            />
                          </View>
                          <FieldInfo field={field}/>
                        </View>
                      )}
          />
          <Divider/>

          <form.Field name={"maxDaysPreferred"}
                      validators={{
                        onSubmit: ({value}) => {
                          if (value && parseInt(value) > 7) {
                            return "Cannot be more than 6 days"
                          }
                          return undefined
                        }
                      }}
                      children={field => (
                              <View style={cn("flex-1 flex flex-row items-center gap-2")}>
                                <View style={{flex: 2}}>
                                  <Text semibold>Preferred # of Days</Text>
                                  <Text size={"xs"} muted>How many days do you want to work this week?</Text>
                                </View>
                                <View style={{flex: 1}}>
                                  <FormInput
                                          isNumber={true}
                                          field={field}
                                          placeholder="Days"
                                          keyboardType="numeric"
                                  />
                                </View>
                                <FieldInfo field={field}/>
                              </View>
                      )}
          />

          <Divider/>
          <View>
            <Text size={"xs"} semibold>By submitting this availability, I confirm that the hours provided are accurate
              and cannot be changed once the scheduling window has closed. If adjustments are needed after the
              scheduling window is closed, you may request time off or arrange a shift swap with other employees.</Text>
          </View>
        </View>
      </ScrollView>
      <View style={[cn("px-6 pt-3 border-t border-gray-300")]}>
        <Button colorScheme={"primary"}
                isLoading={isLoading}
                isDisabled={isLoading}
                onPress={form.handleSubmit}
                style={cn("ml-2")}>
          Submit
        </Button>
      </View>
    </View>
  );
};
