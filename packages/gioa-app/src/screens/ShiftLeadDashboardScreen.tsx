import {useLocalSearchParams, useNavigation} from "expo-router";
import {Pressable, RefreshControl, ScrollView, StyleSheet, View} from "react-native";
import {Text} from "@/components/Text";
import {cn} from "@/style.util";
import {imageUrl} from "@/images";
import {colors} from "@/styles";
import React, {Suspense, useCallback, useRef, useState} from "react";
import {ImageBackground} from "expo-image";
import {api} from "@/api";
import {FancyMenuIconSvg} from "@/components/icons/FancyMenuIconSvg";
import {DrawerActions} from '@react-navigation/native';
import {useSafeAreaInsets} from "react-native-safe-area-context";
import Ionicons from "@expo/vector-icons/Ionicons";
import {ScreenLoadingIndicator} from "@/components/ScreenLoadingIndicator";
import {ShiftLeaderDashboard, ShiftLeaderDashboardRef} from "@/components/ShiftLeaderDashboard";
import {LinearGradient} from "expo-linear-gradient";
import {useShiftLeadDateStore} from "@/hooks/useShiftLeadDateStore";
import {ScrollViewColorContinuityDingus} from "@/components/ScrollViewColorContinuityDingus";
import {useNavigationPadding} from "@/hooks/useNavigationPadding";

export const ShiftLeadDashboardScreen = () => {
  const {storeId} = useLocalSearchParams<{ storeId: string; }>();
  const [store] = api.user.getStoreAdmin.useSuspenseQuery({storeId: storeId!}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });

  const navigation = useNavigation();

  const safeArea = useSafeAreaInsets();
  const {activeDate, setActiveDate} = useShiftLeadDateStore();

  const onNext = () => {
    const newActiveDate = activeDate.plus({days: 1});
    setActiveDate(newActiveDate);
  }
  const onPrev = () => {
    const newActiveDate = activeDate.minus({days: 1});
    setActiveDate(newActiveDate);
  }

  const headerHeight = safeArea.top + 120;

  const dashboardRef = useRef<ShiftLeaderDashboardRef>(null);
  const [refreshing, setRefreshing] = useState(false);
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    if (!dashboardRef.current) {
      setRefreshing(false);
      return;
    }
    dashboardRef.current?.refetch().then(() => {
      setRefreshing(false);
    });
  }, [dashboardRef]);

  const navigationPadding = useNavigationPadding();

  return (
          <View style={[cn("flex-1 bg-slate-100"), {position: "relative",}]}>
            <LinearGradient colors={[colors.gioaBlue, "#3E4B7E00"]}
                            style={[cn("absolute top-0 left-0 right-0 bottom-0"), {zIndex: 1000, height: safeArea.top}]}/>
            <ScrollView style={cn("flex-1")}
                        contentContainerStyle={{paddingBottom: navigationPadding}}
                        refreshControl={<RefreshControl style={{zIndex: 2000}}
                                                        refreshing={refreshing}
                                                        onRefresh={onRefresh}
                                                        tintColor={"white"}/>}
                        stickyHeaderIndices={[0]}
            >
              <View>
                <ImageBackground source={imageUrl(store.imageUrl, {height: headerHeight * 2})}
                                 contentFit={"cover"}
                                 style={[{overflow: "hidden"}, cn(""), {paddingTop: safeArea.top},
                                   {height: headerHeight, backgroundColor: "#474484"}]}>
                  <View style={[StyleSheet.absoluteFill, {backgroundColor: colors.gioaBlue, opacity: 0.7}]}/>
                  <LinearGradient colors={[colors.gioaBlue, "#3E4B7E00"]}
                                  style={[cn("absolute top-0 left-0 right-0 bottom-0"), {
                                    zIndex: 1000,
                                    height: safeArea.top
                                  }]}/>
                  <View style={cn("flex flex-row items-center gap-4")}>
                    <Pressable style={[cn("pt-2"), {flex: 0.1}]} onPressIn={() => {
                      navigation.dispatch(DrawerActions.toggleDrawer())
                    }}>
                      {({pressed}) =>
                              <View style={{paddingLeft: 20, paddingRight: 12, opacity: pressed ? 0.5 : 1}}>
                                <FancyMenuIconSvg fill={pressed ? "#fff" : "#fff"}/>
                              </View>
                      }
                    </Pressable>
                    <View style={[{flex: 0.8}, cn("mt-2")]}>
                      <Text bold size={"lg"} colorScheme={"light"} center>
                        {store.title}
                      </Text>
                    </View>
                    <View style={{flex: 0.1}}/>
                  </View>

                  <View style={cn("flex flex-row items-center justify-center gap-4 px-4 py-2")}>
                    <Pressable hitSlop={8} onPress={onPrev}>
                      <Ionicons name={"chevron-back-outline"} size={24} color={"white"}/>
                    </Pressable>
                    <Text colorScheme={"light"} bold size={"xl"}>
                      {activeDate.toLocaleString({
                        weekday: "long",
                        month: "short",
                        day: "numeric"
                      })}
                    </Text>
                    <Pressable hitSlop={8} onPress={onNext}>
                      <Ionicons name={"chevron-forward-outline"} size={24} color={"white"}/>
                    </Pressable>
                  </View>
                </ImageBackground>
              </View>
              <ScrollViewColorContinuityDingus color={colors.gioaBlue}/>

              <View style={[cn("flex-1 flex gap-4 bg-slate-100 rounded-t-xl pt-6"), {zIndex: 100, top: -30}]}>
                <Suspense fallback={<ScreenLoadingIndicator/>}>
                  <ShiftLeaderDashboard storeId={storeId!} ref={dashboardRef}
                                        activeDate={activeDate}/>
                </Suspense>
              </View>

            </ScrollView>
          </View>

  )
}
