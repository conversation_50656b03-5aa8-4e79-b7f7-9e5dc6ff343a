import {createFileRoute, Link as RouterLink, SearchSchemaInput} from '@tanstack/react-router'
import {api} from 'src/api'
import {Heading} from 'src/components/Heading'
import {Button} from 'src/components/ui/button'
import {DataTable} from 'src/components/ui/data-table'
import {CopyFromDialog} from '@/src/components/CopyFromDialog.tsx'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from 'src/components/ui/breadcrumb'
import {
  createColumnHelper,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  RowSelectionState,
  useReactTable,
} from '@tanstack/react-table'
import {Checkbox} from 'src/components/ui/checkbox'
import {DataTableColumnHeader} from 'src/components/DataTableColumnHeader'
import {Select, SelectContent, SelectItem, SelectSeparator, SelectTrigger, SelectValue} from 'src/components/ui/select'
import {useState} from 'react'
import {z} from 'zod'
import {toast} from 'sonner'
import {find, flatMap, map} from "lodash";
import {getHumanReadableErrorMessage} from "@/src/components/ErrorAlert.tsx";
import {CopyToDialog} from "@/src/components/CopyToDialog.tsx";

const templateSearchSchema = z.object({
  sorting: z.array(z.object({
    id: z.string(),
    desc: z.boolean(),
  })),
  pageIndex: z.number().int().nonnegative(),
  pageSize: z.number().int().nonnegative(),
  filters: z.array(z.object({
    id: z.string(),
    value: z.array(z.any())
  }))
});

type TemplateSearch = z.infer<typeof templateSearchSchema>;

export const Route = createFileRoute('/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/')({
  component: Component,
  validateSearch: (search: unknown & SearchSchemaInput): TemplateSearch => {
    if (templateSearchSchema.safeParse(search).success) {
      return search as any;
    }
    return {
      sorting: [{
        id: 'title',
        desc: false,
      }],
      pageIndex: 0,
      pageSize: 20,
      filters: []
    };
  },
})

const colHelper = createColumnHelper<any>();

const columns = [
  colHelper.display({
    id: 'select',
    header: ({table}) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({row}) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  }),
  colHelper.accessor('title', {
    header: ({column}) => {
      return <DataTableColumnHeader column={column} title="Name"/>
    },
  }),
  colHelper.accessor('description', {
    header: ({column}) => {
      return <DataTableColumnHeader column={column} title="Description"/>
    },
  }),
  colHelper.accessor('items', {
    header: ({column}) => {
      return <DataTableColumnHeader column={column} title="Items"/>
    },
    cell: ({getValue}) => {
      const items = getValue();
      return items?.length || 0;
    },
  }),
];

function Component() {
  const {businessId, storeId} = Route.useParams();
  const navigate = Route.useNavigate();
  const [fromStoreId, setFromStoreId] = useState<string>('');
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [isCopyToOpen, setIsCopyToOpen] = useState(false);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const [businesses] = api.admin.getBusinesses.useSuspenseQuery(undefined, {
    staleTime: 1000 * 60 * 60 * 24
  });
  // Current store templates
  const [templates] = api.admin.getStoreChecklistTemplates.useSuspenseQuery({
    businessId,
    storeId,
  });

  const exportTemplates = api.admin.exportStoreChecklistTemplates.useMutation();

  // Selected store templates
  const {data: fromTemplates} = api.admin.getStoreChecklistTemplates.useQuery({
    businessId,
    storeId: fromStoreId,
  }, {
    enabled: !!fromStoreId
  });

  const serializedSort = Route.useSearch()?.sorting;
  const initPageIndex = Route.useSearch()?.pageIndex;
  const initPageSize = Route.useSearch()?.pageSize;
  const columnFilters = Route.useSearch()?.filters;

  const sorting = serializedSort ?? [{
    id: 'title',
    desc: false,
  }];

  const table = useReactTable({
    data: templates?.items ?? [],
    columns,
    state: {
      sorting,
      pagination: {
        pageIndex: initPageIndex,
        pageSize: initPageSize
      },
      columnFilters,
      rowSelection,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onColumnFiltersChange: colFilterFn => {
      const newColumnFilters = typeof colFilterFn === 'function' ? colFilterFn(columnFilters) : colFilterFn;
      navigate({
        replace: true,
        search: (prev) => ({
          ...prev,
          filters: newColumnFilters
        })
      });
    },
    onSortingChange: sortingState => {
      const newSorting = typeof sortingState === 'function' ? sortingState(sorting) : sortingState;
      navigate({
        replace: true,
        search: (prev) => ({
          ...prev,
          sorting: newSorting
        })
      });
    },
    onPaginationChange: pagination => {
      const newPagination = typeof pagination === 'function' ? pagination({
        pageIndex: initPageIndex,
        pageSize: initPageSize
      }) : pagination;
      navigate({
        replace: true,
        search: (prev) => ({
          ...prev,
          pageIndex: newPagination.pageIndex,
          pageSize: newPagination.pageSize
        })
      });
    },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  const stores = flatMap(businesses, b => {
    return map(b.stores, s => ({...s, businessTitle: b.legalName}))
  }).filter(s => s.id !== storeId);
  const demoStore = find(stores, s => s.title.toLowerCase().includes('demo'));

  const handleOpenConfirm = () => {
    if (!fromStoreId) {
      toast.error('Please select a store to copy from');
      return;
    }
    setIsConfirmOpen(true);
  };

  const apiUtil = api.useUtils();
  const selectedStore = stores.find(s => s.id === fromStoreId);
  const currentBusiness = businesses?.find(b => b.id === businessId);
  const currentStore = currentBusiness?.stores?.find(s => s.id === storeId);

  const onExport = () => {
    exportTemplates.mutate({
      businessId,
      storeId
    }, {
      onSuccess: (templates) => {
        const blob = new Blob([JSON.stringify(templates, null, 2)], {type: "application/json"});
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `templates-${storeId}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      },
      onError: (error) => {
        toast.error(getHumanReadableErrorMessage(error))
      }
    })
  }

  return (
    <div className="space-y-4">
      <Breadcrumb className="mb-3">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <RouterLink from={Route.fullPath} to="/admin/businesses">
                Businesses
              </RouterLink>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator/>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <RouterLink from={Route.fullPath} to={`/admin/businesses/${businessId}`}>
                {currentBusiness?.legalName}
              </RouterLink>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator/>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <RouterLink from={Route.fullPath} to={`/admin/businesses/${businessId}/stores/${storeId}`}>
                {currentStore?.title}
              </RouterLink>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator/>
          <BreadcrumbItem>
            Checklist Templates
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <Heading level={1}>Checklist Templates</Heading>
      </div>

      <div className="flex items-center gap-4">
        <Select value={fromStoreId} onValueChange={setFromStoreId}>
          <SelectTrigger className="w-[280px]">
            <SelectValue placeholder="Select store to copy from"/>
          </SelectTrigger>
          <SelectContent>
            {demoStore ? <SelectItem key={demoStore.id} value={demoStore.id}>
              {demoStore.title}
            </SelectItem> : null}
            <SelectSeparator/>
            {stores.map(store => (
              <SelectItem key={store.id} value={store.id}>
                {store.businessTitle} - {store.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Button
          onClick={handleOpenConfirm}
          disabled={!fromStoreId}
        >
          Copy Templates
        </Button>

        <div className={"grow"}></div>

        <Button
          onClick={() => setIsCopyToOpen(true)}
          disabled={Object.keys(rowSelection).length === 0}
          className="mr-2"
        >
          Copy To ({Object.keys(rowSelection).length} selected)
        </Button>
        <Button onClick={onExport} disabled={exportTemplates.isPending}>
          Export
        </Button>
      </div>

      <DataTable
        table={table}
        rowDetailLinkFrom={Route.fullPath}
      />

      <CopyFromDialog
        open={isConfirmOpen}
        onOpenChange={setIsConfirmOpen}
        businessId={businessId}
        storeId={storeId}
        fromStoreId={fromStoreId}
        fromTemplates={fromTemplates?.items ?? []}
        selectedStore={selectedStore}
        onSuccess={() => {
          apiUtil.admin.getStoreChecklistTemplates.invalidate();
          setFromStoreId('');
        }}
      />

      <CopyToDialog
        open={isCopyToOpen}
        onOpenChange={setIsCopyToOpen}
        stores={stores}
        businessId={businessId}
        storeId={storeId}
        templates={(templates?.items ?? []).filter((_, index) => rowSelection[index])}
        onSuccess={() => {
          apiUtil.admin.getStoreChecklistTemplates.invalidate();
        }}
      />
    </div>
  )
}
