import {api} from "@/src/api";
import {createFileRoute, useNavigate, SearchSchemaInput} from "@tanstack/react-router";
import {DataTable} from "@/src/components/ui/data-table.tsx";
import {
    ColumnFiltersState,
    createColumnHelper,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    SortingState,
    useReactTable,
} from "@tanstack/react-table";
import {Button} from "@/src/components/ui/button";
import {ScheduleEventDto} from "@gioa/api/src/scheduleEventSchemas";
import {DateTime, WeekdayNumbers} from "luxon";
import {EditEventModal} from "@/src/components/EditEventModal";
import {ScheduleCalendars, ScheduleEventType} from "@gioa/api/src/scheduleCalendars";
import {Text} from "@/src/components/Text.tsx";
import React, {useMemo, useState} from "react";
import {useForm} from "@tanstack/react-form";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {PlusIcon, SearchIcon} from "lucide-react";
import {DataTableColumnHeader} from "@/src/components/DataTableColumnHeader.tsx";
import {IsoCompleteDate} from "../../../../../../../../../api/src/timeSchemas.ts";
import {genScheduleEventId} from "../../../../../../../../../api/src/schemas.ts";
import {Edit, ChevronDown} from "lucide-react";
import {Badge} from "@/src/components/ui/badge.tsx";
import {FilterEventsFormValues, FilterEventTypesPopover} from "@/src/components/FilterEventsPopover.tsx";
import {getDateRangeForTimeFrame} from "@/src/utils/dateRange.util.ts";
import {CustomDateFilter, TimeFrameFilter} from "@/src/components/CustomDateFilter.tsx";
import {z} from "zod";

const sharedDateFilterSchema = z.object({
  week: z.number().int().min(1).max(53).optional(),
  year: z.number().int().min(2000).max(9999).optional(),
  day: z.number().int().min(1).max(7).optional(),
  month: z.number().int().min(1).max(12).optional(),
  timeFrame: z.enum(['day', 'week', 'month', 'custom']).optional(),
  startDate: z.string().optional(), // ISO date string for custom range
  endDate: z.string().optional(),   // ISO date string for custom range
}).optional();

type DateFilterSearch = z.infer<typeof sharedDateFilterSchema>;

export const Route = createFileRoute("/_signedin/$businessId/$storeId/_nav/store/events/")({
  component: RouteComponent,
  validateSearch: (search: unknown & SearchSchemaInput): DateFilterSearch | undefined => {
    if (sharedDateFilterSchema.safeParse(search).success) {
      return search as any;
    }
  },
});

interface EventsFilterFormValues {
    searchInput: string;
    startDate?: Date;
    endDate?: Date;
}

function RouteComponent() {
  const {storeId} = Route.useParams();
  const searchParams = Route.useSearch();
  const navigate = useNavigate();
  const [store] = api.user.getStoreAdmin.useSuspenseQuery(
          {storeId: storeId!},
          {
            staleTime: 1000 * 60 * 60, // 1 hour
          },
  );
  const timezone = store.timezone;
  const [today] = useState(DateTime.now().setZone(timezone));

  const timeFrame: TimeFrameFilter = (searchParams?.timeFrame as TimeFrameFilter) ?? 'custom';
  const isoDateSearch: IsoCompleteDate = searchParams?.week && searchParams?.year ? {
    week: searchParams.week,
    year: searchParams.year,
    day: searchParams.day,
    month: searchParams.month
  } : {
    week: today.weekNumber,
    year: today.weekYear,
    day: today.weekday,
    month: today.month,
  };

  // Custom date range logic - create dates in local timezone to avoid off-by-one issues
  // Default to past 2 weeks only on first visit (when timeFrame is custom but no URL params exist at all)
  const hasAnySearchParams = Object.keys(searchParams || {}).length > 0;
  const isFirstVisit = timeFrame === 'custom' && !hasAnySearchParams;

  let customStartDate: IsoCompleteDate | null = null;
  let customEndDate: IsoCompleteDate | null = null;

  if (searchParams?.startDate) {
    // Parse as calendar date (YYYY-MM-DD) without timezone interpretation
    const [year, month, day] = searchParams.startDate.split('-').map(Number);
    const dt = DateTime.fromObject({ year, month, day });
    customStartDate = { year: dt.weekYear, month: month, day: dt.weekday, week: dt.weekNumber };
  } else if (isFirstVisit) {
    const startDateTime = today.minus({weeks: 2});
    customStartDate = { year: startDateTime.weekYear, month: startDateTime.month, day: startDateTime.weekday, week: startDateTime.weekNumber };
  }

  if (searchParams?.endDate) {
    // Parse as calendar date (YYYY-MM-DD) without timezone interpretation
    const [year, month, day] = searchParams.endDate.split('-').map(Number);
    const dt = DateTime.fromObject({ year, month, day });
    customEndDate = { year: dt.weekYear, month: month, day: dt.weekday, week: dt.weekNumber };
  } else if (isFirstVisit) {
    customEndDate = { year: today.weekYear, month: today.month, day: today.weekday, week: today.weekNumber };
  }

  const dateRange = getDateRangeForTimeFrame(isoDateSearch, timeFrame, timezone, customStartDate, customEndDate);

  const handleDateChange = (date: IsoCompleteDate, timeFrame: TimeFrameFilter, customStart?: IsoCompleteDate | null, customEnd?: IsoCompleteDate | null) => {
    // Convert IsoCompleteDate to calendar date string for URL
    const convertToDateString = (isoDate: IsoCompleteDate) => {
      const dt = DateTime.fromObject({
        weekYear: isoDate.year,
        weekNumber: isoDate.week,
        weekday: (isoDate.day ?? 1) as WeekdayNumbers
      }).set({ month: isoDate.month });
      return dt.toFormat('yyyy-MM-dd');
    };

    const startDateString = customStart ? convertToDateString(customStart) : undefined;
    const endDateString = customEnd ? convertToDateString(customEnd) : undefined;

    navigate({
      from: Route.fullPath,
      search: {
        ...searchParams,
        week: date.week,
        year: date.year,
        day: date.day,
        month: date.month,
        timeFrame: timeFrame,
        startDate: startDateString,
        endDate: endDateString,
      }
    });
  };

  const [events] = api.user.getScheduleEvents.useSuspenseQuery({
    range: dateRange,
    storeId: storeId!
  });

    const [isOpen, setIsOpen] = useState(false);
    const [selectedEvent, setSelectedEvent] = useState<ScheduleEventDto | undefined>(undefined);
    const [searchInput, setSearchInput] = useState("");
    const [startDate, setStartDate] = useState<Date | undefined>(undefined);
    const [endDate, setEndDate] = useState<Date | undefined>(undefined);

    const form = useForm<EventsFilterFormValues>({
        defaultValues: {
            searchInput: "",
            startDate: undefined,
            endDate: undefined,
        },
        onSubmit: ({value}) => {
            setSearchInput(value.searchInput);
            setStartDate(value.startDate);
            setEndDate(value.endDate);
        },
    });

    // Table state
    const [sorting, setSorting] = useState<SortingState>([{id: "start", desc: true}]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

    const [filterValues, setFilterValues] = React.useState<FilterEventsFormValues>({eventTypes: []});
    const defaultFilterValues: FilterEventsFormValues = {
        eventTypes: [],
    }

    // Filter events
    const filteredEvents = useMemo(() => {
        let filtered = events;

        // Filter by search input
        if (searchInput) {
            const searchLower = searchInput.toLowerCase();
            filtered = filtered.filter(
                (event) =>
                    event.title.toLowerCase().includes(searchLower) ||
                    (event.description?.toLowerCase().includes(searchLower) ?? false),
            );
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter((event) => new Date(event.range.start) >= startDate);
        }

        if (endDate) {
            filtered = filtered.filter((event) => new Date(event.range.end) <= endDate);
        }

        // Filter by event type
        if (filterValues.eventTypes.length) {
            filtered = filtered.filter((event) =>
                filterValues.eventTypes.includes(event.eventType as ScheduleEventType),
            );
        }

        return filtered;
    }, [events, searchInput, startDate, endDate, filterValues]);

    const colHelper = createColumnHelper<ScheduleEventDto>();

    const handleAddEvent = (
        () => {
            setSelectedEvent(undefined);
            setIsOpen(true);
        }
    )

    const handleEditEvent = (
        (event: ScheduleEventDto) => {
            setSelectedEvent(event);
            setIsOpen(true);
        }
    );

    const columns = useMemo(
            () => [
                colHelper.accessor("title", {
                    header: ({column}) => <DataTableColumnHeader column={column} title="Title"/>,
                    cell: (info) => info.getValue(),
                }),

                colHelper.accessor("range.start", {
                    header: ({column}) => <DataTableColumnHeader column={column} title="Start Date"/>,
                    id: "start",
                    cell: (info) =>
                        <div className="font-semibold">
                            {info.getValue().toLocaleDateString()}
                            <div className="text-gray-400 font-normal">
                                {info.getValue().toLocaleTimeString("en-US", {
                                    hour: "numeric",
                                    minute: "2-digit",
                                    hour12: true
                                })}
                            </div>
                        </div>,
                }),

                colHelper.accessor("range.end", {
                    header: ({column}) => <DataTableColumnHeader column={column} title="End Date"/>,
                    cell: (info) =>
                        <div className="font-semibold">
                            {info.getValue().toLocaleDateString()}
                            <div className="text-gray-400 font-normal">
                                {info.getValue().toLocaleTimeString("en-US", {
                                    hour: "numeric",
                                    minute: "2-digit",
                                    hour12: true
                                })}
                            </div>
                        </div>,
                }),

                colHelper.accessor("visibilityLevel", {
                    header: ({column}) => <DataTableColumnHeader column={column} title="Visibility"/>,
                    cell: (info) => {
                        const level = info.getValue();
                        const visibility =
                            level === 2 ? "Scheduler" :
                                level === 1 ? "Leadership" :
                                    level === 0 ? "Everyone" :
                                        "--";

                        return <div className="font-semibold">
                            {visibility}
                        </div>;
                    },
                }),

                colHelper.accessor("eventType", {
                    header: ({column}) => <DataTableColumnHeader column={column} title="Calendar"/>,
                    cell: (info) => {
                        const eventType = info.getValue() as string;
                        const calendar = ScheduleCalendars[eventType] ?? ScheduleCalendars.other;

                        return (
                            <div
                                className="inline-block border-2 rounded-lg px-2 py-1 font-semibold"
                                style={{
                                    borderColor: calendar.color,
                                    color: calendar.color,
                                }}
                            >
                                {calendar.title}
                            </div>
                        );
                    },
                }),

                colHelper.display({
                    id: "edit",
                    header: () => null,
                    cell: ({row}) => (
                        <Button variant="ghost" size="icon" onClick={() => handleEditEvent(row.original)}
                                title="Edit Event">
                            <Edit size={18} className="text-gray-500"/>
                        </Button>
                    ),
                }),

            ],
            [timezone],
        )
    ;

    const table = useReactTable({
        data: filteredEvents,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        onSortingChange: setSorting,
        getSortedRowModel: getSortedRowModel(),
        onColumnFiltersChange: setColumnFilters,
        getFilteredRowModel: getFilteredRowModel(),
        state: {
            sorting,
            columnFilters,
        },
    });

    return (
        <div className="rounded-xl border border-gray-200 bg-white p-4 w-full flex flex-col justify-start gap-4">
            <div className="flex flex-row justify-between items-center">
                <Text className="text-3xl" colorScheme={"dark"} semibold>
                    Events
                </Text>
                <Button variant={"outline"} rightIcon={<PlusIcon size={16}/>} onClick={handleAddEvent}>
                    New Event
                </Button>
            </div>

            {/* Filter form */}
            <div className="flex flex-col gap-4">
                <div className="flex flex-row justify-between">
                    <div className="flex flex-row gap-3">
                      <CustomDateFilter
                              timezone={store.timezone ?? "America/New_York"}
                              timeRange={timeFrame}
                              selectedDate={isoDateSearch}
                              customStartDate={customStartDate}
                              customEndDate={customEndDate}
                              onDateChange={handleDateChange}
                      />
                        <div className="flex flex-row gap-1">
                            <FilterEventTypesPopover initialValues={filterValues}
                                                     defaultValues={defaultFilterValues}
                                                     onSubmit={setFilterValues}>
                                <div className={"flex-1 relative"}>
                                    <Button variant={"outline"} className={"w-full"} rightIcon={<ChevronDown size={18}/>}>
                                      Filter
                                    </Button>
                                    {filterValues.eventTypes.length > 0 ?
                                        <div className={"absolute"} style={{top: -3, right: -5}}>
                                            <Badge size={"sm"} colorScheme={"destructive"}
                                                   className={"min-h-5 min-w-5 ml-2 px-1 flex items-center justify-center"}>
                                                {filterValues.eventTypes.length}
                                            </Badge>
                                        </div> : null}
                                </div>
                            </FilterEventTypesPopover>
                        </div>
                    </div>
                    <div className="">
                        <form.Field name="searchInput">
                            {(field) => (
                                <FormInput
                                    field={field}
                                    placeholder="Search..."
                                    leftIcon={SearchIcon}
                                    onChange={(e) => {
                                        form.handleSubmit();
                                    }}
                                />
                            )}
                        </form.Field>
                    </div>
                </div>
            </div>

            <DataTable
                table={table}
                rowDetailLinkFrom={Route.fullPath}
                EmptyComponent={<Text muted>No events found</Text>}
            />

            <EditEventModal
                storeId={storeId}
                event={selectedEvent}
                isOpen={isOpen}
                onOpenChange={setIsOpen}
            />
        </div>
    );
}
