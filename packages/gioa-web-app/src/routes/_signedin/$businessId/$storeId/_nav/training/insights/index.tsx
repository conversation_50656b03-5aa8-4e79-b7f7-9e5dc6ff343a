import {createFileRoute} from '@tanstack/react-router'
import {api} from "@/src/api.ts";
import React, {useState, useMemo, Suspense} from "react";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/src/components/ui/table.tsx";
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu.tsx";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select.tsx";
import {Text} from "@/src/components/Text.tsx";
import {TeamMemberCombobox} from "@/src/components/TeamMemberCombobox.tsx";
import {ComboBox} from "@/src/components/ComboBox.tsx";
import {TrainingStatusModal} from "@/src/components/TrainingStatusModal.tsx";
import {ProficiencyRatingModal} from "@/src/components/ProficiencyRatingModal.tsx";
import { map, sortBy, filter, orderBy, find, flatMap, forEach, size } from 'lodash';
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {ProficiencyRating} from "@/src/components/ProficiencyRating.tsx";
import {LaborStatusIcon} from "@/src/components/LaborStatusIcon.tsx";
import {getPersonLaborStatus} from "../../../../../../../../../api/src/personMetrics.ts";
import {hasPermissionPackage} from "../../../../../../../../../api/src/authorization.util.ts";
import {toast} from "sonner";

export const Route = createFileRoute('/_signedin/$businessId/$storeId/_nav/training/insights/')({
  component: () => (
    <Suspense fallback={<div>Loading...</div>}>
      <Insights />
    </Suspense>
  ),
})

// Extend ColumnMeta to include sticky property
declare module '@tanstack/react-table' {
  interface ColumnMeta<TData, TValue> {
    sticky?: 'left' | 'right';
  }
}

const columnHelper = createColumnHelper<any>();

// Helper functions are now imported from shared utilities

function Insights() {
  const {storeId} = Route.useParams();

  // Get employees for the combobox
  const [storeData] = api.user.getStore.useSuspenseQuery({storeId: storeId!}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });

  // State for filters
  const [employeeFilter, setEmployeeFilter] = useState<string>("all");
  const [selectedAreaId, setSelectedAreaId] = useState<string | undefined>(undefined);
  const [selectedPositionId, setSelectedPositionId] = useState<string | undefined>(undefined);
  const [sorting, setSorting] = useState<SortingState>([]);

  // Training status modal state
  const [trainingModal, setTrainingModal] = useState<{
    isOpen: boolean;
    personId?: string;
    personName?: string;
    positionId?: string;
    positionTitle?: string;
    areaTitle?: string;
    currentStatus?: boolean;
  }>({
    isOpen: false,
  });

  // Proficiency rating modal state
  const [proficiencyModal, setProficiencyModal] = useState<{
    isOpen: boolean;
    personId?: string;
    personName?: string;
    currentRating?: number;
  }>({
    isOpen: false,
  });

  // Fetch team member training data
  const trainingData = api.user.getTeamMemberTrainingData.useQuery({
    storeId,
    personId: employeeFilter !== "all" ? employeeFilter : undefined,
  });

  // Get user and check permissions
  const [user] = api.user.getUserProfile.useSuspenseQuery();
  const hasCreateTrainingPermission = hasPermissionPackage(user, "createTraining");
  const hasCreateProficiencyScoresPermission = hasPermissionPackage(user, "createProficiencyScores");

  // Frontend sorting function using lodash consistently
  const sortPeopleData = useMemo(() => {
    if (!trainingData.data?.people || !sorting[0]) {
      // Default sort: alphabetical by last name, first name
      return orderBy(trainingData.data?.people ?? [], ['lastName', 'firstName'], ['asc', 'asc']);
    }

    const people = trainingData.data.people;
    const sortColumn = sorting[0].id;
    const isDesc = sorting[0].desc;

    if (sortColumn === 'teamMember') {
      // Sort by proficiency ranking first, then alphabetically
      return orderBy(people, [
        // Primary sort: proficiency ranking
        (person: any) => person.proficiencyRanking || 0,
        // Secondary sort: alphabetical by name
        'lastName',
        'firstName'
      ], [isDesc ? 'desc' : 'asc', 'asc', 'asc']);
    } else if (sortColumn.startsWith('status-')) {
      // Sort by training completion status (handles dynamic status column IDs)
      // Use the same filtering logic as the status column calculation
      const currentAreasToShow = selectedAreaId
        ? filter(trainingData.data.storeAreas, area => area.id === selectedAreaId)
        : trainingData.data.storeAreas;

      const currentVisiblePositions = flatMap(currentAreasToShow, area => {
        return selectedPositionId
          ? filter(area.positions, position => position.id === selectedPositionId)
          : area.positions;
      });

      return orderBy(people, [
        // Primary sort: completion percentage for visible positions
        (person: any) => {
          const completedCount = size(filter(currentVisiblePositions, pos =>
            trainingData.data?.trainingMatrix[person.id]?.[pos.id]?.isCompleted
          ));
          return size(currentVisiblePositions) > 0 ? completedCount / size(currentVisiblePositions) : 0;
        },
        // Secondary sort: alphabetical by name
        'lastName',
        'firstName'
      ], [isDesc ? 'desc' : 'asc', 'asc', 'asc']);
    } else {
      // Check if it's a position column (format: areaId-positionId)
      const allPositions = flatMap(trainingData.data.storeAreas, area => area.positions);
      const position = find(allPositions, pos => {
        const columnId = `${pos.areaId}-${pos.id}`;
        return columnId === sortColumn;
      });

      if (position) {
        // Sort by specific position training completion using orderBy
        return orderBy(people, [
          // Primary sort: training completion status (boolean sorts false before true)
          (person: any) => trainingData.data?.trainingMatrix[person.id]?.[position.id]?.isCompleted || false,
          // Secondary sort: alphabetical by last name, first name
          'lastName',
          'firstName'
        ], [isDesc ? 'desc' : 'asc', 'asc', 'asc']);
      }
    }

    // Fallback: default alphabetical sort
    return orderBy(people, ['lastName', 'firstName'], ['asc', 'asc']);
  }, [trainingData.data, sorting]);

  // Use the sorted people data
  const tableData = sortPeopleData;

  // Modal handlers
  const openTrainingModal = (personId: string, personName: string, positionId: string, positionTitle: string, areaTitle: string, currentStatus: boolean) => {
    setTrainingModal({
      isOpen: true,
      personId,
      personName,
      positionId,
      positionTitle,
      areaTitle,
      currentStatus,
    });
  };

  const closeTrainingModal = () => {
    setTrainingModal({ isOpen: false });
  };

  // API mutations
  const updateTraining = api.user.upsertPositionTrainingHistory.useMutation();
  const updateProficiencyRating = api.user.editEmployeeProficiencyRanking.useMutation();
  const apiUtil = api.useUtils();

  const handleTrainingStatusSave = async (isCompleted: boolean) => {
    if (!trainingModal.personId || !trainingModal.positionId) return;

    try {
      await updateTraining.mutateAsync({
        personId: trainingModal.personId,
        storeId,
        storePositionId: trainingModal.positionId,
        isCompleted,
        isPersonPreferred: false, // Default to false since we're not managing preferences here
      });

      // Invalidate queries to refresh the data
      apiUtil.user.getTeamMemberTrainingData.invalidate();

      closeTrainingModal();
    } catch (error) {
      console.error('Failed to update training status:', error);
      toast.error("Failed to update training status. Please try again.", {
        position: "top-center",
      });
    }
  };

  // Proficiency modal handlers
  const openProficiencyModal = (personId: string, personName: string, currentRating: number) => {
    setProficiencyModal({
      isOpen: true,
      personId,
      personName,
      currentRating,
    });
  };

  const closeProficiencyModal = () => {
    setProficiencyModal({ isOpen: false });
  };

  const handleProficiencyRatingSave = async (rating: number) => {
    if (!proficiencyModal.personId) return;

    try {
      await updateProficiencyRating.mutateAsync({
        personId: proficiencyModal.personId,
        storeId,
        ranking: rating,
      });

      // Invalidate queries to refresh the data
      apiUtil.user.getTeamMemberTrainingData.invalidate();

      closeProficiencyModal();
    } catch (error) {
      console.error('Failed to update proficiency rating:', error);
      toast.error("Failed to update proficiency rating. Please try again.", {
        position: "top-center",
      });
    }
  };

  // Pre-calculate training statistics for better performance
  const trainingStats = useMemo(() => {
    if (!trainingData.data) return new Map();

    const stats = new Map();
    forEach(trainingData.data.storeAreas, area => {
      forEach(area.positions, position => {
        const totalPeople = size(trainingData.data?.people) || 0;
        const trainedCount = size(filter(trainingData.data?.people || [], person =>
          trainingData.data?.trainingMatrix[person.id]?.[position.id]?.isCompleted
        ));
        stats.set(position.id, { totalPeople, trainedCount });
      });
    });
    return stats;
  }, [trainingData.data]);

  // Create table columns dynamically based on training data
  const columns = useMemo(() => {
    if (!trainingData.data) return [];

    const cols = [
      // Team Member column
      columnHelper.accessor(row => row.fullName, {
        id: "teamMember",
        enableSorting: true,
        sortingFn: (rowA, rowB) => {
          const a = rowA.original;
          const b = rowB.original;

          // Primary sort: proficiency ranking (higher rating first for desc, lower first for asc)
          const aRating = a.proficiencyRanking || 0;
          const bRating = b.proficiencyRanking || 0;

          if (aRating !== bRating) {
            return aRating - bRating; // This will sort 0, 1, 2, 3 (asc) or 3, 2, 1, 0 (desc)
          }

          // Secondary sort: alphabetical by last name, first name
          const lastNameCompare = (a.lastName || '').localeCompare(b.lastName || '');
          if (lastNameCompare !== 0) return lastNameCompare;
          return (a.firstName || '').localeCompare(b.firstName || '');
        },
        header: ({ column }) => {
          const isSorted = column.getIsSorted();
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className={`w-full text-left p-2 rounded transition-colors ${
                  isSorted ? 'bg-blue-50 hover:bg-blue-100' : 'hover:bg-gray-50'
                }`}>
                  <span className="font-medium">Team Member</span>
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start">
                <DropdownMenuItem onClick={() => column.toggleSorting(true)}>
                  Most Proficient First
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => column.toggleSorting(false)}>
                  Least Proficient First
                </DropdownMenuItem>
                {isSorted && (
                  <DropdownMenuItem onClick={() => column.clearSorting()}>
                    Clear sorting
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
        cell: ({ row }) => {
          const person = row.original;
          const laborStatus = person.age ? getPersonLaborStatus(person.age) : null;

          const handleClick = () => {
            openProficiencyModal(
              person.id,
              `${person.firstName} ${person.lastName}`,
              person.proficiencyRanking || 0
            );
          };

          return (
            <button
              onClick={hasCreateProficiencyScoresPermission ? handleClick : undefined}
              className={`flex flex-row gap-2 items-center w-full text-left p-2 rounded transition-colors ${
                hasCreateProficiencyScoresPermission
                  ? 'hover:bg-gray-50 cursor-pointer'
                  : 'cursor-default'
              }`}
            >
              <PersonAvatar person={person} />
              <div>
                <div className="flex flex-row gap-2 items-center">
                  <Text className="font-medium">{person.firstName} {person.lastName}</Text>
                  {laborStatus && <LaborStatusIcon laborStatus={laborStatus} />}
                </div>
                {person.proficiencyRanking !== null && person.proficiencyRanking !== undefined && person.proficiencyRanking > 0 && (
                  <div className="mt-1">
                    <ProficiencyRating rank={person.proficiencyRanking} size={14} />
                  </div>
                )}
              </div>
            </button>
          );
        },
        size: 280,
        meta: {
          sticky: 'left',
        },
      }),
    ];

    // Add columns for each position grouped by store area
    // Filter by selected area and/or position
    const areasToShow = selectedAreaId
      ? filter(trainingData.data.storeAreas, area => area.id === selectedAreaId)
      : trainingData.data.storeAreas;

    forEach(areasToShow, area => {
      const positionsToShow = selectedPositionId
        ? filter(area.positions, position => position.id === selectedPositionId)
        : area.positions;

      forEach(positionsToShow, position => {
        cols.push(
          columnHelper.accessor(row => {
            const trainingStatus = trainingData.data?.trainingMatrix[row.id]?.[position.id];
            return trainingStatus?.isCompleted || false;
          }, {
            id: `${area.id}-${position.id}`,
            header: ({ column }) => {
              // Get pre-calculated training statistics
              const stats = trainingStats.get(position.id) || { totalPeople: 0, trainedCount: 0 };
              const { totalPeople, trainedCount } = stats;

              const isSorted = column.getIsSorted();

              return (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className={`w-full text-center p-2 rounded transition-colors ${
                      isSorted ? 'bg-blue-50 hover:bg-blue-100' : 'hover:bg-gray-50'
                    }`}>
                      <div className="font-medium text-sm">{position.title}</div>
                      <div className="font-normal text-xs text-gray-500 mt-0.5">{area.title}</div>
                      <div className="font-normal text-xs text-gray-400 mt-0.5">
                        {trainedCount}/{totalPeople}
                      </div>
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="center">
                    <DropdownMenuItem onClick={() => column.toggleSorting(true)}>
                      Trained first
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => column.toggleSorting(false)}>
                      Not trained first
                    </DropdownMenuItem>
                    {isSorted && (
                      <DropdownMenuItem onClick={() => column.clearSorting()}>
                        Clear sorting
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              );
            },
            cell: ({ getValue, row }) => {
              const isCompleted = getValue();
              const person = row.original;

              const handleClick = () => {
                openTrainingModal(
                  person.id,
                  `${person.firstName} ${person.lastName}`,
                  position.id,
                  position.title,
                  area.title,
                  isCompleted
                );
              };

              return (
                <div className="flex justify-center">
                  <button
                    onClick={hasCreateTrainingPermission ? handleClick : undefined}
                    className={`w-8 h-8 rounded-full flex items-center justify-center transition-transform ${
                      hasCreateTrainingPermission
                        ? 'hover:scale-110 cursor-pointer'
                        : 'cursor-default'
                    }`}
                  >
                    {isCompleted ? (
                      <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                        <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" strokeWidth="2.5" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    ) : (
                      <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center">
                        <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" strokeWidth="2.5" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </div>
                    )}
                  </button>
                </div>
              );
            },
            size: 180,
          })
        );
      });
    });

    // Add Status column at the end
    cols.push(
      columnHelper.accessor((row) => {
        if (!trainingData.data) return "0/0";

        // Recalculate visible positions inside accessor to force re-render
        const currentAreasToShow = selectedAreaId
          ? filter(trainingData.data.storeAreas, area => area.id === selectedAreaId)
          : trainingData.data.storeAreas;

        const currentVisiblePositions = flatMap(currentAreasToShow, area => {
          return selectedPositionId
            ? filter(area.positions, position => position.id === selectedPositionId)
            : area.positions;
        });

        const completedCount = size(filter(currentVisiblePositions, position =>
          trainingData.data?.trainingMatrix[row.id]?.[position.id]?.isCompleted
        ));

        return `${completedCount}/${size(currentVisiblePositions)}`;
      }, {
        id: `status-${selectedAreaId || 'all'}-${selectedPositionId || 'all'}`,
        header: ({ column }) => {
          const isSorted = column.getIsSorted();
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className={`w-full text-center p-2 rounded transition-colors ${
                  isSorted ? 'bg-blue-50 hover:bg-blue-100' : 'hover:bg-gray-50'
                }`}>
                  <span className="font-medium">Status</span>
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center">
                <DropdownMenuItem onClick={() => column.toggleSorting(true)}>
                  Descending
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => column.toggleSorting(false)}>
                  Ascending
                </DropdownMenuItem>
                {isSorted && (
                  <DropdownMenuItem onClick={() => column.clearSorting()}>
                    Clear sorting
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
        cell: ({ getValue }) => {
          const statusText = getValue();
          return (
            <div className="flex justify-center">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
                {statusText}
              </span>
            </div>
          );
        },
        size: 120,
        meta: {
          sticky: 'right',
        },
      })
    );

    return cols;
  }, [trainingData.data, selectedAreaId, selectedPositionId, hasCreateTrainingPermission, hasCreateProficiencyScoresPermission]);

  // Create table instance for training matrix with client-side sorting
  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  });

  return (
    <div className="rounded-xl border border-gray-200 bg-white p-4 w-full flex flex-col justify-start">
      {/* Header */}
      <div className="flex flex-row justify-between items-center mb-4">
        <Text className="text-3xl" colorScheme={"dark"} semibold>
          Team Member Training
        </Text>
      </div>

      {/* Date Filter and Employee Filter */}
      <div className="bg-gray-50 rounded-lg border border-gray-200 p-4 mb-4">
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-start">
          {/* Employee Search */}
          <div className="min-w-0 lg:min-w-[200px]">
            <TeamMemberCombobox
              people={[
                {
                  id: "all",
                  firstName: "All",
                  lastName: "team members",
                  profileImageUrl: undefined,
                  jobTitle: "",
                  storeIds: []
                } as any,
                ...sortBy(
                  filter(storeData.employees, employee => !employee.isArchived),
                  ['lastName', 'firstName']
                )
              ]}
              value={employeeFilter}
              onValueChange={(personId) => {
                setEmployeeFilter(personId);
              }}
              className="w-full"
            />
          </div>

          <div className="min-w-0 lg:min-w-[200px]">
            <Select
              value={selectedAreaId || "all"}
              onValueChange={(value) => {
                const newAreaId = value === "all" ? undefined : value;
                setSelectedAreaId(newAreaId);

                // Reset position filter if the selected position is not in the new area
                if (selectedPositionId && newAreaId) {
                  const selectedArea = find(trainingData.data?.storeAreas, area => area.id === newAreaId);
                  const positionExistsInArea = selectedArea && find(selectedArea.positions, pos => pos.id === selectedPositionId);
                  if (!positionExistsInArea) {
                    setSelectedPositionId(undefined);
                  }
                } else if (!newAreaId) {
                  // If switching to "All areas", keep the position filter as is
                }
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select area" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  <span className="font-medium text-sm">All areas</span>
                </SelectItem>
                {map(trainingData.data?.summary.areas, (area) => (
                  <SelectItem key={area.areaId} value={area.areaId}>
                    <span className="font-medium text-sm">{area.areaTitle}</span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="min-w-0 lg:min-w-[250px]">
            <ComboBox
              items={[
                {
                  id: "all",
                  title: "All positions",
                  subtitle: "",
                },
                ...flatMap(
                  selectedAreaId
                    ? filter(trainingData.data?.storeAreas, area => area.id === selectedAreaId)
                    : trainingData.data?.storeAreas,
                  area =>
                    map(area.positions, position => ({
                      id: position.id,
                      title: position.title,
                      subtitle: area.title,
                    }))
                ) || []
              ]}
              value={selectedPositionId || "all"}
              onValueChange={(value: string) => {
                setSelectedPositionId(value === "all" ? undefined : value);
              }}
              placeholder="Select position"
              searchPlaceholder="Search positions..."
              emptyMessage="No position found."
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Training Summary Cards - Horizontally Scrollable */}
      <div className="mb-6">
        <div className="flex gap-4 overflow-x-auto pb-2 pt-1 px-1" style={{ scrollbarWidth: 'thin' }}>
          {/* Overall Summary Card */}
          {trainingData.data?.summary && (
            <button
              onClick={() => {
                setSelectedAreaId(undefined);
              }}
              className={`bg-white rounded-lg border p-4 min-w-[200px] flex-shrink-0 text-left transition-all hover:shadow-md cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 my-1 ${
                selectedAreaId === undefined
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
              type="button"
            >
              <div className="text-sm text-gray-600 mb-1">All</div>
              <div className="text-2xl font-semibold text-slate-800 mb-1">
                {trainingData.data.summary.overall.partiallyTrained + trainingData.data.summary.overall.fullyTrained}
              </div>
              <div className="text-xs text-gray-500">
                {trainingData.data.summary.overall.fullyTrained} fully trained
              </div>
            </button>
          )}

          {/* Area-specific Summary Cards */}
          {map(trainingData.data?.summary.areas, (area) => (
            <button
              key={area.areaId}
              onClick={() => {
                setSelectedAreaId(area.areaId);
              }}
              className={`bg-white rounded-lg border p-4 min-w-[200px] flex-shrink-0 text-left transition-all hover:shadow-md cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 my-1 ${
                selectedAreaId === area.areaId
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
              type="button"
            >
              <div className="text-sm text-gray-600 mb-1">{area.areaTitle}</div>
              <div className="text-2xl font-semibold text-slate-800 mb-1">
                {area.partiallyTrained + area.fullyTrained}
              </div>
              <div className="text-xs text-gray-500">
                {area.fullyTrained} fully trained
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Data Table */}
      <div className="space-y-4">
        {/* Table with horizontal scroll and sticky headers */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto max-h-[70vh] overflow-y-auto">
            <Table className="min-w-full">
                <TableHeader className="bg-blue-50 sticky top-0 z-30">
                  {map(table.getHeaderGroups(), (headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {map(headerGroup.headers, (header) => {
                        const isSticky = header.column.columnDef.meta?.sticky;
                        return (
                          <TableHead
                            key={header.id}
                            className={`
                              text-gioaBlue bg-blue-50
                              ${isSticky === 'left' ? 'sticky left-0 z-40 border-r border-gray-300' : ''}
                              ${isSticky === 'right' ? 'sticky right-0 z-40 border-l border-gray-300' : ''}
                            `}
                            style={{
                              width: header.getSize(),
                              minWidth: header.getSize(),
                            }}
                          >
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                          </TableHead>
                        )
                      })}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {size(table.getRowModel().rows) ? (
                    map(table.getRowModel().rows, (row) => (
                      <TableRow
                        key={row.id}
                        data-state={row.getIsSelected() && "selected"}
                        className="hover:bg-gray-50"
                      >
                        {map(row.getVisibleCells(), (cell) => {
                          const isSticky = cell.column.columnDef.meta?.sticky;
                          const isSorted = cell.column.getIsSorted();
                          return (
                            <TableCell
                              key={cell.id}
                              className={`
                                ${isSticky === 'left' ? 'sticky left-0 z-10 border-r border-gray-200' : ''}
                                ${isSticky === 'right' ? 'sticky right-0 z-10 border-l border-gray-200' : ''}
                                ${isSorted ? 'bg-blue-50' : ''}
                                ${isSticky && isSorted ? 'bg-blue-50' : isSticky ? 'bg-white' : ''}
                              `}
                              style={{
                                width: cell.column.getSize(),
                                minWidth: cell.column.getSize(),
                              }}
                            >
                              {flexRender(cell.column.columnDef.cell, cell.getContext())}
                            </TableCell>
                          );
                        })}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={size(table.getAllColumns())} className="h-24 text-center">
                        <div className="text-center py-8">
                          <Text muted>No team members found</Text>
                          <Text muted className="text-sm mt-1">
                            Team member training data will appear here when available
                          </Text>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
          </div>
        </div>

        {/* No pagination needed - showing all team members */}
      </div>

      {/* Training Status Modal */}
      <TrainingStatusModal
        isOpen={trainingModal.isOpen}
        onClose={closeTrainingModal}
        onSave={handleTrainingStatusSave}
        personName={trainingModal.personName || ''}
        positionTitle={trainingModal.positionTitle || ''}
        areaTitle={trainingModal.areaTitle || ''}
        storeName={storeData.title}
        currentStatus={trainingModal.currentStatus || false}
        isLoading={updateTraining.isPending}
      />

      {/* Proficiency Rating Modal */}
      <ProficiencyRatingModal
        isOpen={proficiencyModal.isOpen}
        onClose={closeProficiencyModal}
        onSave={handleProficiencyRatingSave}
        personName={proficiencyModal.personName || ''}
        currentRating={proficiencyModal.currentRating || 0}
        isLoading={updateProficiencyRating.isPending}
      />
    </div>
  );
}
