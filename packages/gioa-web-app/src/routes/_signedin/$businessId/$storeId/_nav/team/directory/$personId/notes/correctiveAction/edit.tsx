import {create<PERSON><PERSON><PERSON><PERSON><PERSON>, useRouter} from "@tanstack/react-router";
import React, {useState} from "react";
import {api} from "@/src/api";
import {useForm} from "@tanstack/react-form";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {z} from "zod";
import {Button} from "@/src/components/ui/button";
import {FormControl} from "@/src/components/form/FormControl";
import {Label} from "@/src/components/ui/label";
import {FormTextarea} from "@/src/components/form/FormTextarea";
import {FieldInfo} from "@/src/components/form/FieldInfo";
import {find, isEmpty, map} from "lodash";
import {toast} from "sonner";
import {ErrorAlert} from "@/src/components/ErrorAlert";
import {LoadingPage} from "@/src/components/LoadingPage";
import {Text} from "@/src/components/Text.tsx";
import {FormSelectPolicy} from "@/src/components/FormSelectPolicy.tsx";
import {FormImagePicker, SelectedMedia} from "@/src/components/form/FormImagePicker.tsx";
import {useUploadFile} from "@/src/hooks/useUploadFile.tsx";
import {FormDatePicker} from "@/src/components/form/FormDatePicker.tsx";
import {Heading} from "@/src/components/Heading.tsx";
import {inferMimeTypeFromUri} from "../../../../../../../../../../../../api/src/inferMimeTypeFromUri.ts";

// Define search params schema to handle optional type
const correctiveActionSearchSchema = z.object({
  noteId: z.string().optional(),
});

export const Route = createFileRoute(
  "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/edit",
)({
  component: EditCorrectiveActionScreen,
  validateSearch: correctiveActionSearchSchema,
  pendingComponent: LoadingPage,
  errorComponent: ({ error }) => <ErrorAlert error={error} />,
});

function EditCorrectiveActionScreen() {
  const { businessId, storeId, personId } = Route.useParams();
  const { noteId } = Route.useSearch();
  const router = useRouter();
  const apiUtil = api.useUtils();

  const onGoBack = () => {
    router.history.back();
  };

  // Fetch person details
  const [person] = api.user.getPersonDetail.useSuspenseQuery(
    {
      personId,
      storeId,
    },
    {
      staleTime: 5 * 60 * 1000,
    },
  );

  // Find the note if noteId is provided
  const note = find(person.notes, (n) => n.id === noteId);
  const correctiveAction = note && "correctiveAction" in note ? note.correctiveAction : undefined;

  // Fetch store details
  const [business] = api.user.getBusiness.useSuspenseQuery(undefined, {
    staleTime: 5 * 60 * 1000,
  });

  const store = find(business.stores, (s) => s.id === storeId);

  const [isUploading, setIsUploading] = useState(false);
  const getPresignedPost = api.user.getPersonNotesPresignedPost.useMutation();
  const upload = useUploadFile(({ contentType }) => getPresignedPost.mutateAsync({ contentType, folder: "ca" }));

  const createCorrectiveAction = api.user.createCorrectiveAction.useMutation({
    async onSuccess() {
      toast.success(`Actionable Item created for ${person?.firstName} ${person?.lastName}`, {
        position: "top-center",
      });
      await apiUtil.user.getPersonDetail.invalidate({
        personId,
        storeId,
      });
      onGoBack();
    },
  });

  const editActionableItem = api.user.editActionableItem.useMutation({
    async onSuccess() {
      toast.success(`Actionable Item updated for ${person?.firstName} ${person?.lastName}`, {
        position: "top-center",
      });
      await apiUtil.user.getPersonDetail.invalidate({
        personId,
        storeId,
      });
      await apiUtil.user.getPersonNote.invalidate({
        noteId,
      });
      onGoBack();
    },
  });

  const form = useForm({
    defaultValues: {
      infractionAt:
        correctiveAction && "actionableItemInfractionAt" in correctiveAction
          ? correctiveAction.actionableItemInfractionAt
          : new Date(),
      incidentDescription:
        correctiveAction && "incidentDescription" in correctiveAction ? correctiveAction.incidentDescription : "",
      policiesInAction:
        correctiveAction && "actionableItemPoliciesInAction" in correctiveAction
          ? (correctiveAction.actionableItemPoliciesInAction ?? [])
          : [],
      images: [] as SelectedMedia[],
      existingImages:
        correctiveAction && "actionableItemImages" in correctiveAction
          ? (correctiveAction.actionableItemImages ?? [])
          : [],
      certify: false,
    },
    validatorAdapter: zodValidator(),
    onSubmit: async (event) => {
      try {
        setIsUploading(true);
        const uploadedImageIds: string[] = [];
        for (const image of event.value.images) {
          const mimeType = image.mimeType || inferMimeTypeFromUri(image.uri);
          const uploadResult = await upload.upload({
            file: image.file,
            contentType: mimeType,
            mediaType: "image",
          });
          if (!uploadResult) {
            toast.error("Failed to upload image", {
              position: "top-center",
            });
            return;
          }
          uploadedImageIds.push(uploadResult.newId);
        }

        if (correctiveAction) {
          editActionableItem.mutate({
            id: correctiveAction.id,
            version: correctiveAction.version,
            data: {
              infractionAt: event.value.infractionAt,
              incidentDescription: event.value.incidentDescription,
              policiesInAction: event.value.policiesInAction,
              images: map(event.value.images, (image, idx) => {
                const newImageId = uploadedImageIds[idx];
                return {
                  imageId: newImageId,
                  width: image.width,
                  height: image.height,
                  mimeType: image.mimeType,
                };
              }),
              copyImageIds: map(event.value.existingImages, (image) => image.id),
            },
          });
          return;
        }

        createCorrectiveAction.mutate({
          infractionAt: event.value.infractionAt,
          recipientPersonId: person.id!,
          incidentDescription: event.value.incidentDescription,
          policiesInAction: event.value.policiesInAction,
          storeId: storeId,
          businessId: businessId,
          images: map(event.value.images, (image, idx) => {
            const newImageId = uploadedImageIds[idx];
            return {
              imageId: newImageId,
              width: image.width,
              height: image.height,
              mimeType: image.mimeType,
            };
          }),
        });
      } catch (error) {
        console.error("Error submitting form:", error);
      } finally {
        setIsUploading(false);
      }
    },
  });

  const isLoading = createCorrectiveAction.isPending || editActionableItem.isPending || isUploading;

  return (
    <div className="max-w-4xl mx-auto">
      <Heading level={1} size={"xs"}>{correctiveAction ? "Edit" : "New"} Actionable Item</Heading>

      <div className="my-3">
        <Text>
          Create an Actionable Item for {person?.firstName} {person?.lastName}.
        </Text>
      </div>

      {createCorrectiveAction.isError && <ErrorAlert error={createCorrectiveAction.error} className="my-2" />}
      {editActionableItem.isError && <ErrorAlert error={editActionableItem.error} className="my-2" />}

      <form.Field
        name="policiesInAction"
        validators={{
          onChangeListenTo: ["incidentDescription"],
          onChange: ({ value, fieldApi }) => {
            if (isEmpty(value) && isEmpty(fieldApi.form.getFieldValue("incidentDescription"))) {
              return "You must either select at least one policy or enter a description of the incident.";
            }
            return undefined;
          },
        }}
        children={(field) => (
          <FormControl>
            <div className="mb-1">
              <Label className="text-primary-600">Policy violated</Label>
            </div>
            <FormSelectPolicy field={field} />
            <FieldInfo field={field} />
          </FormControl>
        )}
      />

      <div className="space-y-6">
        <form.Field
          name="incidentDescription"
          validators={{
            onSubmit: z.string().min(1, "Required"),
          }}
          children={(field) => (
            <FormControl>
              <Label className={"text-primary-600"}>Description of incident</Label>
              <FormTextarea field={field} placeholder="Describe what happened" className="min-h-32" />
              <FieldInfo field={field} />
            </FormControl>
          )}
        />

        <form.Field
          name="infractionAt"
          validators={{
            onSubmit: z.date().refine((d) => d <= new Date(), { message: "Date cannot be in the future." }),
          }}
          children={(field) => (
            <FormControl>
              <Label className={"text-primary-600"}>Date of Infraction</Label>
              <FormDatePicker field={field} />
              <FieldInfo field={field} />
            </FormControl>
          )}
        />

        <div className="mb-4">
          <Label className={"text-primary-600"}>Location</Label>
          <div className="mt-1">
            <Text>{store?.title}</Text>
          </div>
        </div>

        <div>
          <Label className="mb-1 text-primary-700">Attachments</Label>
          <div className="flex flex-row flex-wrap gap-2 mt-2">
            <form.Field
              name="existingImages"
              mode="array"
              validators={{
                onSubmit: z.array(z.any()).max(5),
              }}
              children={(arrField) => (
                <>
                  {map(arrField.state.value, (image, idx) => (
                    <div key={image.id} className="relative group">
                      <img
                        src={`${image.url}?w=100&h=100`}
                        className="w-24 h-24 object-cover rounded-md"
                        alt="Attachment"
                      />
                      <button
                        type="button"
                        className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => arrField.removeValue(idx)}
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </>
              )}
            />

            <div>
              <form.Field
                name="images"
                mode="array"
                validators={{
                  onSubmit: z.array(z.any()).max(5),
                }}
                children={(field) => (
                  <FormControl>
                    <FormImagePicker field={field} maxImages={5} />
                    <FieldInfo field={field} />
                  </FormControl>
                )}
              />
            </div>
          </div>
        </div>

        <form.Field
          name="certify"
          validators={{
            onSubmit: z.boolean().refine((b) => b === true, { message: "You must check this box." }),
          }}
          children={(field) => (
            <FormControl>
              <div className="flex items-center space-x-4">
                <input
                  type="checkbox"
                  id="certify"
                  className="mt-1 h-5 w-5"
                  checked={field.state.value}
                  onChange={(e) => field.handleChange(e.target.checked)}
                />
                <label htmlFor="certify">I certify that this is a fair and accurate description of what happened</label>
              </div>
              <FieldInfo field={field} />
            </FormControl>
          )}
        />

        <div className={"mt-2"}>
          <Text>
            Pressing <span className="font-bold">Save & Notify</span> will notify HR that you have added this Actionable
            Item.
          </Text>
        </div>

        <div className="flex justify-between gap-4 mt-8">
          <Button variant="outline" onClick={onGoBack}>
            Cancel
          </Button>
          <Button type="button" onClick={form.handleSubmit} disabled={isLoading}>
            {isLoading ? "Saving..." : correctiveAction ? "Save" : "Save & Notify"}
          </Button>
        </div>
      </div>
    </div>
  );
}
