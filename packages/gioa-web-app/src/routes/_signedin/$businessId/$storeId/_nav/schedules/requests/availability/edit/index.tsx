import {createFile<PERSON>oute, useNavigate} from "@tanstack/react-router";
import {api} from "@/src/api.ts";
import {Text} from "@/src/components/Text.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {ArrowLeftIcon} from "lucide-react";
import {AvailabilityWeekDetails} from "@/src/components/AvailabilityWeekDetails.tsx";
import React, {useEffect, useState} from "react";
import {DateTime} from "luxon";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {useForm} from "@tanstack/react-form";
import {find, reduce} from "lodash";
import {getDurationHours} from "../../../../../../../../../../../api/src/timeSchemas.util.ts";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {EditAvailabilityDayModal} from "@/src/components/EditAvailabilityDayModal.tsx";
import {DayOfWeek, useDaysOfWeek} from "../../../../../../../../../../../api/src/daysOfWeek.ts";
import {formatDateNoTime} from "../../../../../../../../../../../api/src/date.util.ts";
import {FormDateTimePicker} from "@/src/components/form/FormDateTimePicker.tsx";
import {toast} from "sonner";
import {Label} from "@/src/components/ui/label.tsx";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {FormTeamMemberCombobox} from "@/src/components/form/FormTeamMemberCombobox.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {genPersonAvailabilityId} from "../../../../../../../../../../../api/src/schemas.ts";

export const Route = createFileRoute(
        "/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/"
)({
  component: RouteComponent,
});

// This route handles NEW availability request creation only
// For editing existing requests, use edit/$personAvailabilityId route
function RouteComponent() {
  const {businessId, storeId} = Route.useParams();
  const navigate = useNavigate();
  const apiUtil = api.useUtils();

  const form = useForm({
    defaultValues: {
      personId: undefined as string | undefined,
      totalHours: 0,
      maxHoursPreferred: undefined as number | undefined,
      maxDaysPreferred: undefined as number | undefined,
      effectiveAt: new Date(),
      reason: "",
    },
    onSubmit: async ({value}) => {
      if (!value.personId) return;
      const id = personAvailability?.pending?.id ?? personAvailability?.draft?.id!;
      if (!hasPendingAvailability) {
        await submitPersonAvailabilityWeekAdmin.mutateAsync({
          id,
          personId: value.personId,
          storeId: storeId!,
          effectiveAt: formatDateNoTime(value.effectiveAt),
          maxHoursPreferred: Number(value.maxHoursPreferred) || undefined,
          maxDaysPreferred: Number(value.maxDaysPreferred) || undefined,
        });
      }

      await approveAvailability.mutateAsync({
        personAvailabilityId: id,
        reason: value.reason,
      });

      handleBack();
    },
  });

  const [{timezone}] = api.user.getStore.useSuspenseQuery({storeId});
  const [{people}] = api.user.getAllSchedulePeopleAtStore.useSuspenseQuery({storeId});
  const personId = form.useStore((s) => s.values.personId);

  const createPersonAvailabilityAdmin = api.user.createPersonAvailabilityAdmin.useMutation();
  const {data: personAvailability} = api.user.getPersonAvailabilityAdmin.useQuery(
          {
            storeId,
            personId: personId!,
          },
          {
            enabled: Boolean(personId),
          }
  );

  const hasPendingAvailability = Boolean(personAvailability?.pending);
  const availabilityData = personAvailability?.pending ?? personAvailability?.draft;
  const daysOfWeek = useDaysOfWeek();

  const totalHours = availabilityData?.ranges
          ? reduce(availabilityData.ranges, (acc, range) => acc + getDurationHours(range), 0)
          : 0;

  const totalHoursRounded = Math.round(totalHours * 10) / 10;
  const maxHoursPreferred = availabilityData?.maxHoursPreferred;
  const effectiveDate = DateTime.fromISO(availabilityData?.effectiveAt ?? DateTime.now().toISO(), {zone: timezone})
          .set({hour: 12, minute: 0})
          .toJSDate();

  const activeAvailabilityId = personAvailability?.pending?.id ?? personAvailability?.draft?.id;

  useEffect(() => {
    (async () => {
      if (availabilityData) {
        form.setFieldValue("totalHours", totalHoursRounded);
        form.setFieldValue("maxHoursPreferred", maxHoursPreferred);
        form.setFieldValue("effectiveAt", effectiveDate);
      }

      if (!personId) return;

      const currentAvailability = await apiUtil.user.getPersonAvailabilityAdmin.fetch({
        storeId: storeId!,
        personId,
      });

      if (!currentAvailability?.draft && !currentAvailability?.pending) {
        const id = genPersonAvailabilityId();

        await createPersonAvailabilityAdmin.mutateAsync({
          id,
          personId,
          storeId: storeId!,
          effectiveAt: formatDateNoTime(new Date()),
        });

        await apiUtil.user.getPersonAvailabilityAdmin.invalidate({
          storeId,
          personId,
        });
      }
    })();
  }, [personId, availabilityData]);


  const submitPersonAvailabilityWeekAdmin = api.user.submitPersonAvailabilityWeekAdmin.useMutation();
  const approveAvailability = api.user.approveAvailabilityRequest.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getAllSchedulePeopleAtStore.invalidate({storeId});
      await apiUtil.user.getAvailabilityRequestsAdvanced.invalidate();
      await apiUtil.user.getAllSchedulePeopleAtStore.invalidate({storeId: storeId});
      toast.success("Availability approved");
    },
  });
  const declineAvailability = api.user.declineAvailabilityRequest.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getAvailabilityRequestsAdvanced.invalidate();
      toast.success("Availability declined");
    },
  });

  const [isOpen, setIsOpen] = useState(false);
  const [dayOfWeek, setDayOfWeek] = useState<DayOfWeek>();

  const handleOnDayClick = (dayOfWeek: number) => {
    if (dayOfWeek === 7) return;
    const match = daysOfWeek.find((d) => d.dayOfWeek === dayOfWeek);
    if (match) {
      setDayOfWeek(match);
      setIsOpen(true);
    }
  };

  const handleBack = () => {
    navigate({
      to: "/$businessId/$storeId/schedules/requests/availability",
      params: {businessId, storeId},
    });
  };

  const handleDelete = async () => {
    if (!personAvailability?.pending?.id) return;
    await declineAvailability.mutateAsync({
      personAvailabilityId: personAvailability.pending.id,
      reason: form.state.values.reason,
    });
    handleBack();
  };

  const isLoading =
          submitPersonAvailabilityWeekAdmin.isPending ||
          approveAvailability.isPending ||
          declineAvailability.isPending;

  return (
          <>
            <form
                    onSubmit={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      form.handleSubmit();
                    }}
                    className="p-12 w-full flex flex-col justify-start gap-4 max-w-screen-lg"
            >
              <div>
                <Text className="text-2xl font-semibold">Edit Availability</Text>
                <Text className="font-light">Select a team member and a day to edit availability</Text>
              </div>
              <hr/>

              <form.Field name="personId">
                {(field) => (
                        <FormControl>
                          <FormTeamMemberCombobox people={people} className="flex" listClassName="max-h-[60vh]"
                                                  field={field}/>
                          <FieldInfo field={field}/>
                        </FormControl>
                )}
              </form.Field>

              {personId && availabilityData ? (
                      <>
                        <div className="flex flex-row items-start gap-8 w-full">
                          <div className="flex-1 border-r border-gray-200 pr-8">
                            <AvailabilityWeekDetails
                                    availabilityData={availabilityData}
                                    onDayClick={handleOnDayClick}
                            />
                          </div>

                          <div className="flex-1">
                            <form.Field name="totalHours">
                              {(field) => (
                                      <FormControl>
                                        <Label className="text-sm mb-2">Total Hours Available</Label>
                                        <FormInput field={field} readOnly/>
                                      </FormControl>
                              )}
                            </form.Field>

                            <form.Field name="maxHoursPreferred">
                              {(field) => (
                                      <FormControl>
                                        <Label className="text-sm mb-2">Max Hours Preferred</Label>
                                        <FormInput field={field}/>
                                      </FormControl>
                              )}
                            </form.Field>

                            <form.Field name="maxDaysPreferred">
                              {(field) => (
                                      <FormControl>
                                        <Label className="text-sm mb-2">Preferred Number of Days</Label>
                                        <FormInput field={field}/>
                                      </FormControl>
                              )}
                            </form.Field>

                            <form.Field name="effectiveAt">
                              {(field) => (
                                      <FormControl>
                                        <Label className="text-sm mb-2">Effective Date</Label>
                                        <FormDateTimePicker
                                                field={field}
                                                timezone={timezone}
                                                showTimeSelect={false}
                                        />
                                      </FormControl>
                              )}
                            </form.Field>

                            <form.Field name="reason">
                              {(field) => (
                                      <FormControl>
                                        <Label className="text-sm mb-2">Comments</Label>
                                        <FormTextarea field={field}/>
                                      </FormControl>
                              )}
                            </form.Field>
                          </div>
                        </div>

                        <div className="flex flex-row gap-4 justify-end mt-6">
                          <Button variant="outline" onClick={handleBack} leftIcon={<ArrowLeftIcon size={16}/>}>
                            Back
                          </Button>
                          {hasPendingAvailability ? (
                                  <>
                                    <Button variant="destructive" onClick={handleDelete} isLoading={isLoading}>
                                      Decline
                                    </Button>
                                    <Button type="submit" isLoading={isLoading}>
                                      Approve
                                    </Button>
                                  </>
                          ) : (
                                  <Button type="submit" isLoading={isLoading}>
                                    Submit
                                  </Button>
                          )}
                        </div>
                      </>
              ) : null}

            </form>

            {dayOfWeek && personId && activeAvailabilityId && (
                    <EditAvailabilityDayModal
                            isOpen={isOpen}
                            onOpenChange={setIsOpen}
                            personId={personId}
                            storeId={storeId}
                            personAvailabilityId={activeAvailabilityId}
                            dayOfWeek={dayOfWeek.dayOfWeek}
                    />
            )}
          </>
  );
}
