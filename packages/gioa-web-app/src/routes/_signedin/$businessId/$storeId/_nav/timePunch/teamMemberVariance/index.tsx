import {createFile<PERSON>oute, useNavigate, SearchSchemaInput} from '@tanstack/react-router'
import {api} from "@/src/api.ts";
import {useState, useMemo, Suspense, useEffect, useRef} from "react";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/src/components/ui/table.tsx";
import {DataTablePagination} from "@/src/components/DataTablePagination.tsx";
import {
  flexRender,
  getCoreRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import {Text} from "@/src/components/Text.tsx";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {cn} from "@/src/util.ts";
import {CustomDateFilter, TimeFrameFilter} from "@/src/components/CustomDateFilter.tsx";
import {DateTime, WeekdayNumbers} from "luxon";
import {z} from "zod";
import {IsoCompleteDate} from "../../../../../../../../../api/src/timeSchemas.ts";
import {getDateRangeForTimeFrame} from "@/src/utils/dateRange.util.ts";
import { map,} from 'lodash';
import {Button} from "@/src/components/ui/button.tsx";
import {Checkbox} from "@/src/components/ui/checkbox.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {ProficiencyRating} from "@/src/components/ProficiencyRating.tsx";
import {LaborStatusIcon} from "@/src/components/LaborStatusIcon.tsx";
import {getPersonLaborStatus} from "../../../../../../../../../api/src/personMetrics.ts";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/src/components/ui/dropdown-menu.tsx";
import {PayRatesUploadDialog} from "@/src/components/PayRatesUploadDialog.tsx";
import {EditPayRatesDialog} from "@/src/components/EditPayRatesDialog.tsx";
import {TotalLaborVarianceHelpDialog, createVarianceTableColumns, VarianceFilterDialog, VarianceCostTooltip} from "@/src/components/VarianceComponents.tsx";
import {ArrowLeft, ChevronDownIcon, PencilIcon, SlidersVertical, UploadIcon, HelpCircleIcon} from "lucide-react";

// Shared search schema for date navigation (used by both Variance and Punches pages)
const sharedDateFilterSchema = z.object({
  week: z.number().int().min(1).max(53).optional(),
  year: z.number().int().min(2000).max(9999).optional(),
  day: z.number().int().min(1).max(7).optional(),
  month: z.number().int().min(1).max(12).optional(),
  timeFrame: z.enum(['day', 'week', 'month', 'custom']).optional(),
  startDate: z.string().optional(), // ISO date string for custom range
  endDate: z.string().optional(),   // ISO date string for custom range
  // Person ID passed from Team Totals page
  personId: z.string().optional(),
}).optional();

type TeamMemberVarianceSearch = z.infer<typeof sharedDateFilterSchema>;

export const Route = createFileRoute('/_signedin/$businessId/$storeId/_nav/timePunch/teamMemberVariance/')({
  component: () => (
    <Suspense fallback={<div>Loading...</div>}>
      <TeamMemberVariance />
    </Suspense>
  ),
  validateSearch: (search: unknown & SearchSchemaInput): TeamMemberVarianceSearch | undefined => {
    const result = sharedDateFilterSchema.safeParse(search);
    if (result.success) {
      return result.data as any;
    }
    // If validation fails, still return the search to preserve parameters
    return search as any;
  },
})

// Type and column helper are now imported from VarianceComponents

function TeamMemberVariance() {
  const {businessId, storeId} = Route.useParams();
  const searchParams = Route.useSearch();
  const navigate = useNavigate();

  // Get person details using API if personId is provided
  const personDetailsQuery = api.user.getPersonDetail.useQuery(
    {
      personId: searchParams?.personId!,
      storeId: storeId,
    },
    {
      enabled: !!searchParams?.personId, // Only run query if personId exists
    }
  );

  // Extract person information from API response
  const selectedPerson = personDetailsQuery.data ? (() => {
    // Calculate age from metadata birthday (same logic as in timePunch.util.ts)
    let age: number | null = null;
    if (personDetailsQuery.data.metadata?.birthday) {
      const birthDate = new Date(personDetailsQuery.data.metadata.birthday);
      const today = new Date();
      age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
    }

    return {
      id: searchParams?.personId,
      name: `${personDetailsQuery.data.firstName || ''} ${personDetailsQuery.data.lastName || ''}`.trim(),
      profileImageUrl: personDetailsQuery.data.profileImageUrl || null,
      age: age,
      proficiencyRanking: personDetailsQuery.data.proficiencyRanking,
    };
  })() : null;

  // Function to navigate back to Team Totals page
  const handleBackToTeamTotals = () => {
    // Remove person-specific search params when going back
    const {personId, ...teamTotalsSearchParams} = searchParams || {};
    navigate({
      to: `/${businessId}/${storeId}/timePunch/teamTotals`,
      search: teamTotalsSearchParams as any // Type assertion to handle different search schemas
    });
  };

  // Get store data for timezone
  const [store] = api.user.getStoreAdmin.useSuspenseQuery({storeId: storeId!}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const timezone = store.timezone ?? "America/New_York";
  const [today] = useState(DateTime.now().setZone(timezone));

  // Get people data for pay rate management modals
  const [peopleData] = api.user.getAllSchedulePeopleAtStore.useSuspenseQuery({storeId: storeId!});
  const people = peopleData.people;

  // Date filter logic with default to custom range (past 2 weeks)
  const timeFrame: TimeFrameFilter = (searchParams?.timeFrame as TimeFrameFilter) ?? 'custom';
  const isoDateSearch: IsoCompleteDate = searchParams?.week && searchParams?.year ? {
    week: searchParams.week,
    year: searchParams.year,
    day: searchParams.day,
    month: searchParams.month
  } : {week: today.weekNumber, year: today.weekYear};

  // Custom date range logic - create dates in local timezone to avoid off-by-one issues
  // Default to past 2 weeks only when timeFrame is custom but no date params exist
  const hasDateSearchParams = !!(searchParams?.startDate || searchParams?.endDate || searchParams?.timeFrame);
  const isFirstVisit = timeFrame === 'custom' && !hasDateSearchParams;

  let customStartDate: IsoCompleteDate | null = null;
  let customEndDate: IsoCompleteDate | null = null;

  if (searchParams?.startDate) {
    // Parse as calendar date (YYYY-MM-DD) without timezone interpretation
    const [year, month, day] = searchParams.startDate.split('-').map(Number);
    const dt = DateTime.fromObject({ year, month, day });
    customStartDate = { year: dt.weekYear, month: month, day: dt.weekday, week: dt.weekNumber };
  } else if (isFirstVisit) {
    const startDateTime = today.minus({weeks: 2});
    customStartDate = { year: startDateTime.weekYear, month: startDateTime.month, day: startDateTime.weekday, week: startDateTime.weekNumber };
  }

  if (searchParams?.endDate) {
    // Parse as calendar date (YYYY-MM-DD) without timezone interpretation
    const [year, month, day] = searchParams.endDate.split('-').map(Number);
    const dt = DateTime.fromObject({ year, month, day });
    customEndDate = { year: dt.weekYear, month: month, day: dt.weekday, week: dt.weekNumber };
  } else if (isFirstVisit) {
    customEndDate = { year: today.weekYear, month: today.month, day: today.weekday, week: today.weekNumber };
  }

  const dateRange = getDateRangeForTimeFrame(isoDateSearch, timeFrame, timezone, customStartDate, customEndDate);

  // State for filters
  const [employeeFilter, setEmployeeFilter] = useState<string>("all");
  const [showOpenPunchesOnly, setShowOpenPunchesOnly] = useState<boolean>(false);
  const [sorting, setSorting] = useState<SortingState>([]);

  // Variance filter state - separate draft and applied states
  const [varianceFilterOpen, setVarianceFilterOpen] = useState<boolean>(false);

  // Modal disclosures for pay rate management and help dialog
  const editPayRateDisclosure = useDisclosure();
  const uploadPayRateDisclosure = useDisclosure();
  const helpDialogDisclosure = useDisclosure();

  // API utils for invalidating queries
  const apiUtils = api.useUtils();

  // Draft filter state (what user is currently editing in the dialog)
  const [draftPunchType, setDraftPunchType] = useState<string>("all"); // "all", "clockIn", "clockOut"
  const [draftPunchTiming, setDraftPunchTiming] = useState<string>("all"); // "all", "early", "late"
  const [draftTimeCondition, setDraftTimeCondition] = useState<string>("all"); // "all", "lessThan", "greaterThan"
  const [draftTimeThreshold, setDraftTimeThreshold] = useState<number>(5); // minutes (same as variance page)

  // Applied filter state (what is actually used for filtering)
  const [appliedPunchType, setAppliedPunchType] = useState<string>("all");
  const [appliedPunchTiming, setAppliedPunchTiming] = useState<string>("all");
  const [appliedTimeCondition, setAppliedTimeCondition] = useState<string>("all");
  const [appliedTimeThreshold, setAppliedTimeThreshold] = useState<number>(5); // minutes (same as variance page)

  // Use default pagination values initially
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 20,
  });

  // Helper function to check if variance filter is active (based on applied state)
  const isVarianceFilterActive = () => {
    return appliedPunchType !== "all" || appliedPunchTiming !== "all" || appliedTimeCondition !== "all";
  };

  // Helper function to reset variance filter (resets both draft and applied)
  const resetVarianceFilter = () => {
    setDraftPunchType("all");
    setDraftPunchTiming("all");
    setDraftTimeCondition("all");
    setDraftTimeThreshold(5); // Reset to 5 minutes (same as variance page)
    setAppliedPunchType("all");
    setAppliedPunchTiming("all");
    setAppliedTimeCondition("all");
    setAppliedTimeThreshold(5); // Reset to 5 minutes (same as variance page)
  };

  // Custom close handlers that invalidate variance data
  const handleUploadPayRateClose = () => {
    uploadPayRateDisclosure.onClose();
    // Invalidate variance data to refresh calculations with new pay rates
    apiUtils.user.getTimePunchVarianceEntries.invalidate();
  };

  const handleEditPayRateClose = () => {
    editPayRateDisclosure.onClose();
    // Invalidate variance data to refresh calculations with new pay rates
    apiUtils.user.getTimePunchVarianceEntries.invalidate();
  };

  // Helper function to apply the draft filters
  const applyVarianceFilter = () => {
    setAppliedPunchType(draftPunchType);
    setAppliedPunchTiming(draftPunchTiming);
    setAppliedTimeCondition(draftTimeCondition);
    setAppliedTimeThreshold(draftTimeThreshold);
  };

  // Track previous filter values to detect actual filter changes
  const prevFiltersRef = useRef({
    startDate: dateRange.start,
    endDate: dateRange.end,
    employeeFilter,
    showOpenPunchesOnly,
    appliedPunchType,
    appliedPunchTiming,
    appliedTimeCondition,
    appliedTimeThreshold
  });

  // Reset pagination to page 1 whenever any filter changes (but not on initial render)
  useEffect(() => {
    const currentFilters = {
      startDate: dateRange.start,
      endDate: dateRange.end,
      employeeFilter,
      showOpenPunchesOnly,
      appliedPunchType,
      appliedPunchTiming,
      appliedTimeCondition,
      appliedTimeThreshold
    };

    const prevFilters = prevFiltersRef.current;

    // Check if any filter actually changed
    const filtersChanged =
      currentFilters.startDate?.getTime() !== prevFilters.startDate?.getTime() ||
      currentFilters.endDate?.getTime() !== prevFilters.endDate?.getTime() ||
      currentFilters.employeeFilter !== prevFilters.employeeFilter ||
      currentFilters.showOpenPunchesOnly !== prevFilters.showOpenPunchesOnly ||
      currentFilters.appliedPunchType !== prevFilters.appliedPunchType ||
      currentFilters.appliedPunchTiming !== prevFilters.appliedPunchTiming ||
      currentFilters.appliedTimeCondition !== prevFilters.appliedTimeCondition ||
      currentFilters.appliedTimeThreshold !== prevFilters.appliedTimeThreshold;

    if (filtersChanged) {
      setPagination(prev => ({ ...prev, pageIndex: 0 }));
    }

    // Update the ref with current values
    prevFiltersRef.current = currentFilters;
  }, [
    dateRange.start,
    dateRange.end,
    employeeFilter,
    showOpenPunchesOnly,
    appliedPunchType,
    appliedPunchTiming,
    appliedTimeCondition,
    appliedTimeThreshold
  ]);

  // Date navigation function
  const handleDateChange = (date: IsoCompleteDate, timeFrame: TimeFrameFilter, customStart?: IsoCompleteDate | null, customEnd?: IsoCompleteDate | null) => {
    // Convert IsoCompleteDate to calendar date string for URL
    const convertToDateString = (isoDate: IsoCompleteDate) => {
      const dt = DateTime.fromObject({
        weekYear: isoDate.year,
        weekNumber: isoDate.week,
        weekday: (isoDate.day ?? 1) as WeekdayNumbers
      }).set({ month: isoDate.month });
      return dt.toFormat('yyyy-MM-dd');
    };

    const startDateString = customStart ? convertToDateString(customStart) : undefined;
    const endDateString = customEnd ? convertToDateString(customEnd) : undefined;

    navigate({
      from: Route.fullPath,
      search: {
        ...searchParams,
        week: date.week,
        year: date.year,
        day: date.day,
        month: date.month,
        timeFrame: timeFrame,
        startDate: startDateString,
        endDate: endDateString,
      }
    });
  };

  // Fetch time punch variance data with pagination and sorting using date range
  // Filter by the selected person's ID if available
  const personIdFilter = selectedPerson?.id || (employeeFilter !== "all" ? employeeFilter : undefined);

  const timePunchData = api.user.getTimePunchVarianceEntries.useQuery({
    storeId,
    startDate: dateRange.start, // Send Date object directly
    endDate: dateRange.end,     // Send Date object directly
    personId: personIdFilter,
    showOpenPunchesOnly: showOpenPunchesOnly || undefined,
    // Variance filter parameters
    variancePunchType: appliedPunchType as 'all' | 'clockIn' | 'clockOut',
    variancePunchTiming: appliedPunchTiming as 'all' | 'early' | 'late',
    varianceTimeCondition: appliedTimeCondition as 'all' | 'lessThan' | 'greaterThan',
    varianceTimeThreshold: appliedTimeCondition !== "all" ? appliedTimeThreshold : undefined, // Already in minutes
    limit: pagination.pageSize,
    offset: pagination.pageIndex * pagination.pageSize,
    sortBy: sorting[0]?.id as 'employeeName' | 'date' | 'timeIn' | 'timeOut' | 'totalHours' | 'scheduledTimeRange' | 'clockInVariance' | 'clockOutVariance' | 'varianceTotal' | undefined,
    sortOrder: sorting[0]?.desc ? 'desc' : 'asc',
  }, {
    enabled: !searchParams?.personId || !!selectedPerson, // Only run query when person details are loaded (if personId is provided)
    staleTime: 0, // Prevent showing stale data when navigating between team members
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: false, // Don't refetch on window focus to avoid unnecessary requests
  });

  // Use the data directly from the API since all filtering is now done server-side
  const filteredData = useMemo(() => {
    return timePunchData.data?.entries ?? [];
  }, [timePunchData.data?.entries]);

  // Use total variance cost calculated by the API (covers all entries, not just current page)
  const totalVarianceCostCents = timePunchData.data?.totalVarianceCostCents || 0;

  // Calculate summary statistics from all entries (not just current page)
  const allEntries = timePunchData.data?.entries || [];
  const summaryStats = {
    totalScheduled: allEntries.reduce((sum, entry) => sum + (entry.scheduledTimeDuration || 0), 0),
    totalActual: allEntries.reduce((sum, entry) => sum + (entry.punchTimeDuration || 0), 0),
    totalVarianceTime: allEntries.reduce((sum, entry) => sum + (entry.varianceTotal || 0), 0),
    totalVarianceCost: -totalVarianceCostCents, // Invert sign to match team totals convention
    totalWages: allEntries.reduce((sum, entry) => {
      // Calculate wages for each entry: punchTimeDuration (minutes) * payRate (cents per hour)
      const actualMinutes = entry.punchTimeDuration || 0;
      const payRateCents = entry.payRate || 0;
      if (actualMinutes > 0 && payRateCents > 0) {
        return sum + Math.round((actualMinutes / 60) * payRateCents);
      }
      return sum;
    }, 0),
  };

  // Use shared variance table columns (without team member column since showing single person)
  const columns = createVarianceTableColumns({
    store,
    helpDialogDisclosure,
    showTeamMemberColumn: false
  });

  // Create table instance with manual pagination and sorting
  const table = useReactTable({
    data: filteredData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualSorting: true,
    pageCount: timePunchData.data?.totalCount ? Math.ceil(timePunchData.data.totalCount / pagination.pageSize) : 0,
    onPaginationChange: setPagination,
    onSortingChange: (updater) => {
      setSorting(updater);
      // Reset to first page when sorting changes
      setPagination(prev => ({ ...prev, pageIndex: 0 }));
    },
    state: {
      sorting,
      pagination,
    },
  });

  return (
    <div className="rounded-xl border border-gray-200 bg-white p-4 w-full flex flex-col justify-start">
      <div className="flex flex-row justify-between items-center mb-8">
        {/* Back Button */}
        <Button
          variant="outline"
          onClick={handleBackToTeamTotals}
          className="flex items-center gap-2"
        >
          <ArrowLeft size={16} />
          Back
        </Button>

        {/* Manage Pay Rates Button - Only show if user has canViewPayRates permission */}
        {store.permissions?.canViewPayRates && (
          store.permissions?.canUpdatePayRates ?
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" type={"button"}
                        rightIcon={<ChevronDownIcon size={16}/>}>
                  Manage Pay Rates
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                <DropdownMenuItem onClick={uploadPayRateDisclosure.onOpen}>
                  <UploadIcon/>
                  <span>Upload Pay Rates</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={editPayRateDisclosure.onOpen}>
                  <PencilIcon/>
                  Edit Pay Rates
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            : <Button variant="outline" type={"button"}
                      onClick={() => alert(`You do not have permission to manage pay rates. Contact your manager or Operator to request the "Manage Pay Rates" permission.`)}
                      rightIcon={<ChevronDownIcon size={16}/>}>
              Manage Pay Rates
            </Button>
        )}
      </div>

      {/* Selected Person Avatar */}
      {searchParams?.personId && (
        <div className="mb-8 flex items-center gap-10 pl-6">
          {selectedPerson && selectedPerson.name ? (() => {
            // Split employeeName for PersonAvatar component
            const nameParts = selectedPerson.name.split(' ');
            const firstName = nameParts[0] || null;
            const lastName = nameParts.slice(1).join(' ') || null;
            const laborStatus = selectedPerson.age ? getPersonLaborStatus(selectedPerson.age) : null;

            // Create a person object compatible with PersonAvatar
            const personForAvatar = {
              id: selectedPerson.id || '',
              firstName,
              lastName,
              profileImageUrl: selectedPerson.profileImageUrl,
            };

            return (
              <div className="flex items-center gap-7">
                {/* Larger avatar */}
                <div className="scale-[2.00]">
                  <PersonAvatar person={personForAvatar} />
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <Text className="text-2xl font-semibold" colorScheme={"dark"}>
                      {selectedPerson.name}
                    </Text>
                    {laborStatus && <LaborStatusIcon laborStatus={laborStatus} />}
                  </div>
                  {selectedPerson.proficiencyRanking && selectedPerson.proficiencyRanking > 0 && (
                    <div className="mt-2">
                      <ProficiencyRating rank={selectedPerson.proficiencyRanking ?? 0} size={16} />
                    </div>
                  )}
                </div>
              </div>
            );
          })() : (
            // Loading state for avatar while person details are being fetched
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-gray-200 rounded-full animate-pulse"></div>
              <div>
                <div className="h-8 w-48 bg-gray-200 rounded animate-pulse mb-2"></div>
                <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Date Filter and Employee Filter */}
      <div className="bg-gray-50 rounded-lg border border-gray-200 p-4 mb-4">
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-start">
          {/* Date Filter */}
          <div className="flex gap-3 items-center flex-wrap">
            <CustomDateFilter
              timezone={store.timezone ?? "America/New_York"}
              timeRange={timeFrame}
              selectedDate={isoDateSearch}
              customStartDate={customStartDate}
              customEndDate={customEndDate}
              onDateChange={handleDateChange}
            />
          </div>

          {/* Variance Filter */}
          <div className="min-w-0">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Sync draft state with applied state when opening
                setDraftPunchType(appliedPunchType);
                setDraftPunchTiming(appliedPunchTiming);
                setDraftTimeCondition(appliedTimeCondition);
                setDraftTimeThreshold(appliedTimeThreshold);
                setVarianceFilterOpen(true);
              }}
              className={cn(
                "h-10 px-3 gap-2",
                isVarianceFilterActive() && "border-blue-500 bg-blue-50 text-blue-700"
              )}
            >
              Filter
              <SlidersVertical className="h-4 w-4" />
              {isVarianceFilterActive() && (
                <span className="bg-blue-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[16px] h-4 flex items-center justify-center">
                  •
                </span>
              )}
            </Button>
          </div>

          {/* Open Punches Filter */}
          <div className="flex items-center space-x-2 min-w-0 h-10">
            <Checkbox
              id="open-punches"
              checked={showOpenPunchesOnly}
              onCheckedChange={(checked) => {
                setShowOpenPunchesOnly(checked === true);
                setPagination(prev => ({ ...prev, pageIndex: 0 }));
              }}
            />
            <Label htmlFor="open-punches" className="text-sm font-normal cursor-pointer">
              Show open punches only
            </Label>
          </div>

          {/* Spacer to push total to the right */}
          <div className="flex-grow"></div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        {/* Total Scheduled */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-sm text-gray-600 mb-1">Total Scheduled</div>
          <div className="text-2xl font-semibold text-slate-800">
            {(() => {
              const totalMinutes = summaryStats.totalScheduled;
              if (totalMinutes === 0) return "—";
              const hours = Math.floor(totalMinutes / 60);
              const minutes = totalMinutes % 60;
              return `${hours}:${minutes.toString().padStart(2, '0')}`;
            })()}
          </div>
        </div>

        {/* Total Actual */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-sm text-gray-600 mb-1">Total Actual</div>
          <div className="text-2xl font-semibold text-slate-800">
            {(() => {
              const totalMinutes = summaryStats.totalActual;
              if (totalMinutes === 0) return "—";
              const hours = Math.floor(totalMinutes / 60);
              const minutes = totalMinutes % 60;
              return `${hours}:${minutes.toString().padStart(2, '0')}`;
            })()}
          </div>
        </div>

        {/* Total Wages */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center gap-1 mb-1">
            <span className="text-sm text-gray-600">Total Wages</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0 hover:bg-gray-100"
              onClick={helpDialogDisclosure.onOpen}
            >
              <HelpCircleIcon className="h-3 w-3 text-gray-500" />
            </Button>
          </div>
          <div className="text-2xl font-semibold text-slate-800">
            {(() => {
              const totalWagesCents = summaryStats.totalWages;
              if (totalWagesCents === 0) return "—";
              const wageDollars = totalWagesCents / 100;
              return wageDollars.toLocaleString('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              });
            })()}
          </div>
        </div>

        {/* Total Variance Time */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-sm text-gray-600 mb-1">Total Variance Time</div>
          <div className="text-2xl font-semibold">
            {(() => {
              const totalMinutes = summaryStats.totalVarianceTime;

              if (totalMinutes === 0) return <span className="text-slate-800">—</span>;

              const hours = Math.floor(Math.abs(totalMinutes) / 60);
              const minutes = Math.abs(totalMinutes) % 60;
              const timeStr = `${hours}:${minutes.toString().padStart(2, '0')}`;
              const colorClass = totalMinutes < 0 ? "text-green-600" : "text-red-600";
              return <span className={colorClass}>{timeStr}</span>;
            })()}
          </div>
        </div>

        {/* Total Variance Cost */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center gap-1 mb-1">
            <span className="text-sm text-gray-600">Total Variance Cost</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0 hover:bg-gray-100"
              onClick={helpDialogDisclosure.onOpen}
            >
              <HelpCircleIcon className="h-3 w-3 text-gray-500" />
            </Button>
          </div>
          <div className="text-2xl font-semibold">
            {(() => {
              const totalCostCents = summaryStats.totalVarianceCost;
              const costDollars = Math.abs(totalCostCents) / 100;
              const formattedCost = costDollars.toLocaleString('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
              });
              const colorClass = totalCostCents < 0 ? "text-green-600" : "text-red-600";

              return (
                <VarianceCostTooltip
                  totalCostCents={totalCostCents}
                  formattedCost={formattedCost}
                  colorClass={colorClass}
                />
              );
            })()}
          </div>
        </div>
      </div>

      {/* Data Table */}
      <div className="space-y-4">
        {/* Table with border */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <Table>
            <TableHeader className="bg-blue-50">
              {map(table.getHeaderGroups(), (headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {map(headerGroup.headers, (header) => {
                    return (
                      <TableHead key={header.id} className="text-gioaBlue">
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    )
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                map(table.getRowModel().rows, (row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {map(row.getVisibleCells(), (cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                    <div className="text-center py-8">
                      <Text muted>No time punch data found</Text>
                      <Text muted className="text-sm mt-1">
                        Upload time punch data to get started
                      </Text>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <DataTablePagination table={table} />
      </div>

      {/* Variance Filter Dialog */}
      <VarianceFilterDialog
        isOpen={varianceFilterOpen}
        onClose={() => setVarianceFilterOpen(false)}
        draftPunchType={draftPunchType}
        setDraftPunchType={setDraftPunchType}
        draftPunchTiming={draftPunchTiming}
        setDraftPunchTiming={setDraftPunchTiming}
        draftTimeCondition={draftTimeCondition}
        setDraftTimeCondition={setDraftTimeCondition}
        draftTimeThreshold={draftTimeThreshold}
        setDraftTimeThreshold={setDraftTimeThreshold}
        onReset={resetVarianceFilter}
        onApply={applyVarianceFilter}
      />

      {/* Pay Rate Management Modals */}
      <PayRatesUploadDialog
        isOpen={uploadPayRateDisclosure.isOpen}
        onClose={handleUploadPayRateClose}
        scheduleId={""} // No specific schedule for team totals page
        storeId={storeId}
        people={people}
      />

      <EditPayRatesDialog
        isOpen={editPayRateDisclosure.isOpen}
        onClose={handleEditPayRateClose}
        scheduleId={""} // No specific schedule for team totals page
        storeId={storeId}
        people={people}
      />

      {/* Help Dialog for Total Labor Variance */}
      <TotalLaborVarianceHelpDialog
        isOpen={helpDialogDisclosure.isOpen}
        onClose={helpDialogDisclosure.onClose}
      />
    </div>
  );
}
