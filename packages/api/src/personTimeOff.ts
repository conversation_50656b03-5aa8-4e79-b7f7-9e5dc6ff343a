import {DateTimeRange, DtRange} from "./timeSchemas";
import * as Prisma from "@prisma/client";
import {type Prisma as P} from "@prisma/client";
import {
  approvedTimeOff,
  ApprovedTimeOff,
  BaseTimeOff,
  CancelledTimeOff,
  DeclinedTimeOff,
  pendingTimeOff,
  PendingTimeOff,
  PersonTimeOff,
  TimeOffAdminDto,
  TimeOffDto,
  TimeOffStatusFilter
} from "./personTimeOff.schemas";
import {PersonWithJobAndImage, toMinimalPersonDto} from "./schema.converters";
import {GetImageUrl} from "./types";
import Result, {err, ok} from "./true-myth/result";
import {filter, map, omit, some} from "lodash";
import {doDateTimeRangesOverlap, dtRangeToDateRange, getDayRange, isDtRangeAllDay, utcNow} from "./date.util";
import {ScheduleEvent} from "./scheduleEventSchemas";
import {subDays} from "date-fns";
import {getEarliestValidTimeOffRequestStartTime, TimeOffRestriction} from "./timeOffRestrictions";
import {DateTime} from "luxon";
import {SchedulePersonDto, SchedulePersonTimeOffDto} from "./schedulePersonDto";


export function validateTimeOffRequest({
                                         id,
                                         range,
                                         personRequests,
                                         timezone,
                                         scheduleEvents,
                                         timeOffRestrictions,
                                         now,
                                         submitterCanBypassTimeOffRestriction
                                       }: {
  id: string;
  range: DateTimeRange;
  personRequests: Array<ApprovedTimeOff | PendingTimeOff>;
  scheduleEvents: Array<ScheduleEvent>;
  timeOffRestrictions: TimeOffRestriction;
  timezone: string;
  now: Date;
  submitterCanBypassTimeOffRestriction?: boolean;
}): Result<boolean, string> {
  if (range.end <= range.start) {
    return err("End time must be after start time");
  }

  // can't request time off in the past
  if (range.end < now) {
    return err("Can't request time off in the past");
  }

  const overlappingRequests = filter(personRequests, req => {
    return doDateTimeRangesOverlap(req.range, range) && req.id !== id;
  });

  if (overlappingRequests.length > 0) {
    return err("There is already a time off request for this period.");
  }

  // check if any of the schedule events restrict time off requests
  const eventsOverlappingRange = filter(scheduleEvents, evt => {
    return doDateTimeRangesOverlap(evt.range, range);
  });

  const eventsRestrictTimeOff = some(eventsOverlappingRange, evt => {
    return Boolean(evt.isTimeOffRestricted);
  });

  const earliestValidStartTime = getEarliestValidTimeOffRequestStartTime({
    now: DateTime.fromJSDate(now, {zone: timezone}),
    ...timeOffRestrictions,
    timezone: timezone,
  })

  const isBeforeValidStartTime = range.start < earliestValidStartTime.toJSDate();

  if (eventsRestrictTimeOff && !submitterCanBypassTimeOffRestriction) { // bad boy
    return err("You cannot request time off in this period because it overlaps with a scheduled event that restricts time off requests. Check the calendar tab to see scheduled events.");
  }

  if (isBeforeValidStartTime && !submitterCanBypassTimeOffRestriction) { // bad boy
    return err(`You cannot request time off in this period because it conflicts with store policy. Requested time off must start later than ${earliestValidStartTime.toLocaleString(DateTime.DATETIME_MED_WITH_WEEKDAY)}`);
  }

  return ok(true);
}

/**
 * Create a pending time off request.
 * @param id
 * @param storeId
 * @param humanId
 * @param submittedAt
 * @param submittedByPersonId
 * @param timeOffType
 * @param submittedReason
 * @param now
 * @param range
 * @param personId
 * @param personRequests
 * @param scheduleEvents The schedule events for the store that overlap with the time off request's time range. This is used to check if any of those events restrict time off requests.
 * @param timeOffRestrictions
 * @param submitterCanBypassTimeOffRestriction Can the person who submitted the time off request bypass the time off restrictions?
 * @param timezone
 */
export function createPendingTimeOffRequest({
                                              id,
                                              storeId,
                                              submittedAt,
                                              submittedByPersonId,
                                              timeOffType,
                                              submittedReason,
                                              now,
                                              range,
                                              personId,
                                              personRequests,
                                              scheduleEvents,
                                              timeOffRestrictions,
                                              submitterCanBypassTimeOffRestriction,
                                              timezone
                                            }: {
  id: string,
  storeId: string,
  submittedAt: Date,
  submittedByPersonId: string,
  personId: string,
  now: Date,
  range: DateTimeRange;
  timeOffType: string | undefined,
  submittedReason: string,
  personRequests: Array<ApprovedTimeOff | PendingTimeOff>;
  scheduleEvents: Array<ScheduleEvent>;
  timeOffRestrictions: TimeOffRestriction;
  submitterCanBypassTimeOffRestriction?: boolean;
  timezone: string;
}): Result<PendingTimeOff, string> {
  const newReq: PendingTimeOff = {
    id, submittedAt, submittedReason, range, personId, storeId,
    submittedByPersonId, timeOffType,
    isApproved: false,
    isDeclined: false,
    isCancelled: false,
    isSubmitted: true,
  }

  const validationResult = validateTimeOffRequest({
    id: newReq.id,
    range: newReq.range,
    personRequests: personRequests,
    scheduleEvents: scheduleEvents,
    timeOffRestrictions: timeOffRestrictions,
    timezone: timezone,
    now: now,
    submitterCanBypassTimeOffRestriction: submitterCanBypassTimeOffRestriction,
  });
  if (validationResult.isErr) {
    return err(validationResult.error);
  }

  return ok(newReq);
}

export function approveTimeOffRequest(request: PendingTimeOff, {approvedAt, approvedByPersonId, approvedReason}: {
  approvedAt: Date,
  approvedReason?: string,
  approvedByPersonId: string;
}): ApprovedTimeOff {
  return {
    ...request,
    isApproved: true,
    approvedAt,
    approvedByPersonId,
    approvedReason
  }
}

export function declineTimeOffRequest(request: PendingTimeOff, {declinedAt, declinedByPersonId, declinedReason}: {
  declinedAt: Date,
  declinedReason: string,
  declinedByPersonId: string
}): DeclinedTimeOff {
  return {
    ...request,
    isDeclined: true,
    declinedAt,
    declinedReason,
    declinedByPersonId
  }
}

export function updateTimeOffRequest(request: PendingTimeOff | ApprovedTimeOff, {
  range,
  timeOffType,
  updatedReason,
  updatedByPersonId,
  updatedAt,
}: {
  range: DateTimeRange;
  timeOffType: string;
  updatedReason: string;
  updatedByPersonId: string;
  updatedAt: Date;
}): PendingTimeOff | ApprovedTimeOff {
  return {
    ...request,
    range,
    timeOffType,
    updatedReason,
    updatedByPersonId,
    updatedAt
  }
}

export type CancellableTimeOff = PendingTimeOff | ApprovedTimeOff;

export function cancelTimeOffRequest(request: CancellableTimeOff, {
  cancelledAt,
  cancelledByPersonId,
  cancelledReason
}: {
  cancelledAt: Date;
  cancelledByPersonId: string;
  cancelledReason?: string;
}): CancelledTimeOff {
  return {
    ...request,
    isApproved: false,
    isCancelled: true,
    cancelledAt,
    cancelledByPersonId,
    cancelledReason
  }
}

export function toTimeOffCreateInput(timeOff: PersonTimeOff): P.PersonTimeOffUncheckedCreateInput {
  return {
    id: timeOff.id,
    personId: timeOff.personId,
    storeId: timeOff.storeId,
    isCancelled: timeOff.isCancelled,
    isApproved: timeOff.isApproved,
    declinedAt: timeOff.declinedAt,
    isDeclined: timeOff.isDeclined,
    declinedReason: timeOff.declinedReason,
    approvedAt: timeOff.approvedAt,
    cancelledAt: timeOff.cancelledAt,
    isSubmitted: timeOff.isSubmitted,
    submittedAt: timeOff.submittedAt,
    personTimeOffTypeTitle: timeOff.timeOffType,
    // personTimeOffType: {
    //   connect: {
    //     storeId_title: {
    //       storeId: timeOff.storeId,
    //       title: timeOff.timeOffType
    //     }
    //   }
    // },
    submittedReason: timeOff.submittedReason,
    start: timeOff.range.start,
    end: timeOff.range.end,
    approvedReason: timeOff.approvedReason,
    approvedByPersonId: timeOff.approvedByPersonId,
    declinedByPersonId: timeOff.declinedByPersonId,
    cancelledByPersonId: timeOff.cancelledByPersonId,
    cancelledReason: timeOff.cancelledReason,
    submittedByPersonId: timeOff.submittedByPersonId,
    updatedByPersonId: timeOff.updatedByPersonId,
    updatedReason: timeOff.updatedReason,
  }
}

export function toTimeOffUpdateInput(timeOff: PersonTimeOff): P.PersonTimeOffUncheckedUpdateInput {
  return omit(toTimeOffCreateInput(timeOff), ["id", "humanId", "personId", "storeId"]);
}

export function hydrateTimeOff(timeOff: Prisma.PersonTimeOff): BaseTimeOff {
  return {
    id: timeOff.id,
    personId: timeOff.personId,
    storeId: timeOff.storeId,
    range: {start: timeOff.start, end: timeOff.end},
    timeOffType: timeOff.personTimeOffTypeTitle ?? undefined,
    submittedReason: timeOff.submittedReason ?? undefined,
    isApproved: timeOff.isApproved,
    isDeclined: timeOff.isDeclined,
    isCancelled: timeOff.isCancelled,
    isSubmitted: timeOff.isSubmitted,
    submittedAt: timeOff.submittedAt ?? undefined,
    approvedAt: timeOff.approvedAt ?? undefined,
    declinedAt: timeOff.declinedAt ?? undefined,
    cancelledAt: timeOff.cancelledAt ?? undefined,
    declinedReason: timeOff.declinedReason ?? undefined,
    approvedReason: timeOff.approvedReason ?? undefined,
    cancelledByPersonId: timeOff.cancelledByPersonId ?? undefined,
    declinedByPersonId: timeOff.declinedByPersonId ?? undefined,
    approvedByPersonId: timeOff.approvedByPersonId ?? undefined,
    submittedByPersonId: timeOff.submittedByPersonId ?? undefined,
    cancelledReason: timeOff.cancelledReason ?? undefined,
    updatedByPersonId: timeOff.updatedByPersonId ?? undefined,
    updatedReason: timeOff.updatedReason ?? undefined,
  }
}

export function toPendingTimeOff(timeOff: Prisma.PersonTimeOff): PendingTimeOff {
  return pendingTimeOff.parse(hydrateTimeOff(timeOff));
}

export function narrowToPendingTimeOff(timeOff: PersonTimeOff): PendingTimeOff {
  return pendingTimeOff.parse(timeOff);
}

export function toApprovedTimeOff(timeOff: Prisma.PersonTimeOff): ApprovedTimeOff {
  return approvedTimeOff.parse(hydrateTimeOff(timeOff));
}

export function toCancellableTimeOffRequest(timeOff: PersonTimeOff): CancellableTimeOff {
  const status = getTimeOffStatus(timeOff);
  if (status === "pending") {
    return pendingTimeOff.parse(timeOff);
  }
  if (status === "approved") {
    return approvedTimeOff.parse(timeOff);
  }

  throw new Error("Invalid time off request status");
}

export function getTimeOffStatus(timeOff: {
  isApproved: boolean,
  isDeclined: boolean,
  isCancelled: boolean,
  isSubmitted: boolean,
}): TimeOffStatusFilter {
  if (timeOff.isApproved) {
    return "approved";
  }
  if (timeOff.isDeclined) {
    return "declined";
  }
  if (timeOff.isCancelled) {
    return "cancelled";
  }
  if (timeOff.isSubmitted) {
    return "pending";
  }
  return "pending";
}

export function toTimeOffDto(timeOffRow: Prisma.PersonTimeOff): TimeOffDto {
  return {
    ...hydrateTimeOff(timeOffRow),
    status: getTimeOffStatus(timeOffRow),
  }
}

export function toTimeOffAdminDto(ctx: {
  getImageUrl: GetImageUrl;
}, timeOffRow: Prisma.PersonTimeOff & {
  approvedByPerson: PersonWithJobAndImage | null,
  person: PersonWithJobAndImage,
  declinedByPerson: PersonWithJobAndImage | null,
  submittedByPerson: PersonWithJobAndImage | null,
}, storeId?: string): TimeOffAdminDto {
  return {
    ...hydrateTimeOff(timeOffRow),
    status: getTimeOffStatus(timeOffRow),
    approvedByPerson: timeOffRow.approvedByPerson ? toMinimalPersonDto(ctx, timeOffRow.approvedByPerson) : undefined,
    person: toMinimalPersonDto(ctx, timeOffRow.person, storeId),
    declinedByPerson: timeOffRow.declinedByPerson ? toMinimalPersonDto(ctx, timeOffRow.declinedByPerson) : undefined,
    submittedByPerson: timeOffRow.submittedByPerson ? toMinimalPersonDto(ctx, timeOffRow.submittedByPerson) : undefined,
  }
}

export function getTimeOffStatusWhereCondition(status: TimeOffStatusFilter | TimeOffStatusFilter[], {
  pastDays,
  dateRange,
}: {
  pastDays?: number;
  dateRange?: DateTimeRange;
}): Prisma.Prisma.PersonTimeOffWhereInput {
  const historyStartDate = pastDays ? subDays(utcNow(), pastDays) : undefined;
  const statuses = Array.isArray(status) ? status : [status];

  const filterOptions = {
    all: {isSubmitted: true},
    expired: {isSubmitted: true, isApproved: false, isDeclined: false, isCancelled: false},
    pending: {isSubmitted: true, isApproved: false, isDeclined: false, isCancelled: false},
    approved: {isApproved: true},
    declined: {isDeclined: true},
    cancelled: {isCancelled: true},
  }

  return {
    OR: map(statuses, (singleStatus) => {
      const searchQuery = dateRange !== undefined
        ? {
          OR: [{
            start: {lte: dateRange.end},
            end: {gte: dateRange.start},
          }, {
            start: {gte: dateRange.start},
            end: {lte: dateRange.end},
          }]
        }
        : {
          OR: [
            ...historyStartDate
              ? [{createdAt: {gte: historyStartDate}}]
              : [],
            {
              start: {gte: utcNow()},
            }, {
              end: {gte: utcNow()},
            }]
        };

      return {
        ...filterOptions[singleStatus],
        ...singleStatus === "expired"
          // expired means all pending in the past
          ? {end: {lt: utcNow()}}
          // otherwise show all in future
          : searchQuery
      }
    })
  }

}

export function getPersonTimeOffOnDay({person, day, timezone}: {
  person: SchedulePersonDto,
  day: DateTime;
  timezone: string
}): SchedulePersonTimeOffDto[] {
  const dayRange = dtRangeToDateRange(getDayRange(day.setZone(timezone)));
  const filterToday = (timeOff: SchedulePersonTimeOffDto[]) => filter(timeOff, to => doDateTimeRangesOverlap(dayRange, to));

  const approved = filterToday(person.timeOff);
  const pending = filterToday(person.pendingTimeOff);
  return [...approved, ...pending];
}

/**
 * A time off is considered all day if it engulfs the entire day or if it goes from midnight to 11:59:59 PM
 * @param timeOff
 * @param day
 */
export function isTimeOffAllDay(timeOff: DtRange, day: DateTime): boolean {
  return isDtRangeAllDay(timeOff, day);
}
