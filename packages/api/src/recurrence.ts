import {DateTime} from "luxon";
import {DtRang<PERSON>} from "./timeSchemas";
import {RRule, rrulestr} from 'rrule';
import {map} from "lodash";
import {divideRange} from "./date.util";
import {defaultPriority, ScheduledActionSchema} from "./scheduledActions.schemas";

/**
 * Get the occurrences of an RRule in the given time range. Expects the RRule DTSTART to be in UTC.
 * @param rrule
 * @param range
 * @param timezone
 */
export function getRRuleOccurrences({rrule, range, timezone}: { rrule: RRule, range: DtRange, timezone: string }): DateTime[] {
  // rrule interprets the range start and end as floating and in the system local timezone. For example, if my machine
  // is in Denver, and I pass in 2024-01-01T00:00:00.000Z (midnight) as the range start (it ignores the "Z"), then the rrule will return occurrences
  // of the event starting at 2024-01-01T00:00:00.000 in Denver. If the rrule has a tzid of "America/New_York", then
  // that means an event starting at midnight in New York will NOT be returned, since asking for events starting at midnight
  // in Denver means asking for events starting at 2AM in New York. Therefore, we have to convert the range to
  // use this weird floating time that rrule uses.
  const start = range.start.toLocal().setZone("UTC", { keepLocalTime: true }).toJSDate();
  const end = range.end.toLocal().setZone("UTC", { keepLocalTime: true }).toJSDate();

  const dates = rrule.between(start, end, true);

  // The dates returned by rrule are in system local time, with the "Z" at the end of the string ignored.
  // mapping to get the times in true UTC, per https://github.com/jkbrzt/rrule?tab=readme-ov-file#important-use-utc-dates
  return map(dates, date =>
    DateTime.fromJSDate(date)
      .toUTC()
      .setZone("local", { keepLocalTime: true })
      .setZone(timezone)
  )
}

/**
 * Get the date times for the events that need to be created for in the given time range.
 * If the generator does not have an rrule, then there will be one event at the generator's startAt.
 * If the generator has an rrule, then there will be an event for each occurrence of the rrule in the given range.
 * @param generator A generic concept of "generator". This could be e.g. a checklistgenerator.
 * @param range
 * @param timezone
 */
export function generateEventOccurrences({generator, range, timezone}: {
  generator: {
    start: Date;
    rrule?: string | null;
  };
  range: DtRange;
  timezone: string;
}): DateTime[] {
  if (generator.rrule) {
    // when using tzid with rrule, it interprets the dtstart as a "floating" time in tzid TZ. Imagine you're
    // creating a calendar event in Denver. You select 9AM on Jan 1, 2024 as the start time. To represent that accurately here,
    // we need to pass in America/Denver as the tzid and 2024-01-01T:09:00:00.000Z as the dtstart. Note that even though
    // the dtstart has a "Z" at the end, rrule interprets it as a local time according to the tzid passed in.
    const floatingDtStart = DateTime.fromJSDate(generator.start, {zone: timezone}).setZone("UTC", {keepLocalTime: true}).toJSDate();

    const rrule = rrulestr(generator.rrule, {
      dtstart: floatingDtStart,
      tzid: timezone
    });
    return getRRuleOccurrences({
      rrule,
      range,
      timezone: timezone
    });
  } else {
    return [DateTime.fromJSDate(generator.start, {zone: timezone})];
  }
}

/**
 * Get the scheduled actions that keep a recurrence scheduled out in the future at least 2/3rds of the given range.
 * So for example if you provide a range of 18 months, then the recurrence will be scheduled out at least 12 months
 * at all times.
 *
 * The generate more action, on executing, should generate more events for the recurrence and insert them into the database.
 * It's your responsibility to provide an actionType that will do that.
 *
 * @param range
 * @param actionType
 * @param checklistGeneratorId
 * @param genScheduledActionId
 */
export function getRRuleMaintenanceScheduledActions({range, actionType, checklistGeneratorId, genScheduledActionId}: {
  range: DtRange;
  genScheduledActionId: () => string;
  checklistGeneratorId?: string;
  actionType: string;
}): ScheduledActionSchema[] {
  // schedule a generate more action at the time of the first 1/3rd of the total generation range.
  // E.g. if range is 18 months from now, then we'll generate the next 18 months after 6 months have elapsed.
  const thirds = divideRange(range, 3);
  const firstThird = thirds[0];
  if (!firstThird.end) throw new Error("Could not find first third of range");

  const genMoreAction: ScheduledActionSchema = {
    id: genScheduledActionId(),
    checklistGeneratorId: checklistGeneratorId,
    notBefore: firstThird.end.toJSDate(),
    notAfter: firstThird.end.plus({weeks: 1}).toJSDate(),
    actionType: actionType,
    actionPayload: {},
    actionVersion: "1.0.0",
    priority: defaultPriority,
  }

  return [genMoreAction];
}
