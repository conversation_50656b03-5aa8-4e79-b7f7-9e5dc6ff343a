import {employedProcedure, employedStatsProcedure, t} from "../../trpc";
import {toSecureStoreIdOrThrow} from "../../authorization.util";
import {AttachmentMediaType, attachmentMediaType, FileAttachmentDto, fileAttachmentDto} from "../../fileAttachment.dto";
import {getFileAttachmentS3Key, getFileS3KeyPrefix} from "../../fileAttachment.util";
import {personId, presignedPostDto} from "../../schemas";
import {allowRateOrThrow} from "../../rateLimiting";
import {genFileAttachmentId, genFileId} from "../../fileAttachment.schemas";
import {dateTimeRange, isoWeekDateWithDay} from "../../timeSchemas";
import {z} from "zod";
import {
  ArchiveChecklistTemplateCommand,
  CreateChecklistTemplateCommand,
  DeleteChecklistTemplateItemCommand,
  DuplicateChecklistTemplateCommand,
  ReorderChecklistTemplateItemsCommand,
  UpdateChecklistTemplateCommand,
  UpsertChecklistTemplateItemCommand
} from "../../checklists/checklistTemplate/checklistTemplateCommand";
import {genChecklistId, genChecklistTemplateId} from "../../checklist.schemas";
import {handleCommandAndUpdateProjections} from "../../eventSourcing/handleCommandAndUpdateProjections";
import {
  anonymousChecklistTemplateDto,
  checklistItemInstructionUploadDto,
  checklistTemplateDto,
  getChecklistTemplateItemFileAttachmentIds,
  itemRequirementDto,
  toAnonymousChecklistTemplateDto,
  toChecklistTemplateDto
} from "../../checklists/checklistTemplate/checklistTemplateDtos";
import {ChecklistTemplate} from "../../checklists/checklistTemplate/checklistTemplateTypes";
import {filter, flatMap, map, orderBy, some, unionBy, values} from "lodash";
import {rruleOptionsSchema} from "../../calendar/recurrenceSet/recurrenceRule.types";
import {
  ArchiveChecklistCommand,
  CompleteChecklistItemCommand,
  CreateChecklistCommand,
  DeleteChecklistCommand,
  DeleteChecklistItemCommand,
  ReorderChecklistItemsCommand,
  UnarchiveChecklistCommand,
  UncompleteChecklistItemCommand,
  UpdateChecklistCommand,
  UpsertChecklistItemCommand
} from "../../checklists/checklist/checklistCommand";
import {constructRRule} from "../../calendar/recurrenceSet/recurrenceRule";
import {
  constructChecklistAssignmentRule,
  evaluateChecklistsAssignments,
  getLiveChecklistsAssignedToPerson as getLiveChecklists2AssignedToPerson
} from "../../checklists/checklistAssignment/checklistAssignment";
import {constructChecklistTiming, getStart, isExplicitTiming} from "../../checklists/checklistTiming/checklistTiming";
import {DateTime} from "luxon";
import {getEventRange, getTiming} from "../../calendar/vevent/vevent";
import {getTimingStart} from "../../calendar/vevent/veventTiming";
import {dateRangeToDtRange, doDateTimeRangesOverlap} from "../../date.util";
import {
  ChecklistInstanceDto,
  checklistInstanceWithInteractionsDto,
  checklistItemId,
  checklistUpdateType,
  requirementCompletionsDto,
  toChecklistInstanceDto,
  toChecklistInstanceWithInteractionsDto
} from "../../checklists/checklist/checklistDto";
import {recurrenceIdSchema} from "../../calendar/vevent/veventDtos";
import {constructRecurrenceIdFromDate, getRecurrenceDateTime} from "../../calendar/recurrenceSet/recurrenceId";
import * as perm from "../../permissionChecks"
import {canGetChecklistsForShiftLeader} from "../../permissionChecks"
import {domainEventId} from "../../eventSourcing/domainEvent";
import {constructChecklistAlarms} from "../../checklists/checklistAlarm/checklistAlarm";
import {getArchived, getChecklistTiming} from "../../checklists/checklist/checklist";
import {toShiftWithAbsRange} from "../../checklists";
import {TRPCError} from "@trpc/server";
import {getPresignedPost} from "../../getPresignedPost";
import {getS3Key} from "../../getS3Key";
import {getItems} from "../../checklists/checklistTemplate/checklistTemplate";
import {checklistTemplateCopiedEvent} from "../../checklists/checklistTemplate/checklistTemplateEvent";
import {append, getEventStore} from "../../eventSourcing/eventStore";
import {getChecklistTemplateProjector} from "../../checklists/checklistTemplate/checklistTemplateProjector";
import {checklistTemplateEvolve} from "../../checklists/checklistTemplate/checklistTemplateEvolve";
import {CopyObjectCommand} from "@aws-sdk/client-s3";
import * as Sentry from "@sentry/node";

export const checklist2Router = t.router({
  /**
   * Create a new checklist template
   */
  createChecklist: employedProcedure
    .input(z.object({
      storeId: z.string(),
      title: z.string(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
    }))
    .output(z.object({
      id: z.string(),
    }))
    .mutation(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      if (!perm.canCreateChecklistTemplate(ctx.checkAllowed, {businessId: ctx.businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to create checklist templates"
        });
      }

      const newChecklistTemplateId = genChecklistTemplateId();
      const command: CreateChecklistTemplateCommand = {
        type: "CreateChecklistTemplate",
        checklistTemplateId: newChecklistTemplateId,
        storeId: storeId,
        title: input.title,
        description: input.description,
        tags: input.tags ?? [],
      }

      await handleCommandAndUpdateProjections(ctx, command);

      return {
        id: newChecklistTemplateId,
      }
    }),

  getChecklistTemplate: employedProcedure
    .input(z.object({
      storeId: z.string(),
      checklistTemplateId: z.string(),
    }))
    .output(z.object({
      template: checklistTemplateDto,
      timezone: z.string()
    }))
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      if (!perm.canGetChecklistTemplates(ctx.checkAllowed, {businessId: ctx.businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view checklist templates"
        });
      }

      const template = await ctx.db.checklistTemplate.getChecklistTemplate(storeId, input.checklistTemplateId);
      const fileIds = flatMap(template.items, getChecklistTemplateItemFileAttachmentIds)
      const files = await ctx.db.checklistTemplate.getFileAttachments(storeId, fileIds);

      return {
        template: toChecklistTemplateDto({getImageUrl: ctx.getImageUrl, files}, template),
        timezone: await ctx.db.store.getStoreTimezone(storeId),
      }
    }),

  getChecklist: employedProcedure
    .input(z.object({
      storeId: z.string(),
      checklistId: z.string(),
      recurrenceId: recurrenceIdSchema.optional(),
    }))
    .output(z.object({
      checklist: checklistInstanceWithInteractionsDto,
      canDelete: z.boolean(),
      canUpdateSeries: z.boolean(),
      canEdit: z.boolean(),
      canAssign: z.boolean(),
      canArchive: z.boolean(),
      timezone: z.string()
    }))
    .query(async ({ctx, input}) => {
      const checkAllowed = ctx.checkAllowed;
      const businessId = ctx.businessId;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const timezone = await ctx.db.store.getStoreTimezone(storeId)

      const checklist = await ctx.db.checklist.getChecklistWithInteractionsAndFiles(businessId, storeId, {
        recurrenceId: input.recurrenceId ? constructRecurrenceIdFromDate(input.recurrenceId) : undefined,
        checklistId: input.checklistId
      })

      const [checklistWithAssignments] = await evaluateChecklistsAssignments({
        storeId,
        businessId: ctx.businessId,
        db: ctx.db,
        getImageUrl: ctx.getImageUrl,
        timezone,
        checklists: [checklist]
      })

      const dtoCtx = {
        getImageUrl: ctx.getImageUrl,
        files: checklist.files
      };
      const dto = toChecklistInstanceWithInteractionsDto(dtoCtx, {...checklistWithAssignments, includeItems: true})

      // cannot edit a checklist in the past
      const now = DateTime.now().setZone(timezone);
      const checklistInFuture = getEventRange(checklist).end > now;
      const canEdit = checklistInFuture && perm.canUpdateChecklist(checkAllowed, {businessId, storeId});

      const shiftLeaderShift = await ctx.db.scheduling.findFirstShiftLeaderShiftAtTime(storeId, {
        assignedPersonId: ctx.auth.user.person.id,
        time: now.toJSDate(),
      })

      // can assign if they're a checklist manager or a shift leader
      const canAssign = checklistInFuture && (perm.canAssignChecklist(checkAllowed, {
        businessId,
        storeId
      }) || Boolean(shiftLeaderShift));

      // can archive overdue checklists
      const checklistInPast = dto.end <= new Date();
      const isChecklistComplete = checklist.isComplete;
      const archive = getArchived(checklist);
      const canArchive = !archive?.isArchived && !isChecklistComplete && checklistInPast && (perm.canArchiveChecklist(checkAllowed, {
        businessId,
        storeId
      }) || Boolean(shiftLeaderShift));

      return {
        checklist: dto,
        canDelete: perm.canDeleteChecklists(checkAllowed, {businessId, storeId}),
        canUpdateSeries: perm.canUpdateChecklistSeries(checkAllowed, {businessId, storeId}),
        canEdit: canEdit,
        canAssign: canAssign,
        canArchive: canArchive,
        timezone: timezone
      }
    }),

  getChecklist2: employedProcedure
    .input(z.object({
      storeId: z.string(),
      checklistId: z.string(),
      recurrenceId: recurrenceIdSchema.optional(),
    }))
    .output(z.object({
      checklist: checklistInstanceWithInteractionsDto,
      canDelete: z.boolean(),
      canUpdateSeries: z.boolean(),
      canEdit: z.boolean(),
      canAssign: z.boolean(),
      canArchive: z.boolean(),
      timezone: z.string()
    }))
    .query(async ({ctx, input}) => {
      const checkAllowed = ctx.checkAllowed;
      const businessId = ctx.businessId;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const timezone = await ctx.db.store.getStoreTimezone(storeId)

      const checklist = await ctx.db.checklist.getChecklistWithInteractionsAndFiles(businessId, storeId, {
        recurrenceId: input.recurrenceId ? constructRecurrenceIdFromDate(input.recurrenceId) : undefined,
        checklistId: input.checklistId
      })

      const [checklistWithAssignments] = await evaluateChecklistsAssignments({
        storeId,
        businessId: ctx.businessId,
        db: ctx.db,
        getImageUrl: ctx.getImageUrl,
        timezone,
        checklists: [checklist]
      })

      const dtoCtx = {
        getImageUrl: ctx.getImageUrl,
        files: checklist.files
      };
      const dto = toChecklistInstanceWithInteractionsDto(dtoCtx, {...checklistWithAssignments, includeItems: true})

      // cannot edit a checklist in the past
      const now = DateTime.now().setZone(timezone);
      const canEdit = perm.canUpdateChecklist(checkAllowed, {businessId, storeId});

      const shiftLeaderShift = await ctx.db.scheduling.findFirstShiftLeaderShiftAtTime(storeId, {
        assignedPersonId: ctx.auth.user.person.id,
        time: now.toJSDate(),
      })

      // can assign if they're a checklist manager or a shift leader
      const canAssign = (perm.canAssignChecklist(checkAllowed, {
        businessId,
        storeId
      }) || Boolean(shiftLeaderShift));

      // can archive overdue checklists
      const checklistInPast = dto.end <= new Date();
      const isChecklistComplete = checklist.isComplete;
      const archive = getArchived(checklist);
      const canArchive = !archive?.isArchived && !isChecklistComplete && checklistInPast && (perm.canArchiveChecklist(checkAllowed, {
        businessId,
        storeId
      }) || Boolean(shiftLeaderShift));

      return {
        checklist: dto,
        canDelete: perm.canDeleteChecklists(checkAllowed, {businessId, storeId}),
        canUpdateSeries: perm.canUpdateChecklistSeries(checkAllowed, {businessId, storeId}),
        canEdit: canEdit,
        canAssign: canAssign,
        canArchive: canArchive,
        timezone: timezone
      }
    }),

  getChecklistTemplates: employedProcedure
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.object({
      items: z.array(checklistTemplateDto),
    }))
    .query(async ({ctx, input}) => {
      const {businessId,} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      if (!perm.canGetChecklistTemplates(ctx.checkAllowed, {businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view checklist templates"
        });
      }

      const templates = await ctx.db.checklistTemplate.getActiveChecklistTemplates(storeId);
      const items = map(templates, template => toChecklistTemplateDto({
        getImageUrl: ctx.getImageUrl,
        files: []
      }, template));

      return {
        items: items,
      };
    }),

  upsertChecklistTemplateItem: employedProcedure
    .input(z.object({
      storeId: z.string(),
      checklistTemplateId: z.string(),
      itemId: z.string(),
      title: z.string().min(1).max(256),
      description: z.string().min(0).max(2048).optional(),
      attachment: fileAttachmentDto.optional(),
      requirements: z.array(itemRequirementDto),
      instructions: z.array(checklistItemInstructionUploadDto)
    }))
    .output(z.void())
    .mutation(async ({input, ctx}) => {
      const {businessId, storeIds} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      if (!perm.canEditChecklistTemplates(ctx.checkAllowed, {businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to edit checklist templates"
        });
      }

      const command: UpsertChecklistTemplateItemCommand = {
        ...input,
        type: "UpsertChecklistTemplateItem",
        storeId: storeId,
        description: input.description,
        attachment: input.attachment
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  deleteChecklistTemplateItem: employedProcedure
    .input(z.object({
      storeId: z.string(),
      checklistTemplateId: z.string(),
      itemId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({input, ctx}) => {
      const {storeIds, businessId} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      if (!perm.canEditChecklistTemplates(ctx.checkAllowed, {businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to edit checklist templates"
        });
      }

      const command: DeleteChecklistTemplateItemCommand = {
        type: "DeleteChecklistTemplateItem",
        storeId: storeId,
        checklistTemplateId: input.checklistTemplateId,
        itemId: input.itemId,
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  getPresignedPostForChecklistItem: employedProcedure
    .input(z.object({
      storeId: z.string(),
      contentType: z.string(),
      mediaType: attachmentMediaType
    }))
    .output(presignedPostDto)
    .mutation(async ({ctx, input}) => {
      const userId = ctx.auth.user.id;
      const {s3MediaBucket, s3Client} = ctx;

      await allowRateOrThrow({
        key: userId,
        group: "getPresignedPost",
        maxRequestsPerDuration: 200,
        prisma: ctx.prisma
      });

      const {
        businessId,
        employment: userEmployment
      } = ctx
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const twoHundredMegs = 200 * 1024 * 1024;
      const securityPrefix = getFileS3KeyPrefix({
        mediaType: input.mediaType,
        businessId,
        storeId: storeId,
        folderName: "checklist"
      })
      const newId = genFileId();
      const key = getS3Key({prefix: securityPrefix, objectId: newId});
      const presignedPost = await getPresignedPost({
        s3Client: s3Client,
        s3Bucket: s3MediaBucket,
        maxSizeBytes: twoHundredMegs,
        securityPrefix: securityPrefix,
        contentType: input.contentType,
        key: key
      });

      return {
        newId: newId,
        key,
        presignedPost,
      };
    }),

  updateChecklistTemplateItemsOrder: employedProcedure
    .input(z.object({
      storeId: z.string(),
      checklistTemplateId: z.string(),
      itemOrder: z.array(z.string()) // item IDs
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const {businessId} = ctx;
      if (!perm.canEditChecklistTemplates(ctx.checkAllowed, {businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to edit checklist templates"
        });
      }


      const command: ReorderChecklistTemplateItemsCommand = {
        type: "ReorderChecklistTemplateItems",
        storeId: storeId,
        checklistTemplateId: input.checklistTemplateId,
        itemOrder: input.itemOrder,
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  updateChecklistItemsOrder: employedProcedure
    .input(z.object({
      storeId: z.string(),
      checklistId: z.string(),
      recurrenceId: recurrenceIdSchema.optional(),
      itemOrder: z.array(z.string()) // item IDs
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      // TODO permissions
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const command: ReorderChecklistItemsCommand = {
        type: "ReorderChecklistItems",
        storeId: storeId,
        checklistId: input.checklistId,
        recurrenceId: input.recurrenceId ? constructRecurrenceIdFromDate(input.recurrenceId) : undefined,
        itemOrder: input.itemOrder,
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  updateChecklistSettings: employedProcedure
    .input(z.object({
      storeId: z.string(),
      checklistId: z.string(),
      recurrenceId: recurrenceIdSchema.optional(),
      version: domainEventId,
      updateType: checklistUpdateType,
      title: z.string(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      isExplicitTiming: z.boolean(),
      start: z.date().optional(),
      end: z.date().optional(),
      rrule: rruleOptionsSchema.optional(),
      notifyGeneral: z.boolean(),
      notifyOnIncomplete: z.boolean(),
      notifyOnComplete: z.boolean(),
      peopleToNotifyOnIncomplete: z.array(personId),
      peopleToNotifyOnComplete: z.array(personId),
    }).refine(val => {
      if (val.isExplicitTiming) {
        if (!val.start || !val.end) {
          return false;
        }
      }

      if (val.start && val.end) {
        if (val.start.getTime() > val.end.getTime()) {
          return false;
        }
      }

      return true;
    }, {
      message: "Start and end must be provided if isExplicitTiming is true"
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const {businessId} = ctx;
      const timezone = await ctx.db.store.getStoreTimezone(storeId);
      // TODO permissions

      if (input.updateType === "thisAndFuture" || input.updateType === "all") {
        if (!perm.canEditRecurringChecklist(ctx.checkAllowed, {businessId, storeId})) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You are not authorized to edit recurring checklists"
          });
        }
      }

      const command: UpdateChecklistCommand = {
        type: "UpdateChecklist",
        checklistId: input.checklistId,
        timing: constructChecklistTiming({
          isExplicitTiming: input.isExplicitTiming,
          start: input.start ?? new Date(),
          end: input.end,
          timezone: timezone
        }),
        alarms: constructChecklistAlarms({
          notifyOnComplete: input.notifyOnComplete,
          notifyOnIncomplete: input.notifyOnIncomplete,
          notifyGeneral: input.notifyGeneral,
          peopleToNotifyOnComplete: input.peopleToNotifyOnComplete,
          peopleToNotifyOnIncomplete: input.peopleToNotifyOnIncomplete,
        }),
        title: input.title,
        description: input.description,
        tags: input.tags,
        updateType: input.updateType,
        newChecklistId: genChecklistId(),
        version: input.version,
        rrule: input.rrule ? constructRRule(input.rrule) : null,
        assignmentRule: undefined,
        recurrenceId: input.recurrenceId ? constructRecurrenceIdFromDate(input.recurrenceId) : undefined,
        now: DateTime.now(),
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  /**
   * Archive a checklist. Either a single checklist or an instance of a recurring checklist.
   * You can't archive an entire recurring series. You can only archive a single checklist.
   * If you want to "archive" an entire recurring series, you need to delete it.
   */
  archiveChecklist: employedProcedure
    .input(z.object({
      checklistId: z.string(),
      recurrenceId: recurrenceIdSchema.optional(),
      reason: z.string().min(1)
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId} = ctx;
      const storeId = await ctx.db.checklist.getChecklistStore(businessId, input.checklistId);
      if (!perm.canArchiveChecklist(ctx.checkAllowed, {businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to archive checklists"
        });
      }

      const command: ArchiveChecklistCommand = {
        type: "ArchiveChecklist",
        checklistId: input.checklistId,
        recurrenceId: input.recurrenceId ? constructRecurrenceIdFromDate(input.recurrenceId) : undefined,
        reason: input.reason,
        archivedAt: new Date(),
        archivedByPersonId: ctx.currentPersonId,
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  unarchiveChecklist: employedProcedure
    .input(z.object({
      checklistId: z.string(),
      recurrenceId: recurrenceIdSchema.optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId} = ctx;
      const storeId = await ctx.db.checklist.getChecklistStore(businessId, input.checklistId);
      if (!perm.canArchiveChecklist(ctx.checkAllowed, {businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to archive checklists"
        });
      }

      const command: UnarchiveChecklistCommand = {
        type: "UnarchiveChecklist",
        checklistId: input.checklistId,
        recurrenceId: input.recurrenceId ? constructRecurrenceIdFromDate(input.recurrenceId) : undefined,
        unarchivedAt: new Date(),
        unarchivedByPersonId: ctx.currentPersonId,
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  deleteChecklist: employedProcedure
    .input(z.object({
      checklistId: z.string(),
      recurrenceId: recurrenceIdSchema.optional(),
      deleteType: checklistUpdateType,
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId} = ctx;
      const storeId = await ctx.db.checklist.getChecklistStore(businessId, input.checklistId);
      if (!perm.canDeleteChecklists(ctx.checkAllowed, {businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to delete checklists"
        });
      }

      const command: DeleteChecklistCommand = {
        type: "DeleteChecklist",
        checklistId: input.checklistId,
        recurrenceId: input.recurrenceId ? constructRecurrenceIdFromDate(input.recurrenceId) : undefined,
        updateType: input.deleteType,
        now: DateTime.now(),
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  activateChecklistTemplate: employedProcedure
    .input(z.object({
      storeId: z.string(),
      checklistTemplateId: z.string(),

      isAllDay: z.boolean(),
      isExplicitTiming: z.boolean(),
      start: z.date().optional(),
      end: z.date().optional(),
      rrule: rruleOptionsSchema.optional(),

      notifyGeneral: z.boolean(),
      notifyOnIncomplete: z.boolean(),
      notifyOnComplete: z.boolean(),
      peopleToNotifyOnIncomplete: z.array(personId),
      peopleToNotifyOnComplete: z.array(personId),

      assignPeople: z.array(personId),
      assignShiftLead: z.boolean().optional(),
      assignStoreAreaId: z.string().optional(),
      assignScheduleAreaTitle: z.string().optional(),
      assignStorePositionId: z.string().optional(),
    }).refine(val => {
      if (val.isExplicitTiming) {
        if (!val.start || !val.end) {
          return false;
        }
      }
      return true;
    }, {
      message: "Start and end must be provided if isExplicitTiming is true"
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      if (!perm.canActivateChecklistTemplates(ctx.checkAllowed, {businessId: ctx.businessId, storeId: storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to activate checklists"
        });
      }

      const checklistTemplate = await ctx.db.checklistTemplate.getChecklistTemplate(storeId, input.checklistTemplateId);
      const timezone = await ctx.db.store.getStoreTimezone(storeId);

      const command: CreateChecklistCommand = {
        type: "CreateChecklist",
        checklistId: genChecklistId(),
        createdFromTemplate: checklistTemplate,
        storeId: storeId,

        rrule: input.rrule ? constructRRule(input.rrule) : undefined,
        alarms: constructChecklistAlarms({
          notifyOnComplete: input.notifyOnComplete,
          notifyOnIncomplete: input.notifyOnIncomplete,
          notifyGeneral: input.notifyGeneral,
          peopleToNotifyOnComplete: input.peopleToNotifyOnComplete,
          peopleToNotifyOnIncomplete: input.peopleToNotifyOnIncomplete,
        }),

        timing: constructChecklistTiming({
          isExplicitTiming: input.isExplicitTiming,
          start: input.start ?? new Date(),
          end: input.end,
          timezone
        }),

        assignmentRule: constructChecklistAssignmentRule({
          assignPeople: input.assignPeople,
          assignShiftLead: input.assignShiftLead,
          assignStoreAreaId: input.assignStoreAreaId,
          assignScheduleAreaTitle: input.assignScheduleAreaTitle,
          assignStorePositionId: input.assignStorePositionId,
        })
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  updateChecklistTemplate: employedProcedure
    .input(z.object({
      checklistTemplateId: z.string(),
      title: z.string(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional().default([]),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const storeId = await ctx.db.checklist.getChecklistTemplateStore(ctx.businessId, input.checklistTemplateId);
      if (!perm.canEditChecklistTemplates(ctx.checkAllowed, {businessId: ctx.businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to edit checklist templates"
        });
      }

      const command: UpdateChecklistTemplateCommand = {
        type: "UpdateChecklistTemplate",
        checklistTemplateId: input.checklistTemplateId,
        title: input.title,
        description: input.description,
        tags: input.tags,
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  updateChecklistAssign: employedProcedure
    .input(z.object({
      storeId: z.string(),
      checklistId: z.string(),
      recurrenceId: recurrenceIdSchema.optional(),
      version: domainEventId,
      updateType: checklistUpdateType,
      assignPeople: z.array(personId),
      assignShiftLead: z.boolean().optional(),
      assignStoreAreaId: z.string().optional(),
      assignScheduleAreaTitle: z.string().optional(),
      assignStorePositionId: z.string().optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const {businessId} = ctx;
      // TODO permissions

      if (input.updateType === "thisAndFuture" || input.updateType === "all") {
        if (!perm.canEditRecurringChecklist(ctx.checkAllowed, {businessId, storeId})) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You are not authorized to edit recurring checklists"
          });
        }
      }

      const command: UpdateChecklistCommand = {
        type: "UpdateChecklist",
        checklistId: input.checklistId,
        updateType: input.updateType,
        newChecklistId: genChecklistId(),
        version: input.version,
        assignmentRule: constructChecklistAssignmentRule({
          assignPeople: input.assignPeople,
          assignShiftLead: input.assignShiftLead,
          assignStoreAreaId: input.assignStoreAreaId,
          assignScheduleAreaTitle: input.assignScheduleAreaTitle,
          assignStorePositionId: input.assignStorePositionId,
        }),
        recurrenceId: input.recurrenceId ? constructRecurrenceIdFromDate(input.recurrenceId) : undefined,
        now: DateTime.now(),
        timing: undefined,
        rrule: undefined,
        alarms: undefined,
        title: undefined,
        description: undefined,
        tags: undefined,
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  /**
   * Get future, non-live checklist events for a store
   * @param storeId
   * @param date Will return events for start and end of the month that includes this date. The month range
   * is half-inclusive -- [)
   */
  getUpcomingChecklists: employedProcedure
    .input(z.object({
      storeId: z.string(),
      range: dateTimeRange
    }))
    .output(z.custom<{ items: ChecklistInstanceDto[] }>())
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const timezone = await ctx.db.store.getStoreTimezone(storeId)

      if (!perm.canGetUpcomingChecklists(ctx.checkAllowed, {businessId: ctx.businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view upcoming checklists"
        });
      }

      const inputRange = input.range;
      const now = DateTime.now().setZone(timezone);
      const startOfDay = now.startOf("day");
      const queryStart = DateTime.max(DateTime.fromJSDate(inputRange.start), startOfDay);
      const range = {
        start: queryStart,
        end: DateTime.fromJSDate(inputRange.end)
      }

      if (range.end.toMillis() < range.start.toMillis()) {
        return {
          items: []
        }
      }

      const allChecklists = await ctx.db.checklist.getChecklistsInRange(storeId, {
        range: range
      });

      // filter out non-explicit timing and filter out checklists that have already started. An already started checklist might be returned in allChecklists because we're querying for start of day which is before now.
      // We're querying for start of day instead of now for better caching. Since `now` will bust the cache every time it's called.
      const checklists = filter(allChecklists, c => {
        const timing = getChecklistTiming(c);
        const start = getStart(timing);
        return isExplicitTiming(timing) && !getArchived(c)?.isArchived &&
          (start ? start > now.toJSDate() : true);
      })

      const checklistsWithAssignments = await evaluateChecklistsAssignments({
        storeId,
        businessId: ctx.businessId,
        db: ctx.db,
        getImageUrl: ctx.getImageUrl,
        timezone,
        checklists: checklists
      })

      const dtoCtx = {
        getImageUrl: ctx.getImageUrl,
        files: [],
      };
      const dtos = map(checklistsWithAssignments, checklist => toChecklistInstanceDto(dtoCtx, {
        checklist: checklist.checklist,
        assignments: checklist.assignments,
        assignmentSchedule: checklist.assignmentSchedule,
        includeItems: false,
      }));

      return {
        items: dtos
      }
    }),

  getUpcomingChecklistOccurrences: employedProcedure
    .input(z.object({
      storeId: z.string(),
      range: dateTimeRange
    }))
    .output(z.object({
      items: z.array(isoWeekDateWithDay),
    }))
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      // const timezone = await ctx.db.store.getStoreTimezone(storeId)
      //
      // const inputRange = input.range;
      // const queryStart = DateTime.max(DateTime.fromJSDate(inputRange.start), DateTime.now());
      // const range = {
      //   start: queryStart,
      //   end: DateTime.fromJSDate(inputRange.end)
      // }
      //
      // const occurrenceDts = await ctx.db.checklist.getChecklistOccurrencesInRange(storeId, {
      //   range: range
      // });
      //
      // return {
      //   items: map(occurrenceDts, dt => {
      //     const start = dt.setZone(timezone);
      //     return {
      //       year: start.year,
      //       week: start.weekNumber,
      //       day: start.weekday,
      //     }
      //   }),
      // }

      // commented out this procedure because it's very CPU-intensive for a very unimportant feature (showing the little dots on the calendar view in the app for upcoming checklists).
      return {
        items: []
      }
    }),

  getChecklistEventsForShiftLeader: employedProcedure
    .input(z.object({
      storeId: z.string(),
      range: dateTimeRange
    }))
    .output(z.custom<{ items: ChecklistInstanceDto[] }>())
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const timezone = await ctx.db.store.getStoreTimezone(storeId)
      const allChecklists = await ctx.db.checklist.getChecklistsInRange(storeId, {
        range: dateRangeToDtRange(input.range, timezone)
      });
      const activeChecklists = filter(allChecklists, c => {
        const archived = getArchived(c);
        return !archived?.isArchived;
      });

      const checklistsWithAssignments = await evaluateChecklistsAssignments({
        storeId,
        businessId: ctx.businessId,
        db: ctx.db,
        getImageUrl: ctx.getImageUrl,
        timezone,
        checklists: activeChecklists
      })

      const {hasShiftLeaderConsolePermissions, shiftLeaderDayRanges} = await canGetChecklistsForShiftLeader(ctx, {
        timezone,
        storeId,
        range: input.range
      })

      const dtoCtx = {
        getImageUrl: ctx.getImageUrl,
        files: [],
      };
      const allChecklistInstanceDtos = map(checklistsWithAssignments, checklist => toChecklistInstanceDto(dtoCtx, {
        checklist: checklist.checklist,
        assignments: checklist.assignments,
        assignmentSchedule: checklist.assignmentSchedule,
        includeItems: false,
      }));
      const shiftLeaderChecklists = filter(allChecklistInstanceDtos, event => some(shiftLeaderDayRanges, range => doDateTimeRangesOverlap(range, event)));

      return {
        items: hasShiftLeaderConsolePermissions
          ? allChecklistInstanceDtos
          : shiftLeaderChecklists
      }
    }),

  getLiveChecklists: employedProcedure
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.custom<{ items: ChecklistInstanceDto[] }>())
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      if (!perm.canGetLiveManagerChecklists(ctx.checkAllowed, {businessId: ctx.businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view live checklists"
        });
      }

      const timezone = await ctx.db.store.getStoreTimezone(storeId)
      const now = DateTime.now().setZone(timezone);
      const todayRange = {
        start: now.startOf("day"),
        end: now.endOf("day"),
      }

      const allChecklists = await ctx.db.checklist.getChecklistsInRange(storeId, {
        range: {
          start: todayRange.start.minus({days: 8}),
          end: todayRange.end,
        }
      });
      const activeChecklists = filter(allChecklists, c => {
        const archived = getArchived(c);
        return !archived?.isArchived;
      });

      const nowRange = {
        start: now,
        end: now,
      }
      const todayChecklists = filter(activeChecklists, c => {
        const checklistRange = getEventRange(c);
        return doDateTimeRangesOverlap(todayRange, checklistRange)
      })

      // overdue checklists
      const overdue = filter(activeChecklists, c => {
        const checklistRange = getEventRange(c);
        // only show overdue checklists that are overdue today. Don't dig up a bunch of old overdue checklists that nobody gives a rat's ass about anymore.
        return checklistRange.end < now && checklistRange.end > todayRange.start && !c.isComplete;
      })

      // live checklists
      const liveIncomplete = filter(todayChecklists, c => {
        const checklistRange = getEventRange(c);
        return doDateTimeRangesOverlap(nowRange, checklistRange);
      });

      // completed today
      const completedToday = filter(todayChecklists, c => c.isComplete);

      // evaluate assignments
      const checklists = orderBy(unionBy([
        ...overdue,
        ...liveIncomplete,
        ...completedToday,
      ], c => c.id + (c.recurrenceId ? getRecurrenceDateTime(c.recurrenceId).toMillis() : "")), c => getTimingStart(getTiming(c)).toMillis());

      const checklistsWithAssignments = await evaluateChecklistsAssignments({
        storeId,
        businessId: ctx.businessId,
        db: ctx.db,
        getImageUrl: ctx.getImageUrl,
        timezone,
        checklists: checklists
      })

      const dtoCtx = {
        getImageUrl: ctx.getImageUrl,
        files: [],
      };
      const dtos = map(checklistsWithAssignments, checklist => toChecklistInstanceDto(dtoCtx, {
        checklist: checklist.checklist,
        assignments: checklist.assignments,
        assignmentSchedule: checklist.assignmentSchedule,
        includeItems: false,
      }));

      return {
        items: dtos
      }
    }),

  archiveChecklistTemplate: employedProcedure
    .input(z.object({
      storeId: z.string(),
      checklistTemplateId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({input, ctx}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      if (!perm.canEditChecklistTemplates(ctx.checkAllowed, {businessId: ctx.businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to edit checklist templates"
        });
      }

      const command: ArchiveChecklistTemplateCommand = {
        type: "ArchiveChecklistTemplate",
        checklistTemplateId: input.checklistTemplateId,
      }

      await handleCommandAndUpdateProjections(ctx, command);

    }),

  duplicateChecklistTemplate: employedProcedure
    .input(z.object({
      storeId: z.string(),
      checklistTemplateId: z.string(),
    }))
    .output(z.object({
      newTemplateId: z.string(),
    }))
    .mutation(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      if (!perm.canEditChecklistTemplates(ctx.checkAllowed, {businessId: ctx.businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to edit checklist templates"
        });
      }

      const command: DuplicateChecklistTemplateCommand = {
        type: "DuplicateChecklistTemplate",
        storeId,
        checklistTemplateId: input.checklistTemplateId,
        newChecklistTemplateId: genChecklistTemplateId(),
      }

      await handleCommandAndUpdateProjections(ctx, command);

      return {
        newTemplateId: command.newChecklistTemplateId,
      }
    }),

  getPastChecklists: employedProcedure
    .input(z.object({
      storeId: z.string(),
      range: dateTimeRange
    }))
    .output(z.custom<{ items: ChecklistInstanceDto[] }>())
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const timezone = await ctx.db.store.getStoreTimezone(storeId)

      if (!perm.canGetRecordedChecklists(ctx.checkAllowed, {businessId: ctx.businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view past checklists"
        });
      }

      const inputRange = input.range;
      const endOfDay = DateTime.now().setZone(timezone).endOf("day");
      const queryEnd = DateTime.min(DateTime.fromJSDate(inputRange.end), endOfDay);
      const range = {
        start: DateTime.fromJSDate(inputRange.start),
        end: queryEnd
      }

      if (range.end.toMillis() < range.start.toMillis()) {
        return {
          items: []
        }
      }

      const checklists = await ctx.db.checklist.getChecklistsInRange(storeId, {
        range: range
      });

      const checklistsWithAssignments = await evaluateChecklistsAssignments({
        storeId,
        businessId: ctx.businessId,
        db: ctx.db,
        getImageUrl: ctx.getImageUrl,
        timezone,
        checklists: checklists
      })

      const dtoCtx = {
        getImageUrl: ctx.getImageUrl,
        files: [],
      };
      const dtos = map(checklistsWithAssignments, checklist => toChecklistInstanceDto(dtoCtx, {
        checklist: checklist.checklist,
        assignments: checklist.assignments,
        assignmentSchedule: checklist.assignmentSchedule,
        includeItems: false,
      }));

      return {
        items: dtos
      }
    }),

  getPastChecklistOccurrences: employedProcedure
    .input(z.object({
      storeId: z.string(),
      range: dateTimeRange
    }))
    .output(z.object({
      items: z.array(isoWeekDateWithDay),
    }))
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      // const timezone = await ctx.db.store.getStoreTimezone(storeId)
      //
      // const inputRange = input.range;
      // const queryEnd = DateTime.min(DateTime.fromJSDate(inputRange.end), DateTime.now());
      // const range = {
      //   start: DateTime.fromJSDate(inputRange.start),
      //   end: queryEnd
      // }
      //
      // const occurrenceDts = await ctx.db.checklist.getChecklistOccurrencesInRange(storeId, {
      //   range: range
      // });
      //
      // return {
      //   items: map(occurrenceDts, dt => {
      //     const start = dt.setZone(timezone);
      //     return {
      //       year: start.year,
      //       week: start.weekNumber,
      //       day: start.weekday,
      //     }
      //   }),
      // }

      // commented out this procedure because it's very CPU-intensive for a very unimportant feature (showing the little dots on the calendar view in the app for past checklists).
      return {
        items: []
      }
    }),

  getTeamMemberLiveChecklists: employedProcedure
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.custom<{ items: ChecklistInstanceDto[] }>())
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const timezone = await ctx.db.store.getStoreTimezone(storeId)
      const now = DateTime.now().setZone(timezone);
      // do start and end of day for all live views rather than "now". Since now will bust the cache continuously.
      const startOfDay = now.startOf("day");
      const endOfDay = now.endOf("day");

      const shiftRows = await ctx.db.scheduling.getShiftsAssignedToPersonToday(storeId, ctx.currentPersonId, timezone);
      const shiftsAssignedToCurrentPerson = map(shiftRows, toShiftWithAbsRange);

      const checklistsRows = await ctx.db.checklist.getChecklistsInRange(storeId, {
        range: {
          start: startOfDay.minus({days: 8}),
          end: endOfDay,
        }
      });
      const checklists = getLiveChecklists2AssignedToPerson({
        checklists: checklistsRows,
        personId: ctx.currentPersonId,
        shifts: shiftsAssignedToCurrentPerson,
        now,
        timezone,
      });

      const checklistsWithAssignments = await evaluateChecklistsAssignments({
        storeId,
        businessId: ctx.businessId,
        db: ctx.db,
        getImageUrl: ctx.getImageUrl,
        timezone,
        checklists: checklists
      })

      const dtoCtx = {
        getImageUrl: ctx.getImageUrl,
        files: [],
      };
      const dtos = map(checklistsWithAssignments, checklist => toChecklistInstanceDto(dtoCtx, {
        checklist: checklist.checklist,
        assignments: checklist.assignments,
        assignmentSchedule: checklist.assignmentSchedule,
        includeItems: false,
      }));

      return {
        items: dtos
      }
    }),

  completeChecklistItem: employedProcedure
    .input(z.object({
      checklistId: z.string(),
      recurrenceId: recurrenceIdSchema.optional(),
      itemId: checklistItemId,
      requirements: requirementCompletionsDto,
      checklistVersion: domainEventId,
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      // TODO permissions

      const command: CompleteChecklistItemCommand = {
        type: "CompleteChecklistItem",
        checklistId: input.checklistId,
        recurrenceId: input.recurrenceId ? constructRecurrenceIdFromDate(input.recurrenceId) : undefined,
        checklistItemId: input.itemId,
        checklistVersion: input.checklistVersion,
        personId: ctx.currentPersonId,
        requirementCompletions: input.requirements,
        interactedAt: DateTime.now(),
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  uncompleteChecklistItem: employedProcedure
    .input(z.object({
      checklistId: z.string(),
      recurrenceId: recurrenceIdSchema.optional(),
      itemId: checklistItemId,
      checklistVersion: domainEventId,
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      // TODO permissions
      const command: UncompleteChecklistItemCommand = {
        type: "UncompleteChecklistItem",
        checklistId: input.checklistId,
        recurrenceId: input.recurrenceId ? constructRecurrenceIdFromDate(input.recurrenceId) : undefined,
        checklistItemId: input.itemId,
        checklistVersion: input.checklistVersion,
        personId: ctx.currentPersonId,
        interactedAt: DateTime.now(),
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  upsertChecklistItem: employedProcedure
    .input(z.object({
      storeId: z.string(),
      checklistId: z.string(),
      recurrenceId: recurrenceIdSchema.optional(),
      itemId: checklistItemId,
      title: z.string().min(1).max(256),
      description: z.string().min(0).max(2048).optional(),
      attachment: fileAttachmentDto.optional(),
      requirements: z.array(itemRequirementDto),
      instructions: z.array(checklistItemInstructionUploadDto)
    }))
    .output(z.void())
    .mutation(async ({input, ctx}) => {
      const {businessId, storeIds} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      // TODO permissions

      const command: UpsertChecklistItemCommand = {
        type: "UpsertChecklistItem",
        storeId: storeId,
        checklistId: input.checklistId,
        recurrenceId: input.recurrenceId ? constructRecurrenceIdFromDate(input.recurrenceId) : undefined,
        description: input.description,
        itemId: input.itemId,
        title: input.title,
        requirements: input.requirements,
        instructions: input.instructions,
        attachment: input.attachment
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  deleteChecklistItem: employedProcedure
    .input(z.object({
      storeId: z.string(),
      checklistId: z.string(),
      recurrenceId: recurrenceIdSchema.optional(),
      itemId: checklistItemId,
    }))
    .output(z.void())
    .mutation(async ({input, ctx}) => {
      const {storeIds} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      // TODO permissions

      const command: DeleteChecklistItemCommand = {
        type: "DeleteChecklistItem",
        storeId: storeId,
        checklistId: input.checklistId,
        recurrenceId: input.recurrenceId ? constructRecurrenceIdFromDate(input.recurrenceId) : undefined,
        itemId: input.itemId,
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  getChecklistTags: employedProcedure
    .input(z.object({
      storeId: z.string()
    }))
    .output(z.object({
      tags: z.array(z.string()),
    }))
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const tags = await ctx.db.checklist.getChecklistTags(storeId);
      return {
        tags: map(tags, tag => tag.title)
      };
    }),

  addChecklistTag: employedProcedure
    .input(z.object({
      storeId: z.string(),
      tag: z.string(),
    }))
    .output(z.void())
    .mutation(async ({input, ctx}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      if (!perm.canEditChecklistTemplates(ctx.checkAllowed, {businessId: ctx.businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to edit checklist tags"
        });
      }

      await ctx.db.checklist.addChecklistTag(storeId, input.tag);
    }),

  removeChecklistTag: employedProcedure
    .input(z.object({
      storeId: z.string(),
      tag: z.string(),
    }))
    .output(z.void())
    .mutation(async ({input, ctx}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      if (!perm.canEditChecklistTemplates(ctx.checkAllowed, {businessId: ctx.businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to edit checklist tags"
        });
      }

      await ctx.db.checklist.removeChecklistTag(storeId, input.tag);
    }),

  getTagsAcrossAllBusinesses: employedStatsProcedure({feature: "common"})
    .input(z.object({
      ownStoreId: z.string(),
    }))
    .output(z.object({
      tags: z.array(z.string())
    }))
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.ownStoreId);
      if (!perm.canGetChecklistTemplates(ctx.checkAllowed, {businessId: ctx.businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view checklist templates"
        });
      }

      const tags = await ctx.db.checklist.getTagsAcrossAllBusinesses(ctx.businessId);
      return {
        tags: map(tags, tag => tag.title)
      }
    }),

  getChecklistTemplatesAcrossAllBusinesses: employedStatsProcedure({feature: "common"})
    .input(z.object({
      // This is to filter out templates that already belong to your own store
      ownStoreId: z.string(),
      filter: z.string().optional(),
      pageIndex: z.number().optional(),
      pageSize: z.number().int().min(10).max(50).optional(),
      tags: z.array(z.string()).optional(),
    }))
    .output(z.object({
      templates: z.array(anonymousChecklistTemplateDto),
      totalCount: z.number(),
      pageCount: z.number(),
    }))
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.ownStoreId);
      if (!perm.canGetChecklistTemplates(ctx.checkAllowed, {businessId: ctx.businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view checklist templates"
        });
      }

      const result = await ctx.db.checklistTemplate.getChecklistTemplatesAcrossAllBusinesses(ctx.businessId, {
        filter: input.filter,
        pageIndex: input.pageIndex ?? 0,
        pageSize: input.pageSize ?? 100,
        tags: input.tags,
        omitStoreId: storeId,
        includePaginationMetadata: true,
      }) as {
        items: ChecklistTemplate[];
        totalCount: number;
        pageCount: number;
      };
      const items = map(result.items, template => toAnonymousChecklistTemplateDto({
        getImageUrl: ctx.getImageUrl,
        files: []
      }, template));

      return {
        templates: items,
        totalCount: result.totalCount,
        pageCount: result.pageCount,
      };
    }),

  getAnonymousChecklistTemplate: employedStatsProcedure({feature: "common"})
    .input(z.object({
      checklistTemplateId: z.string(),
      // the user's own storeId
      storeId: z.string(),
    }))
    .output(z.object({
      template: anonymousChecklistTemplateDto,
    }))
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      if (!perm.canGetChecklistTemplates(ctx.checkAllowed, {businessId: ctx.businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view checklist templates"
        });
      }

      const template = await ctx.db.checklistTemplate.getChecklistTemplateInsecure(ctx.businessId, input.checklistTemplateId);

      return {
        template: toAnonymousChecklistTemplateDto({getImageUrl: ctx.getImageUrl, files: []}, template),
      }
    }),

  copyChecklistTemplateToStore: employedStatsProcedure({feature: "common"})
    .input(z.object({
      templateId: z.string(),
      to: z.object({
        storeId: z.string(),
      }),
    }))
    .output(z.object({
      copied: z.object({
        title: z.string(),
        fromId: z.string(),
      })
    }))
    .mutation(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.to.storeId);
      if (!perm.canCreateChecklistTemplate(ctx.checkAllowed, {businessId: ctx.businessId, storeId})) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to create checklist templates"
        });
      }

      const template = await ctx.db.checklistTemplate.getChecklistTemplateInsecure(ctx.businessId, input.templateId);
      const toStore = await ctx.db.store.getStore(storeId);
      if (!toStore) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Store not found"
        });
      }

      if (template.storeId === storeId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot copy template to the same store"
        });
      }

      const items = getItems(template);
      const origAttachments = await ctx.db.checklistTemplate.findChecklistTemplateFileAttachments(template.storeId, template.id);

      // copy all the files in S3
      const attachmentMap: { [origId: string]: FileAttachmentDto } = {};
      for (const origAttachment of origAttachments) {
        const newFileId = genFileId();
        const newS3ObjectKey = getFileAttachmentS3Key({
          businessId: ctx.businessId,
          fileId: newFileId,
          storeId: storeId,
          mediaType: origAttachment.file.mediaType as AttachmentMediaType,
          folderName: "checklist"
        });

        try {
          await ctx.s3Client.send(new CopyObjectCommand({
            Bucket: ctx.s3MediaBucket,
            CopySource: ctx.s3MediaBucket + "/" + origAttachment.file.s3ObjectKey,
            Key: newS3ObjectKey
          }));

          attachmentMap[origAttachment.id] = {
            id: genFileAttachmentId(),
            fileId: newFileId,
            expiresAt: origAttachment.expiresAt ?? undefined,
            documentType: origAttachment.documentType ?? undefined,
            title: origAttachment.title ?? undefined,
            description: origAttachment.description ?? undefined,
            filename: origAttachment.file.filename ?? undefined,
            mimeType: origAttachment.file.mimeType ?? undefined,
            mediaType: (origAttachment.file.mediaType as AttachmentMediaType),
            width: origAttachment.file.width ?? undefined,
            height: origAttachment.file.height ?? undefined,
          }
        } catch (error) {
          Sentry.captureException(error, {
            extra: {
              templateId: template.id,
              origAttachmentId: origAttachment.id,
              origFileId: origAttachment.fileId,
              newFileId: newFileId,
            }
          });
        }
      }

      // Update the item with the new attachment IDs
      for (const item of items) {
        // Update item attachments
        if (item.attachments && item.attachments.length > 0) {
          const newAttachments = [];
          for (const oldAttachmentId of item.attachments) {
            const newAttachment = attachmentMap[oldAttachmentId];
            if (newAttachment) {
              newAttachments.push(newAttachment.id);
            } else {
              newAttachments.push(oldAttachmentId); // Keep the old ID if no mapping exists
            }
          }
          item.attachments = newAttachments;
        }

        // Update instruction attachments
        if (item.instructions && item.instructions.length > 0) {
          for (const instruction of item.instructions) {
            if (instruction.attachment) {
              const newAttachment = attachmentMap[instruction.attachment]
              if (newAttachment) {
                instruction.attachment = newAttachment.id
              }
            }
          }
        }
      }

      // Create a new event for the template
      const evt = checklistTemplateCopiedEvent({
        checklistTemplateId: genChecklistTemplateId(),
        description: template.description ?? undefined,
        tags: template.tags,
        items: items,
        storeId: storeId,
        title: template.title,
        copiedFromStoreId: template.storeId,
        copiedFromTemplateId: template.id,
        attachments: values(attachmentMap),
      });

      // Append the event to the event store
      const persistedEvents = await append({
        events: [{
          ...evt,
          meta: {
            checklistTemplateId: evt.checklistTemplateId,
          },
        }],
        prisma: ctx.prisma,
        businessId: ctx.businessId,
      });

      // Apply the event to update the projections
      const projector = getChecklistTemplateProjector({
        businessId: ctx.businessId,
        database: ctx.db,
        eventStore: getEventStore({prisma: ctx.prisma, businessId: ctx.businessId}),
        evolve: checklistTemplateEvolve
      });

      await projector(persistedEvents);

      return {
        copied: {
          title: evt.title,
          fromId: evt.copiedFromTemplateId!
        }
      }
    }),
});
