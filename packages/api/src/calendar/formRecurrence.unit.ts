import {describe, expect, it, test} from 'vitest';
import {createRRule, getRecurrenceString, parseRRule} from './formRecurrence';
import {datetime, RRule} from 'rrule';
import {DateTime} from "luxon";
import {RecurrenceSchedule} from "./recurrence.types";

describe('parseRRule', () => {
  describe('Daily patterns', () => {
    it('should parse simple daily recurrence', () => {
      const result = parseRRule('RRULE:FREQ=DAILY');
      expect(result).toEqual({
        unit: 'days',
        numUnits: 1,
      });
    });

    it('should parse daily recurrence with interval', () => {
      const result = parseRRule('RRULE:FREQ=DAILY;INTERVAL=3');
      expect(result).toEqual({
        unit: 'days',
        numUnits: 3,
      });
    });
  });

  describe('Weekly patterns', () => {
    it('should parse simple weekly recurrence', () => {
      const rule = new RRule({
        freq: RRule.WEEKLY,
        interval: 1,
        byweekday: [RRule.MO],
        dtstart: datetime(2024, 2, 1, 10, 30),
      })
      const result = parseRRule(rule.toString());
      expect(result).toEqual({ // every Monday
        unit: 'weeks',
        numUnits: 1,
        weekDays: [1], // Monday=1 in DateTime, 0 in RRule
      });
    });

    it('should parse weekly recurrence with specific days', () => {
      const result = parseRRule('RRULE:FREQ=WEEKLY;BYDAY=MO,WE,FR');
      expect(result).toEqual({
        unit: 'weeks',
        numUnits: 1,
        weekDays: [1, 3, 5],
      });
    });

    it('should parse bi-weekly recurrence', () => {
      const result = parseRRule('RRULE:FREQ=WEEKLY;INTERVAL=2;BYDAY=TU,TH');
      expect(result).toEqual({
        unit: 'weeks',
        numUnits: 2,
        weekDays: [2, 4],
      });
    });
  });

  describe('Monthly patterns', () => {
    it('should parse simple monthly recurrence', () => {
      const rule = new RRule({
        freq: RRule.MONTHLY,
        interval: 1,
        dtstart: datetime(2024, 2, 1, 10, 30),
        bymonthday: [15],
      })
      const result = parseRRule(rule.toString());
      expect(result).toEqual({
        unit: 'months',
        numUnits: 1,
        monthDays: new Set([15]),
      });
    });

    it('should parse monthly recurrence on last day', () => {
      const rule = new RRule({
        freq: RRule.MONTHLY,
        interval: 1,
        dtstart: datetime(2024, 2, 1, 10, 30),
        bymonthday: [-1],
      })
      const result = parseRRule(rule.toString());
      expect(result).toEqual({
        unit: 'months',
        numUnits: 1,
        monthDays: new Set([-1])
      });
    });

    it('should parse monthly recurrence on first Monday', () => {
      const rule = new RRule({
        freq: RRule.MONTHLY,
        interval: 1,
        dtstart: datetime(2024, 2, 1, 10, 30),
        byweekday: [RRule.MO.nth(1)],
      })
      const result = parseRRule(rule.toString());
      expect(result).toEqual({
        unit: 'months',
        numUnits: 1,
        monthOn: ['1st', 1] // Monday = 0 in RRule, but 1 in our types
      });
    });

    it('should parse monthly recurrence on last Friday', () => {
      const result = parseRRule('RRULE:FREQ=MONTHLY;BYDAY=-1FR');
      expect(result).toEqual({
        unit: 'months',
        numUnits: 1,
        monthOn: ['last', 5] // Friday = 4 in RRule, but 5 in our types
      });
    });
  });

  describe('Yearly patterns', () => {
    it('should parse simple yearly recurrence', () => {
      const rule = new RRule({
        freq: RRule.YEARLY,
        interval: 1,
        dtstart: datetime(2024, 2, 1, 10, 30),
        bymonth: [3],
      })
      const result = parseRRule(rule.toString());
      expect(result).toEqual({
        unit: 'years',
        numUnits: 1,
        yearMonths: new Set([3]),
        monthDays: new Set([1]), // from the dtstart passed in
      });
    });

    it('should parse yearly recurrence in specific months', () => {
      const result = parseRRule('RRULE:FREQ=YEARLY;BYMONTH=1,6,12');
      expect(result).toMatchObject({
        unit: 'years',
        numUnits: 1,
        yearMonths: new Set([1, 6, 12]),
        monthDays: expect.any(Set), // has the current time's day of the month
      });
    });
  });

  describe('End dates', () => {
    it('should parse end date', () => {
      const result = parseRRule('RRULE:FREQ=DAILY;UNTIL=20241231T235959Z');
      const expectedEnd = new Date('2024-12-31T23:59:59Z');
      expect(result.end).toEqual(expectedEnd);
    });
  });

  describe('Edge cases', () => {
    it('should handle empty BYDAY in weekly recurrence', () => {
      const result = parseRRule('RRULE:FREQ=WEEKLY;INTERVAL=1');
      if (result.unit !== "weeks") {
        throw new Error("Expected weekly recurrence");
      }
      // rrule automatically adds the current day of week to the week days
      expect(result.weekDays).toHaveLength(1);
    });

    it('should handle invalid RRULE strings', () => {
      expect(() => parseRRule('INVALID')).toThrow();
    });

    it('should handle missing interval', () => {
      const result = parseRRule('RRULE:FREQ=MONTHLY;BYMONTHDAY=15');
      expect(result.numUnits).toBe(1);
    });
  });
});

describe('createRRule', () => {
  const dtstart = DateTime.fromObject({year: 2024, month: 1, day: 1}, {zone: "America/New_York"});

  describe('Daily patterns', () => {
    it('should create simple daily recurrence', () => {
      const schedule: RecurrenceSchedule = {
        unit: 'days',
        numUnits: 1,
      };
      const rule = createRRule({start: dtstart, schedule: schedule});
      expect(rule.options.freq).toBe(RRule.DAILY);
      expect(rule.options.interval).toBe(1);
      expect(rule.toString()).toMatchInlineSnapshot(`
        "DTSTART:20240101T050000Z
        RRULE:INTERVAL=1;FREQ=DAILY"
      `);
      expect(rule.toText()).toMatchInlineSnapshot('"every day"');
    });

    it('should create daily recurrence with interval', () => {
      const schedule: RecurrenceSchedule = {
        unit: 'days',
        numUnits: 3,
      };
      const rule = createRRule({start: dtstart, schedule: schedule});
      expect(rule.options.freq).toBe(RRule.DAILY);
      expect(rule.options.interval).toBe(3);
      expect(rule.toString()).toMatchInlineSnapshot(`
        "DTSTART:20240101T050000Z
        RRULE:INTERVAL=3;FREQ=DAILY"
      `);
      expect(rule.toText()).toMatchInlineSnapshot('"every 3 days"');
    });
  });

  describe('Weekly patterns', () => {
    it('should create simple weekly recurrence', () => {
      const schedule: RecurrenceSchedule = {
        unit: 'weeks',
        numUnits: 1,
        weekDays: [1], // Monday
      };
      const rule = createRRule({start: dtstart, schedule: schedule});
      expect(rule.options.freq).toBe(RRule.WEEKLY);
      expect(rule.options.interval).toBe(1);
      expect(rule.options.byweekday).toEqual([0]);
      expect(rule.toString()).toMatchInlineSnapshot(`
        "DTSTART:20240101T050000Z
        RRULE:INTERVAL=1;FREQ=WEEKLY;BYDAY=MO"
      `);
      expect(rule.toText()).toMatchInlineSnapshot('"every week on Monday"');
    });

    it('should create weekly recurrence with multiple days', () => {
      const schedule: RecurrenceSchedule = {
        unit: 'weeks',
        numUnits: 1,
        weekDays: [1, 3, 5], // Monday, Wednesday, Friday
      };
      const rule = createRRule({start: dtstart, schedule: schedule});
      expect(rule.options.freq).toBe(RRule.WEEKLY);
      expect(rule.options.byweekday).toEqual([0, 2, 4]);
      expect(rule.toString()).toMatchInlineSnapshot(`
        "DTSTART:20240101T050000Z
        RRULE:INTERVAL=1;FREQ=WEEKLY;BYDAY=MO,WE,FR"
      `);
      expect(rule.toText()).toMatchInlineSnapshot(`"every week on Monday, Wednesday, Friday"`);
    });

    it('should create bi-weekly recurrence', () => {
      const schedule: RecurrenceSchedule = {
        unit: 'weeks',
        numUnits: 2,
        weekDays: [2, 4], // Tuesday, Thursday
      };
      const rule = createRRule({start: dtstart, schedule: schedule});
      expect(rule.options.freq).toBe(RRule.WEEKLY);
      expect(rule.options.interval).toBe(2);
      expect(rule.options.byweekday).toEqual([1, 3]);
      expect(rule.toString()).toMatchInlineSnapshot(`
        "DTSTART:20240101T050000Z
        RRULE:INTERVAL=2;FREQ=WEEKLY;BYDAY=TU,TH"
      `);
      expect(rule.toText()).toMatchInlineSnapshot(`"every 2 weeks on Tuesday, Thursday"`);
    });
  });

  describe('Monthly patterns', () => {
    it('should create simple monthly recurrence', () => {
      const schedule: RecurrenceSchedule = {
        unit: 'months',
        numUnits: 1,
        monthDays: new Set([15]),
      };
      const rule = createRRule({start: dtstart, schedule: schedule});
      expect(rule.options.freq).toBe(RRule.MONTHLY);
      expect(rule.options.bymonthday).toEqual([15]);
      expect(rule.toString()).toMatchInlineSnapshot(`
        "DTSTART:20240101T050000Z
        RRULE:INTERVAL=1;FREQ=MONTHLY;BYMONTHDAY=15"
      `);
      expect(rule.toText()).toMatchInlineSnapshot('"every month on the 15th"');
    });

    it('should create monthly recurrence on last day', () => {
      const schedule: RecurrenceSchedule = {
        unit: 'months',
        numUnits: 1,
        monthDays: new Set([-1]),
      };
      const rule = createRRule({start: dtstart, schedule: schedule});
      expect(rule.options.freq).toBe(RRule.MONTHLY);
      expect(rule.options.bynmonthday).toEqual([-1]);
      expect(rule.toString()).toMatchInlineSnapshot(`
        "DTSTART:20240101T050000Z
        RRULE:INTERVAL=1;FREQ=MONTHLY;BYMONTHDAY=-1"
      `);
      expect(rule.toText()).toMatchInlineSnapshot('"every month on the last"');
    });

    it('should create monthly recurrence on first Monday', () => {
      const schedule: RecurrenceSchedule = {
        unit: 'months',
        numUnits: 1,
        monthOn: ['1st', 1],
      };
      const rule = createRRule({start: dtstart, schedule: schedule});
      expect(rule.options.freq).toBe(RRule.MONTHLY);
      expect(rule.options.bynweekday).toEqual([[0, 1]]);
      expect(rule.toString()).toMatchInlineSnapshot(`
        "DTSTART:20240101T050000Z
        RRULE:INTERVAL=1;FREQ=MONTHLY;BYDAY=+1MO"
      `);
      expect(rule.toText()).toMatchInlineSnapshot('"every month on the 1st Monday"');
    });

    it('should create monthly recurrence on last Friday', () => {
      const schedule: RecurrenceSchedule = {
        unit: 'months',
        numUnits: 1,
        monthOn: ['last', 5],
      };
      const rule = createRRule({start: dtstart, schedule: schedule});
      expect(rule.options.freq).toBe(RRule.MONTHLY);
      expect(rule.options.bynweekday).toEqual([[4, -1]]);
      expect(rule.toString()).toMatchInlineSnapshot(`
        "DTSTART:20240101T050000Z
        RRULE:INTERVAL=1;FREQ=MONTHLY;BYDAY=-1FR"
      `);
      expect(rule.toText()).toMatchInlineSnapshot('"every month on the last Friday"');
    });
  });

  describe('Yearly patterns', () => {
    it('should create simple yearly recurrence', () => {
      const schedule: RecurrenceSchedule = {
        unit: 'years',
        numUnits: 1,
        yearMonths: new Set([3]),
        monthDays: new Set([3]),
      };
      const rule = createRRule({start: dtstart, schedule: schedule});
      expect(rule.options.freq).toBe(RRule.YEARLY);
      expect(rule.options.bymonth).toEqual([3]);
      expect(rule.toString()).toMatchInlineSnapshot(`
        "DTSTART:20240101T050000Z
        RRULE:INTERVAL=1;FREQ=YEARLY;BYMONTH=3;BYMONTHDAY=3"
      `);
      expect(rule.toText()).toMatchInlineSnapshot(`"every March on the 3rd"`);
    });

    it('should create yearly recurrence in specific months', () => {
      const schedule: RecurrenceSchedule = {
        unit: 'years',
        numUnits: 1,
        yearMonths: new Set([1, 6, 12]),
        monthDays: new Set([3]),
      };
      const rule = createRRule({start: dtstart, schedule: schedule});
      expect(rule.options.freq).toBe(RRule.YEARLY);
      expect(rule.options.bymonth).toEqual([1, 6, 12]);
      expect(rule.toString()).toMatchInlineSnapshot(`
        "DTSTART:20240101T050000Z
        RRULE:INTERVAL=1;FREQ=YEARLY;BYMONTH=1,6,12;BYMONTHDAY=3"
      `);
      expect(rule.toText()).toMatchInlineSnapshot(`"every January, June and December on the 3rd"`);
    });
  });

  describe('End dates', () => {
    it('should include end date', () => {
      const endDate = new Date('2024-12-31T23:59:59Z');
      const schedule: RecurrenceSchedule = {
        unit: 'days',
        numUnits: 1,
        end: endDate,
      };
      const rule = createRRule({start: dtstart, schedule: schedule});
      expect(rule.options.until).toEqual(endDate);
      expect(rule.toString()).toMatchInlineSnapshot(`
        "DTSTART:20240101T050000Z
        RRULE:INTERVAL=1;UNTIL=20241231T235959Z;FREQ=DAILY"
      `);
      expect(rule.toText()).toMatchInlineSnapshot('"every day until December 31, 2024"');
    });
  });

  describe('Round-trip conversion', () => {
    it('should maintain schedule after parse-create cycle', () => {
      const original = 'RRULE:FREQ=MONTHLY;INTERVAL=2;BYDAY=1MO';
      const parsed = parseRRule(original);
      const created = createRRule({start: dtstart, schedule: parsed});
      expect(created.toString()).toMatchInlineSnapshot(`
        "DTSTART:20240101T050000Z
        RRULE:INTERVAL=2;FREQ=MONTHLY;BYDAY=+1MO"
      `);
      expect(created.toText()).toMatchInlineSnapshot('"every 2 months on the 1st Monday"');

      const parsed2 = parseRRule(created.toString());
      expect(parsed2).toMatchInlineSnapshot(`
        {
          "end": undefined,
          "monthDays": undefined,
          "monthOn": [
            "1st",
            1,
          ],
          "numUnits": 2,
          "unit": "months",
        }
      `);

      const created2 = createRRule({start: dtstart, schedule: parsed2});
      expect(created2.toString()).toEqual(created.toString());
    });
  });
});

describe("getRecurrenceString", () => {
  const baseDateTime = DateTime.fromISO("2023-11-27T10:30:00");

  test("daily recurrence", () => {
    const schedule: RecurrenceSchedule = {
      unit: "days",
      numUnits: 1,
    };
    expect(getRecurrenceString(schedule, baseDateTime)).toBe("Every day at 10:30AM");

    const schedule2: RecurrenceSchedule = {
      unit: "days",
      numUnits: 2,
    };
    expect(getRecurrenceString(schedule2, baseDateTime)).toBe("Every 2 days at 10:30AM");
  });

  test("weekly recurrence", () => {
    const schedule: RecurrenceSchedule = {
      unit: "weeks",
      numUnits: 1,
      weekDays: [3, 4], // Wed, Thu
    };
    expect(getRecurrenceString(schedule, baseDateTime)).toBe("Every week on Wed & Thu at 10:30AM");

    const schedule2: RecurrenceSchedule = {
      unit: "weeks",
      numUnits: 2,
      weekDays: [3], // Wed
    };
    expect(getRecurrenceString(schedule2, baseDateTime)).toBe("Every 2 weeks on Wednesday at 10:30AM");
  });

  test("monthly recurrence with monthOn", () => {
    const schedule: RecurrenceSchedule = {
      unit: "months",
      numUnits: 1,
      monthOn: ["1st", 1], // 1st Monday
    };
    expect(getRecurrenceString(schedule, baseDateTime)).toBe("The 1st Monday every month at 10:30AM");

    const schedule2: RecurrenceSchedule = {
      unit: "months",
      numUnits: 2,
      monthOn: ["1st", 1], // 1st Monday
    };
    expect(getRecurrenceString(schedule2, baseDateTime)).toBe("The 1st Monday every 2 months at 10:30AM");
  });

  test("monthly recurrence with monthDays", () => {
    const schedule: RecurrenceSchedule = {
      unit: "months",
      numUnits: 2,
      monthDays: new Set([30, -1]), // 30th and last day
    };
    expect(getRecurrenceString(schedule, baseDateTime)).toBe("The last day & 30th every 2 months at 10:30AM");
  });

  test("yearly recurrence", () => {
    const schedule: RecurrenceSchedule = {
      unit: "years",
      numUnits: 1,
      yearMonths: new Set([11]),
      monthDays: new Set([29])
    };
    expect(getRecurrenceString(schedule, baseDateTime)).toMatchInlineSnapshot(`"Every year on the 29th of Nov at 10:30AM"`);

    // the yearMonths and monthDays override the start date passed in
    const julyDateTime = DateTime.fromISO("2023-07-27T10:30:00");
    expect(getRecurrenceString(schedule, julyDateTime)).toMatchInlineSnapshot(`"Every year on the 29th of Nov at 10:30AM"`);

    // Test with specific months
    const scheduleWithMonths: RecurrenceSchedule = {
      unit: "years",
      numUnits: 1,
      yearMonths: new Set([7]), // July
      monthDays: new Set([27])
    };
    expect(getRecurrenceString(scheduleWithMonths, julyDateTime)).toBe("Every year on the 27th of Jul at 10:30AM");

    const scheduleWithMultipleMonths: RecurrenceSchedule = {
      unit: "years",
      numUnits: 1,
      yearMonths: new Set([7, 10]), // July & October
      monthDays: new Set([28])
    };
    expect(getRecurrenceString(scheduleWithMultipleMonths, julyDateTime)).toBe("Every year on the 28th of Jul & Oct at 10:30AM");
  });

  test("additional edge cases", () => {
    // Test week without specified days
    const scheduleWeekNoDays: RecurrenceSchedule = {
      unit: "weeks",
      numUnits: 1,
      weekDays: [],
    };
    expect(getRecurrenceString(scheduleWeekNoDays, baseDateTime)).toBe("Every week on Monday at 10:30AM");

    // Test month without monthOn or monthDays
    const scheduleMonthSimple: RecurrenceSchedule = {
      unit: "months",
      numUnits: 1,
      monthDays: new Set(),
    };
    expect(getRecurrenceString(scheduleMonthSimple, baseDateTime)).toBe("Every month on the 27th at 10:30AM");

    // Test with three weekdays
    const scheduleThreeDays: RecurrenceSchedule = {
      unit: "weeks",
      numUnits: 1,
      weekDays: [1, 2, 3], // Mon, Tue, Wed
    };
    expect(getRecurrenceString(scheduleThreeDays, baseDateTime)).toBe("Every week on Mon, Tue & Wed at 10:30AM");
  });
});

describe("server to form to server preservation", () => {
  const dtstart = DateTime.fromObject({year: 2024, month: 1, day: 1}, {zone: "America/New_York"});

  it("should result in the same rrule string after parsing and re-creating, if no changes were made by user", () => {
    const schedule: RecurrenceSchedule = {
      unit: 'weeks',
      numUnits: 1,
      weekDays: [1],
    };
    const start = DateTime.fromObject({year: 2024, month: 12, day: 2}, {zone: "America/New_York"})
    const rule = createRRule({start: start, schedule: schedule});
    const rruleString = rule.toString();
    expect(rruleString).toMatchInlineSnapshot(`
      "DTSTART:20241202T050000Z
      RRULE:INTERVAL=1;FREQ=WEEKLY;BYDAY=MO"
    `);
    const parsed = parseRRule(rruleString);
    const reCreatedRule = createRRule({start: start, schedule: parsed});
    expect(reCreatedRule.toString()).toEqual(rruleString);
  });

  it("should result in the same rrule string after parsing and re-creating (no start time)", () => {
    const schedule: RecurrenceSchedule = {
      unit: 'weeks',
      numUnits: 1,
      weekDays: [1],
    };
    const rule = createRRule({schedule: schedule});
    const rruleString = rule.toString();
    expect(rruleString).toMatchInlineSnapshot(`
      "RRULE:INTERVAL=1;FREQ=WEEKLY;BYDAY=MO"
    `);
    const parsed = parseRRule(rruleString);
    const reCreatedRule = createRRule({schedule: parsed});
    expect(reCreatedRule.toString()).toEqual(rruleString);
  });

  // Note: decided to not support this, because rrule adds the weekdays even for implicit weekly recurrences, so a misalignment happens.
  // it("should result in the same rrule string after parsing and re-creating ('floating' interval)", () => {
  //   const schedule: RecurrenceSchedule = {
  //     unit: 'weeks',
  //     numUnits: 1,
  //   };
  //
  //   const rule = createRRule({schedule: schedule});
  //   const rruleString = rule.toString();
  //   expect(rruleString).toMatchInlineSnapshot(`
  //     "RRULE:INTERVAL=1;FREQ=WEEKLY"
  //   `);
  //   const parsed = parseRRule(rruleString);
  //   const reCreatedRule = createRRule({schedule: parsed});
  //   expect(reCreatedRule.toString()).toEqual(rruleString);
  // });

  it("should preserve monthly recurrence with specific days", () => {
    const rruleString = "RRULE:INTERVAL=1;FREQ=MONTHLY;BYMONTHDAY=6";
    const parsed = parseRRule(rruleString);
    expect(parsed.unit).toBe("months");
    if (!("monthDays" in parsed)) {
      throw new Error("Expected monthDays to be set");
    }
    expect(parsed.monthDays).toContain(6);
    const reCreatedRule = createRRule({schedule: parsed});
    expect(reCreatedRule.toString()).toEqual(rruleString);
  });

  it("should preserve monthly recurrence with specific days 2", () => {
    const schedule: RecurrenceSchedule = {
      unit: 'months',
      numUnits: 2,
      monthDays: new Set([15]),
    };
    const rule = createRRule({start: dtstart, schedule: schedule});
    const rruleString = rule.toString();
    const parsed = parseRRule(rruleString);
    expect(parsed.unit).toBe("months");
    if (!("monthDays" in parsed)) {
      throw new Error("Expected monthDays to be set");
    }
    expect(parsed.monthDays).toContain(15);
    const reCreatedRule = createRRule({start: dtstart, schedule: parsed});
    expect(reCreatedRule.toString()).toEqual(rruleString);
  });

  it("should preserve monthly recurrence with nth weekday", () => {
    const schedule: RecurrenceSchedule = {
      unit: 'months',
      numUnits: 1,
      monthOn: ['2nd', 1], // second Tuesday
    };
    const rule = createRRule({start: dtstart, schedule: schedule});
    const rruleString = rule.toString();
    const parsed = parseRRule(rruleString);
    const reCreatedRule = createRRule({start: dtstart, schedule: parsed});
    expect(reCreatedRule.toString()).toEqual(rruleString);
  });

  it("should preserve yearly recurrence with multiple months", () => {
    const schedule: RecurrenceSchedule = {
      unit: 'years',
      numUnits: 1,
      yearMonths: new Set([3, 6, 9, 12]),
      monthDays: new Set([3]),
    };
    const rule = createRRule({start: dtstart, schedule: schedule});
    const rruleString = rule.toString();
    const parsed = parseRRule(rruleString);
    const reCreatedRule = createRRule({start: dtstart, schedule: parsed});
    expect(reCreatedRule.toString()).toEqual(rruleString);
  });

  it("should preserve weekly recurrence with multiple days", () => {
    const schedule: RecurrenceSchedule = {
      unit: 'weeks',
      numUnits: 2,
      weekDays: [1, 3, 5], // Tuesday, Thursday, Saturday
    };
    const rule = createRRule({start: dtstart, schedule: schedule});
    const rruleString = rule.toString();
    const parsed = parseRRule(rruleString);
    const reCreatedRule = createRRule({start: dtstart, schedule: parsed});
    expect(reCreatedRule.toString()).toEqual(rruleString);
  });

  it("should preserve end date in recurrence", () => {
    const schedule: RecurrenceSchedule = {
      unit: 'days',
      numUnits: 1,
      end: new Date('2024-12-31T23:59:59Z'),
    };
    const rule = createRRule({start: dtstart, schedule: schedule});
    const rruleString = rule.toString();
    const parsed = parseRRule(rruleString);
    const reCreatedRule = createRRule({start: dtstart, schedule: parsed});
    expect(reCreatedRule.toString()).toEqual(rruleString);
  });
});
