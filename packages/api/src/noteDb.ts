import {EnforceSecureId, SecureBusinessId, SecurePersonId, SecureStoreId} from "./database.types";
import {PrismaClient} from "@prisma/client";
import {correctiveActionIncludes, personWithJobAndImageIncludes} from "./schema.converters";
import {scheduleNoteMeta} from "./note.schemas";
import {getIsoWeeksOverlappingRange} from "./date.util";
import {DateTimeRange} from "./timeSchemas";
import {EntityNoteWithHistoryAndIncludes} from "./entityNote";

export const noteDb = (prisma: PrismaClient) => {
  const dbImpl = {

    getShiftLeadNotesForSchedule(businessId: SecureBusinessId, storeId: SecureStoreId, {scheduleId, dayOfWeek}: {
      scheduleId: string;
      dayOfWeek: number;
    }) {
      return prisma.entityNote.findMany({
        where: {
          businessId,
          schedule: {
            id: scheduleId,
            businessId,
            storeId: storeId,
          },
          scheduleWeekDay: dayOfWeek,
          noteType: "shiftLeadNotes"
        },
        orderBy: {
          createdAt: "asc"
        },
        include: {
          createdByPerson: personWithJobAndImageIncludes
        }
      });
    },

    async findShiftLeadAndScheduleNotes(businessId: SecureBusinessId, {
      storeId, range,
      cursor, take, isAsc, timezone,
      canViewScheduleNotes, canViewShiftLeadNotes
    }: {
      storeId: SecureStoreId;
      range: DateTimeRange;
      take: number;
      cursor: Date | undefined | null;
      canViewShiftLeadNotes: boolean;
      canViewScheduleNotes: boolean;
      isAsc: boolean | undefined;
      timezone: string;
    }) {
      if (!canViewShiftLeadNotes && !canViewScheduleNotes) {
        return {
          items: [],
          nextCursor: undefined
        };
      }

      const isoWeeks = getIsoWeeksOverlappingRange(range, timezone, 1000);

      const orConditions = [
        ...(canViewShiftLeadNotes ? [{noteType: "shiftLeadNotes"}] : []),
        ...(canViewScheduleNotes ? [{noteType: "scheduleComments"}] : [])
      ]

      const items = await prisma.entityNote.findMany({
        where: {
          businessId,
          schedule: {
            businessId,
            storeId: storeId,
            OR: isoWeeks
          },
          OR: orConditions,
        },
        skip: cursor ? 1 : 0, // first query doesn't need to skip previous cursor
        take: take + 1,
        cursor: cursor ? {createdAt: cursor} : undefined,
        orderBy: {
          createdAt: isAsc ? "asc" : "desc"
        },
        include: {
          createdByPerson: personWithJobAndImageIncludes,
          schedule: {
            select: {
              year: true,
              week: true,
            }
          }
        }
      });

      return {
        items,
        nextCursor: items[take]?.createdAt
      }
    },

    createScheduleNote(businessId: SecureBusinessId, storeId: SecureStoreId, {
      scheduleId,
      noteId,
      note,
      createdByPersonId,
      dayOfWeek,
    } : {
      scheduleId: string;
      noteId: string;
      note: string;
      createdByPersonId: SecurePersonId;
      dayOfWeek: number;
    }) {
      return prisma.entityNote.create({
        data: {
          id: noteId,
          businessId,
          storeId,
          scheduleId,
          noteType: "scheduleComments",
          note,
          createdByPersonId,
          metadata: scheduleNoteMeta.parse({dayOfWeek: dayOfWeek}),
          scheduleWeekDay: dayOfWeek,
        }
      });
    },

    updateScheduleNote(businessId: SecureBusinessId, storeId: SecureStoreId, {
      scheduleId,
      noteId,
      note,
      updatedById,
    } : {
      scheduleId: string;
      noteId: string;
      note: string;
      updatedById: SecurePersonId;
    }) {
      return prisma.entityNote.update({
        where: {
          id: noteId,
          businessId,
          storeId,
          scheduleId,
        },
        data: {
          noteType: "scheduleComments",
          note,
          updatedById: updatedById,
        }
      });
    },

    deleteScheduleNote(businessId: SecureBusinessId, storeId: SecureStoreId, {
      scheduleId,
      noteId,
    } : {
      scheduleId: string;
      noteId: string;
    }) {
      return prisma.entityNote.delete({
        where: {
          id: noteId,
          businessId,
          storeId,
          scheduleId,
        },
      });
    },

    getScheduleNotesForSchedule(businessId: SecureBusinessId, storeId: SecureStoreId, {scheduleId}: {
      scheduleId: string;
    }) {
      return prisma.entityNote.findMany({
        where: {
          businessId,
          schedule: {
            id: scheduleId,
            businessId,
            storeId: storeId
          },
          noteType: {
            in: ["scheduleComments", "shiftLeadNotes"] // for old db records that still have shift lead notes
          }
        },
        orderBy: {
          createdAt: "asc"
        },
        include: {
          createdByPerson: personWithJobAndImageIncludes
        }
      });
    },

    createShiftLeadNote(businessId: SecureBusinessId, storeId: SecureStoreId, {
      noteId,
      scheduleId,
      dayOfWeek,
      createdByPersonId,
      note
    }: {
      noteId: string;
      scheduleId: string;
      dayOfWeek: number;
      note: string;
      createdByPersonId: SecurePersonId | undefined;
    }) {
      return prisma.entityNote.create({
        data: {
          id: noteId,
          businessId,
          storeId: storeId,
          scheduleId: scheduleId,
          noteType: "shiftLeadNotes",
          note: note,
          createdByPersonId: createdByPersonId,
          metadata: scheduleNoteMeta.parse({dayOfWeek: dayOfWeek}),
          scheduleWeekDay: dayOfWeek,
        }
      });
    },

    findNoteById(businessId: SecureBusinessId, storeId: SecureStoreId, {
      noteId
    }: {
      noteId: string;
    }): Promise<EntityNoteWithHistoryAndIncludes | null> {
      return prisma.entityNote.findFirst({
        where: {
          id: noteId,
          businessId: businessId,
          storeId: storeId,
        },
        include: {
          images: true,
          history: {
            include: {
              createdByPerson: personWithJobAndImageIncludes,
              person: personWithJobAndImageIncludes,
              images: true,
              fromCorrectiveAction: {
                include: {
                  ...correctiveActionIncludes,
                }
              },
            }
          },
        }
      });
    },

    /**
     * Get person notes - coaching moments, positive feedback, and general notes
     * Returns all person notes for the given business/store with optional date filtering
     */
    async getPersonNotes(businessId: SecureBusinessId, storeId: SecureStoreId, params: {
      startDate?: Date;
      endDate?: Date;
    }) {
      // Build date filter conditions
      const dateFilter: any = {};
      if (params.startDate || params.endDate) {
        dateFilter.createdAt = {};
        if (params.startDate) {
          dateFilter.createdAt.gte = params.startDate;
        }
        if (params.endDate) {
          dateFilter.createdAt.lte = params.endDate;
        }
      }

      return await prisma.entityNote.findMany({
        where: {
          businessId,
          storeId,
          personId: { not: null }, // Only person notes
          noteType: { in: ['coaching', 'positive-feedback', 'general'] }, // Only these note types
          isArchived: false, // Only active notes
          ...dateFilter,
        },
        select: {
          id: true,
          personId: true,
          createdByPersonId: true,
          noteType: true,
          note: true,
          createdAt: true,
          updatedAt: true,
          requiresAcknowledgement: true,
          isAcknowledged: true,
          acknowledgedAt: true,
          isArchived: true, // Include for status calculation
          sensitivityLevel: true,
          policiesInAction: true, // Include policies for display
          person: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              metadata: true, // Scalar field - only available via select
              profileImage: true,
              employments: {
                include: {
                  stores: true,
                },
              },
            },
          },
          createdByPerson: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 10000, // Sanity limit to prevent runaway queries
      });
    },
  }

  return dbImpl as EnforceSecureId<typeof dbImpl>;
}
