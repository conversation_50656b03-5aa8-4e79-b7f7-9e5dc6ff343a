import {type Prisma as P} from "@prisma/client";
import {Context} from "./types";
import {NotificationSettings} from "./schemas";
import {TimeFrameId} from "./setupSheets/setupSheetDay/setupSheetDaySchemas";
import {TimeOfDay} from "./timeSchemas";

export const NotificationCode = {
  uncategorized: 'NC_uncategorized',
  announcement: 'NC_announcement',
  business: {
    invitation: 'NC_businessInvitation'
  },
  schedule: {
    changed: 'NC_scheduleChanged'
  },
  shift: {
    assigned: 'NC_shiftAssigned',
    unassigned: 'NC_shiftUnassigned',
    rangeChange: 'NC_shiftRangeChange',
    reminder: 'NC_shiftReminder'
  },
  checklist: {
    assigned: 'NC_checkListAssigned',
    overdue: 'NC_checkListOverdue'
  },
  checklist2: {
    assigned: 'NC_checkList2Assigned',
    overdue: 'NC_checkList2Overdue',
    completed: 'NC_checkList2Completed',
    completedNonCompliant: 'NC_checkList2CompletedNonCompliant',
    upcomingDue: 'NC_checkList2UpcomingDue'
  },
  setupSheet: {
    assigned: 'NC_setupSheetAssigned',
    breakStarted: 'NC_setupSheetBreakStarted',
    breakSoon: 'NC_setupSheetBreakSoon',
    shiftReassigned: 'NC_shiftReassigned'
  },
  teamMember: {
    pendingApprovals: 'NC_pendingTeamMemberApprovals',
    onboardingReminder: 'NC_completeOnboardingReminder'
  },
  correctiveAction: {
    read: 'NC_correctiveActionRead',  // intended for the recipient of the corrective action
    review: 'NC_correctiveActionReview',  // intended for HR person
    acknowledged: 'NC_correctiveActionAcknowledged',
  },
  // actionItem: {
  //   created: 'NC_actionableItemCreated',
  // },
  availability: {
    approved: 'NC_availabilityApproved',
    declined: 'NC_availabilityDeclined'
  },
  timeOff: {
    approved: 'NC_timeOffApproved',
    declined: 'NC_timeOffDeclined'
  },
  shiftOffer: {
    available: 'NC_shiftOfferAvailable',
    approved: 'NC_shiftOfferApproved',
    accepted: 'NC_shiftOfferAccepted',
    acceptanceApproved: 'NC_shiftOfferAcceptanceApproved',
    declined: 'NC_shiftOfferDeclined',
    cancelled: 'NC_shiftOfferCancelled'
  },
  shiftSwap: {
    available: 'NC_shiftSwapAvailable',
    acceptedOfferor: 'NC_shiftSwapAcceptedOfferor',
    rejectedOfferor: 'NC_shiftSwapRejectedOfferor',
    approvedOfferor: 'NC_shiftSwapApprovedOfferor',
    approvedOfferee: 'NC_shiftSwapApprovedOfferee',
    declinedOfferor: 'NC_shiftSwapDeclinedOfferor',
    declinedOfferee: 'NC_shiftSwapDeclinedOfferee',
    cancelledOfferor: 'NC_shiftSwapCancelledOfferor',
    cancelledOfferee: 'NC_shiftSwapCancelledOfferee'
  },
  positionScore: {
    updated: 'NC_positionScoreUpdated'
  },
  note: {
    created: 'NC_noteCreated'
  }
} as const;

// No entity IDS, not even a store ID (e.g.: summary emails w/ multiple stores)
type EmptyNotificationIds = {
  storeId?: string;
};

// Generic, we should always include the storeId, regardless
type StoreNotificationIds = {
};

type ScheduleNotificationIds = {
  // TODO: ADD ISO WEEK
  storeId: string;
};

type SetupSheetAssignmentNotificationIds = {
  storeId: string;
  setupDayId: string;
  setupPositionTitle: string | undefined;
  storePositionId: string | undefined;
  timeFrameId: TimeFrameId | undefined;
  start: TimeOfDay | undefined;
  startDt: Date | undefined;
};

type SetupSheetBreakNotificationIds = {
  storeId: string;
  setupDayId: string;
  timeFrameId: TimeFrameId | undefined;
  start: TimeOfDay | undefined;
  startDt: Date | undefined;
};

type ShiftNotificationIds = {
  shiftId: string;
  storeId: string;
  isoDate: string;
};

type ShiftOfferNotificationIds = {
  storeId: string;
  shiftOfferId: string;
};

type ShiftSwapNotificationIds = {
  storeId: string;
  personId: string;
  shiftSwapId: string;
};

type TimeOffNotificationIds = {
  storeId: string;
  personId: string;
  personTimeOffId: string;
};

type AvailabilityNotificationIds = {
  storeId: string;
  availabilityId: string;
};

type ChecklistNotificationIds = {
  storeId: string;
  checklistEventId: string;
};

type Checklist2NotificationIds = {
  storeId: string;
  checklistId: string;
  recurrenceId: Date | undefined;
};

type BusinessNotificationIds = {
  businessId: string;
};

type CorrectiveActionNotificationIds = {
  storeId: string;
  recipientPersonId: string;
  correctiveActionId: string;
};

type NoteNotificationIds = {
  storeId?: string;
  personId: string;
  entityNoteId: string;
};

type PositionScoreNotificationIds = {
  storeId?: string;
  personId: string;
  positionId: string;
};

type AnnouncementNotificationIds = {};

// Map notification codes to their required entity IDs
export type NotificationEntityMap = {
  [NotificationCode.announcement]: AnnouncementNotificationIds;

  [NotificationCode.schedule.changed]: ScheduleNotificationIds;

  [NotificationCode.shift.assigned]: ShiftNotificationIds;
  [NotificationCode.shift.unassigned]: ShiftNotificationIds;
  [NotificationCode.shift.rangeChange]: ShiftNotificationIds;
  [NotificationCode.shift.reminder]: ShiftNotificationIds;

  [NotificationCode.shiftOffer.available]: ShiftOfferNotificationIds;
  [NotificationCode.shiftOffer.approved]: ShiftOfferNotificationIds;
  [NotificationCode.shiftOffer.accepted]: ShiftOfferNotificationIds;
  [NotificationCode.shiftOffer.acceptanceApproved]: ShiftOfferNotificationIds;
  [NotificationCode.shiftOffer.declined]: ShiftOfferNotificationIds;
  [NotificationCode.shiftOffer.cancelled]: ShiftOfferNotificationIds;

  [NotificationCode.shiftSwap.available]: ShiftSwapNotificationIds;
  [NotificationCode.shiftSwap.acceptedOfferor]: ShiftSwapNotificationIds;
  [NotificationCode.shiftSwap.rejectedOfferor]: ShiftSwapNotificationIds;
  [NotificationCode.shiftSwap.approvedOfferor]: ShiftSwapNotificationIds;
  [NotificationCode.shiftSwap.approvedOfferee]: ShiftSwapNotificationIds;
  [NotificationCode.shiftSwap.declinedOfferor]: ShiftSwapNotificationIds;
  [NotificationCode.shiftSwap.declinedOfferee]: ShiftSwapNotificationIds;
  [NotificationCode.shiftSwap.cancelledOfferor]: ShiftSwapNotificationIds;
  [NotificationCode.shiftSwap.cancelledOfferee]: ShiftSwapNotificationIds;

  [NotificationCode.timeOff.approved]: TimeOffNotificationIds;
  [NotificationCode.timeOff.declined]: TimeOffNotificationIds;

  [NotificationCode.availability.approved]: AvailabilityNotificationIds;
  [NotificationCode.availability.declined]: AvailabilityNotificationIds;

  [NotificationCode.checklist.assigned]: ChecklistNotificationIds;
  [NotificationCode.checklist.overdue]: ChecklistNotificationIds;

  [NotificationCode.checklist2.assigned]: Checklist2NotificationIds;
  [NotificationCode.checklist2.overdue]: Checklist2NotificationIds;
  [NotificationCode.checklist2.completed]: Checklist2NotificationIds;
  [NotificationCode.checklist2.completedNonCompliant]: Checklist2NotificationIds;
  [NotificationCode.checklist2.upcomingDue]: Checklist2NotificationIds;

  [NotificationCode.setupSheet.assigned]: SetupSheetAssignmentNotificationIds;
  [NotificationCode.setupSheet.breakStarted]: SetupSheetBreakNotificationIds;
  [NotificationCode.setupSheet.breakSoon]: SetupSheetBreakNotificationIds;
  [NotificationCode.setupSheet.shiftReassigned]: ShiftNotificationIds;

  [NotificationCode.business.invitation]: BusinessNotificationIds;

  // [NotificationCode.actionItemactionItem.created]: StoreNotificationIds;

  [NotificationCode.correctiveAction.read]: CorrectiveActionNotificationIds;
  [NotificationCode.correctiveAction.review]: CorrectiveActionNotificationIds;
  [NotificationCode.correctiveAction.acknowledged]: CorrectiveActionNotificationIds;

  // Default/uncategorized (no required IDs)
  [NotificationCode.uncategorized]: StoreNotificationIds;
  [NotificationCode.teamMember.pendingApprovals]: EmptyNotificationIds;
  [NotificationCode.teamMember.onboardingReminder]: StoreNotificationIds;

  [NotificationCode.positionScore.updated]: PositionScoreNotificationIds;

  [NotificationCode.note.created]: NoteNotificationIds;
};

// Helper type to get required entity IDs for a given notification code
export type RequiredEntityIds<T extends keyof NotificationEntityMap> = NotificationEntityMap[T];

// Modified notifyPerson parameters type
export type NotifyPersonInternalParams<T extends keyof NotificationEntityMap> = {
  ctx: Pick<Context, 'courier' | 'sendEmail' | 'prisma' | 'pushNotificationService' | 'telnyx' | 'telnyxFromNumber'>;
  person: PersonToNotify;
  message: string;
  messageHtml?: string;
  subject: string;
  transports: Set<NotificationTransport>;
  fallbackTransport?: Set<NotificationTransport>;
  onError: (error: string) => void;
  deduplicationId: string | null;
  code: T;
  entityIds: RequiredEntityIds<T>;
};

type NotificationPreferences = {
  pushColumn?: keyof PersonToNotify['notificationSettings'][0];
  smsColumn?: keyof PersonToNotify['notificationSettings'][0];
  includeEmail?: boolean;
};

// Modified notifyPersonInternal in which 'transports' is required along with a few optional parameters
export type NotifyPersonParams<T extends keyof NotificationEntityMap> =
  Omit<NotifyPersonInternalParams<T>, 'transports' | 'onError'> & {
  onError?: (error: string) => void;
  overrideTransports?: Set<NotificationTransport>;
  preferences?: NotificationPreferences;
};

export type NotificationTransport = "email" | "push" | "sms";

export type PersonToNotify = {
  id: string,
  phoneNumber?: string | null;
  email?: string | null,
  user: {
    devicePushTokens: {
      deviceToken: string
    }[],
  } | null,
  notificationSettings: NotificationSettings[],
}

export type NotificationCreateData = P.NotificationSendUncheckedCreateInput & {
  id: string;
  messageText?: string;
  personId: string;
  startedAt: Date;
  transports: string[];
  isRead: boolean;
  isArchived: boolean;
  code: string;
  isoDate?: string;
  storeId?: string;
  checklistEventId?: string;
  correctiveActionId?: string;
  personTimeOffId?: string;
  shiftId?: string;
  shiftOfferId?: string;
  shiftSwapId?: string;
  availabilityId?: string;
  recipientPersonId?: string;
  entityNoteId?: string;
};

type FlattenNotificationCode<T> = T extends string
  ? T
  : T extends object
    ? { [K in keyof T]: FlattenNotificationCode<T[K]> }[keyof T]
    : never;

export type NotificationTitleMap = {
  [K in FlattenNotificationCode<typeof NotificationCode>]: string;
};

export const notificationTitles: NotificationTitleMap = {
  NC_uncategorized: "Notification",
  NC_announcement: "Announcement",
  NC_businessInvitation: "Business Invitation",
  NC_scheduleChanged: "Schedule Updated",
  NC_shiftAssigned: "Shift Assigned",
  NC_shiftUnassigned: "Shift Unassigned",
  NC_shiftRangeChange: "Shift Time Changed",
  NC_shiftReminder: "Upcoming Shift",
  NC_checkList2Assigned: "New Checklist",
  NC_checkListAssigned: "New Checklist",
  NC_checkList2Overdue: "Checklist Overdue",
  NC_checkListOverdue: "Checklist Overdue",
  NC_checkList2Completed: "Checklist Completed",
  NC_checkList2CompletedNonCompliant: "Checklist Completed with Non-compliant Items",
  NC_checkList2UpcomingDue: "Checklist Due Soon",
  NC_setupSheetAssigned: "Position Assigned",
  NC_setupSheetBreakStarted: "Break Started",
  NC_setupSheetBreakSoon: "Break Soon",
  NC_shiftReassigned: "Shift Reassigned",
  NC_pendingTeamMemberApprovals: "Pending Approvals",
  NC_completeOnboardingReminder: "Complete Onboarding",
  NC_correctiveActionRead: "Corrective Action",
  NC_correctiveActionReview: "Review Required",
  NC_correctiveActionAcknowledged: "Action Acknowledged",
  NC_availabilityApproved: "Availability Approved",
  NC_availabilityDeclined: "Availability Declined",
  NC_timeOffApproved: "Time Off Approved",
  NC_timeOffDeclined: "Time Off Declined",
  NC_shiftOfferAvailable: "New Shift Offer",
  NC_shiftOfferApproved: "Shift Offer Approved",
  NC_shiftOfferAccepted: "Shift Offer Accepted",
  NC_shiftOfferAcceptanceApproved: "Shift Offer Acceptance Approved",
  NC_shiftOfferDeclined: "Shift Offer Declined",
  NC_shiftOfferCancelled: "Shift Offer Cancelled",
  NC_shiftSwapAvailable: "New Shift Swap Offered",
  NC_shiftSwapAcceptedOfferor: "Shift Swap Accepted",
  NC_shiftSwapRejectedOfferor: "Shift Swap Rejected",
  NC_shiftSwapApprovedOfferor: "Shift Swap Approved",
  NC_shiftSwapApprovedOfferee: "Shift Swap Approved",
  NC_shiftSwapDeclinedOfferor: "Shift Swap Declined",
  NC_shiftSwapDeclinedOfferee: "Shift Swap Declined",
  NC_shiftSwapCancelledOfferor: "Shift Swap Cancelled",
  NC_shiftSwapCancelledOfferee: "Shift Swap Cancelled",
  NC_positionScoreUpdated: "Position Score Updated",
  NC_noteCreated: "Note Created",
};
