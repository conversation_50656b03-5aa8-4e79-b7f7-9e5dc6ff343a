import {describe, expect, it} from 'vitest';
import {getDerivedMetrics, getMetrics, ShiftsMetrics} from './metrics.util';
import {DraftSchedule} from '../../scheduleSchemas';
import {map} from "lodash";
import {constructShift} from "../../shift";
import {payRates} from "./payRates/payRates";
import {hours} from "./hours";
import {dollars} from "./dollars";

describe('getMetrics', () => {
  const mockSchedule: DraftSchedule = {
    businessId: 'business1',
    storeId: 'store1',
    id: 'schedule1',
    week: {
      week: 32,
      year: 2024,
    },
    storeHours: {start: '08:00', end: '20:00'},
    dayParts: [],
    peakHours: [],
    days: [
      {
        dayOfWeek: 1,
        areas: [
          {
            id: 'area1',
            storeAreaId: 'storeArea1',
            title: 'Front of House',
            shifts: map([
              {id: 'shift1', range: {start: '08:00', end: '16:00'}, shiftAreaId: 'area1', isShiftLead: false},
              {id: 'shift2', range: {start: '12:00', end: '20:00'}, shiftAreaId: 'area1', isShiftLead: false},
            ], s => constructShift(s)),
            countsTowardsLabor: true,
          },
          {
            id: 'area2',
            storeAreaId: 'storeArea2',
            title: 'Training',
            shifts: map([
              {id: 'shift3', range: {start: '10:00', end: '18:00'}, shiftAreaId: 'area2', isShiftLead: false},
            ], s => constructShift(s)),
            countsTowardsLabor: false,
          },
        ],
      },
    ],
    isTemplate: false,
    isPublished: false,
  };

  it('should calculate metrics correctly for a given day', () => {
    const result = getMetrics({
      schedule: mockSchedule,
      dayOfWeek: 1,
      countOpenShiftsTowardsLabor: true,
      payRates: payRates(),
      averagePayRate: undefined,
    });
    expect(result).toEqual({
      assignedShiftCount: 0,
      totalLaborHours: 24,
      shiftCount: 3,
      totalOpsLaborHours: 16,
      totalNonOpsLaborHours: 8,
    });
  });

  it('should return blank metrics for a non-existent day', () => {
    const result = getMetrics({
      schedule: mockSchedule,
      dayOfWeek: 9,
      countOpenShiftsTowardsLabor: true,
      payRates: payRates(),
      averagePayRate: undefined,
    });
    expect(result).toEqual({
      assignedShiftCount: 0,
      totalLaborHours: 0,
      shiftCount: 0,
      totalOpsLaborHours: 0,
      totalNonOpsLaborHours: 0,
    });
  });

  it('should handle empty areas', () => {
    const emptySchedule: DraftSchedule = {
      ...mockSchedule,
      days: [
        {
          dayOfWeek: 2,
          areas: [],
        },
      ],
    };
    const result = getMetrics({
      schedule: emptySchedule,
      dayOfWeek: 2,
      countOpenShiftsTowardsLabor: true,
      payRates: payRates(),
      averagePayRate: undefined,
    });
    expect(result).toEqual({
      assignedShiftCount: 0,
      totalLaborHours: 0,
      shiftCount: 0,
      totalOpsLaborHours: 0,
      totalNonOpsLaborHours: 0,
    });
  });

  it('should handle multiple areas', () => {
    const mixedSchedule: DraftSchedule = {
      ...mockSchedule,
      days: [
        {
          dayOfWeek: 3,
          areas: [
            {
              id: 'area1',
              title: 'FOH',
              shifts: map([
                {id: 'shift1', range: {start: '08:00', end: '16:00'}, shiftAreaId: 'area1', isShiftLead: false},
              ], s => constructShift(s)),
              countsTowardsLabor: true,
            },
            {
              id: 'area2',
              title: 'Management',
              shifts: map([
                {id: 'shift2', range: {start: '09:00', end: '17:00'}, shiftAreaId: 'area2', isShiftLead: false},
              ], s => constructShift(s)),
              countsTowardsLabor: false,
            },
            {
              id: 'area3',
              title: 'BOH',
              shifts: map([
                {id: 'shift3', range: {start: '18:00', end: '22:00'}, shiftAreaId: 'area3', isShiftLead: false},
              ], s => constructShift(s)),
              countsTowardsLabor: true,
            },
          ],
        },
      ],
    };
    const result = getMetrics({
      schedule: mixedSchedule,
      dayOfWeek: 3,
      countOpenShiftsTowardsLabor: true,
      payRates: payRates(),
      averagePayRate: undefined,
    });
    expect(result).toEqual({
      assignedShiftCount: 0,
      totalLaborHours: 20,
      shiftCount: 3,
      totalOpsLaborHours: 12,
      totalNonOpsLaborHours: 8,
    });
  });

  it('should handle fractional hours correctly', () => {
    const fractionalSchedule: DraftSchedule = {
      ...mockSchedule,
      days: [
        {
          dayOfWeek: 4,
          areas: [
            {
              id: 'area1',
              title: 'FOH',
              shifts: map([
                {id: 'shift1', range: {start: '08:00', end: '12:30'}, shiftAreaId: 'area1', isShiftLead: false},
                {id: 'shift2', range: {start: '13:00', end: '17:15'}, shiftAreaId: 'area1', isShiftLead: false},
              ], s => constructShift(s)),
              countsTowardsLabor: true,
            },
          ],
        },
      ],
    };
    const result = getMetrics({
      schedule: fractionalSchedule,
      dayOfWeek: 4,
      countOpenShiftsTowardsLabor: true,
      payRates: payRates(),
      averagePayRate: undefined,
    });
    expect(result.totalLaborHours).toBeCloseTo(8.75, 2);
    expect(result.totalOpsLaborHours).toBeCloseTo(8.75, 2);
  });
});

describe('getDerivedMetrics', () => {
  const baseMetrics: ShiftsMetrics = {
    totalLaborHours: hours(40),
    shiftCount: 5,
    totalOpsLaborHours: hours(32),
    totalNonOpsLaborHours: hours(8),
    assignedShiftCount: 5,
    laborCost: dollars(600),
    averagePayRate: undefined,
  };

  it('should calculate derived metrics', () => {
    const result = getDerivedMetrics({
      metrics: baseMetrics,
      projectedRevenue: dollars(4000),
      productivityGoal: dollars(120),
    });

    expect(result).toEqual({
      ...baseMetrics,
      actualProductivity: 100,
      projectedRevenue: 4000,
      estimatedLaborHours: 4000 / 120,
      laborCost: 600,
      productivityPercentage: 4000 / 600,
      laborPercentage: 15
    });
  });

  it('should handle undefined inputs gracefully', () => {
    const result = getDerivedMetrics({
      metrics: baseMetrics,
      projectedRevenue: undefined,
      productivityGoal: undefined,
    });

    expect(result).toEqual({
      ...baseMetrics,
      actualProductivity: undefined,
      estimatedLaborHours: undefined,
      laborCost: 600,
      laborPercentage: undefined,
      projectedRevenue: undefined,
      productivityPercentage: undefined,
    });
  });

  it('should calculate partial derived metrics when some inputs are missing', () => {
    const result = getDerivedMetrics({
      metrics: baseMetrics,
      projectedRevenue: dollars(4000),
      productivityGoal: undefined,
    });

    expect(result).toEqual({
      ...baseMetrics,
      actualProductivity: 100,
      projectedRevenue: 4000,
      estimatedLaborHours: undefined,
      laborCost: 600,
      laborPercentage: 600 / 4000 * 100,
      productivityPercentage: 4000 / 600,
    });
  });

  it('should handle zero values correctly', () => {
    const zeroMetrics: ShiftsMetrics = {
      totalLaborHours: hours(0),
      shiftCount: 0,
      totalOpsLaborHours: hours(0),
      totalNonOpsLaborHours: hours(0),
      assignedShiftCount: 0,
      laborCost: dollars(0),
      averagePayRate: undefined,
    };

    const result = getDerivedMetrics({
      metrics: zeroMetrics,
      projectedRevenue: dollars(0),
      productivityGoal: dollars(0),
    });

    expect(result).toEqual({
      ...zeroMetrics,
      projectedRevenue: 0,
      actualProductivity: undefined,
      estimatedLaborHours: undefined,
      laborCost: 0,
      laborPercentage: undefined,
      productivityPercentage: undefined,
    });
  });

  it('should handle Infinity and NaN', () => {
    const result = getDerivedMetrics({
      metrics: {
        totalLaborHours: hours(Infinity),
        shiftCount: 1,
        totalOpsLaborHours: hours(Infinity),
        totalNonOpsLaborHours: hours(0),
        assignedShiftCount: 1,
        laborCost: dollars(0),
        averagePayRate: undefined,
      },
      projectedRevenue: dollars(NaN),
      productivityGoal: dollars(0),
    });
    expect(result.actualProductivity).toBe(undefined);
    expect(result.estimatedLaborHours).toBe(undefined);
    expect(result.laborCost).toBe(0);
    expect(result.productivityPercentage).toBe(undefined);
  });
});
