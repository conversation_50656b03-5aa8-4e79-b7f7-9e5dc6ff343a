import {beforeEach, describe, expect, it} from 'vitest';
import {hasOverlappingShifts, isWithinAvailability, isWithinTimeOff, validateSchedule, validateHawaiiACAEligibility} from './scheduleValidation';
import {DraftSchedule, HawaiiACAWeekSchedules, HawaiiACAPersonHours, ScheduleDay, Shift} from './scheduleSchemas';
import {map, times} from "lodash";
import {DateTimeRange, WeekDayTimeRange} from './timeSchemas';
import {availabilityRanges} from "./availabilitySchemas";
import {ScheduleValidationRequest} from "./scheduleValidation.types";
import {scheduleAndPeopleToValidationRequest} from "./scheduleValidation.util";
import {constructShift, updateShiftRange} from "./shift";
import {mapShifts} from "./schedule";
import {bohAreaId, fohAreaId} from "./stockAreaTemplates";
import {preferenceRanges} from "./availabilitySchemas";

// Helper functions for tests
const createTestSched = (): DraftSchedule => ({
  id: 'schedule1',
  businessId: 'business1',
  storeId: 'store1',
  week: {year: 2024, week: 1},
  storeHours: {start: '09:00', end: '17:00'},
  dayParts: [],
  peakHours: [],
  isTemplate: false,
  isPublished: false,
  days: times(6, (i): ScheduleDay => ({
    dayOfWeek: i + 1,
    areas: [
      {
        id: 'area' + (i + 1),
        title: 'Area 1',
        countsTowardsLabor: true,
        storeAreaId: 'area1',
        shifts: map([
          {
            id: 'shift' + (i + 1),
            range: {start: '09:00', end: '17:00'},
            shiftAreaId: 'area1',
            assignedPersonId: 'person1',
            isShiftLead: true
          }
        ], s => constructShift(s))
      }
    ]
  }))
});

let testIdIdx = 0;
const createTestValidationReq = (): ScheduleValidationRequest => ({
  schedule: createTestSched(),
  people: [{
    id: "person1",
    maxHoursPreferred: 50,
    weekAvailability: availabilityRanges.parse([
      {dayOfWeek: 1, start: '09:00', end: '17:00'},
      {dayOfWeek: 2, start: '09:00', end: '17:00'},
      {dayOfWeek: 3, start: '09:00', end: '17:00'},
      {dayOfWeek: 4, start: '09:00', end: '17:00'},
      {dayOfWeek: 5, start: '09:00', end: '17:00'},
      {dayOfWeek: 6, start: '09:00', end: '17:00'},
      {dayOfWeek: 7, start: '09:00', end: '17:00'}
    ]),
    timeOff: [],
    pendingTimeOff: [],
    storeIds: ["store1"],
    status: "Active",
    training: [],
    age: 18
  }],
  timezone: "America/Denver",
  storeAreas: [],
  genId: () => `id-${testIdIdx++}`,
  maxShiftDuration: 16,
  minShiftDuration: 1,
  settings: {
    disabledScheduleValidations: []
  },
  weeksHoursMap: {},
  storeState: 'CA' // Default to non-Hawaii state for most tests
});

const createScheduleWithHours = (year: number, week: number, personId: string, totalHours: number): DraftSchedule => {
  // Distribute hours across multiple days to avoid creating invalid long shifts
  const hoursPerDay = Math.min(8, totalHours); // Max 8 hours per day
  const daysNeeded = Math.ceil(totalHours / hoursPerDay);

  return {
    id: `schedule-${year}-${week}`,
    businessId: 'business1',
    storeId: 'store1',
    week: {year, week},
    storeHours: {start: '09:00', end: '17:00'},
    dayParts: [],
    peakHours: [],
    isTemplate: false,
    isPublished: false,
    days: times(7, (i): ScheduleDay => {
      const dayOfWeek = i + 1;
      const dayIndex = i;
      let dayHours = 0;

      if (dayIndex < daysNeeded && totalHours > 0) {
        // Calculate hours for this day
        const remainingHours = totalHours - (dayIndex * hoursPerDay);
        dayHours = Math.min(hoursPerDay, remainingHours);
      }

      const shifts = dayHours > 0 ? [constructShift({
        id: `shift-${year}-${week}-${dayOfWeek}`,
        range: {
          start: '09:00',
          end: `${9 + Math.floor(dayHours)}:${Math.floor((dayHours % 1) * 60).toString().padStart(2, '0')}`
        },
        shiftAreaId: 'area1',
        assignedPersonId: personId,
        isShiftLead: false
      })] : [];

      return {
        dayOfWeek,
        areas: [{
          id: `area${dayOfWeek}`,
          title: 'Area 1',
          countsTowardsLabor: true,
          storeAreaId: 'area1',
          shifts
        }]
      };
    })
  };
};

describe('validateSchedule', () => {
  const createSched = (): DraftSchedule => ({
    id: 'schedule1',
    businessId: 'business1',
    storeId: 'store1',
    week: {year: 2024, week: 1},
    storeHours: {start: '09:00', end: '17:00'},
    dayParts: [],
    peakHours: [],
    isTemplate: false,
    isPublished: false,
    days: times(6, (i): ScheduleDay => ({
      dayOfWeek: i + 1,
      areas: [
        {
          id: 'area' + (i + 1),
          title: 'Area 1',
          countsTowardsLabor: true,
          storeAreaId: 'area1',
          shifts: map([
            {
              id: 'shift' + (i + 1),
              range: {start: '09:00', end: '17:00'},
              shiftAreaId: 'area1',
              assignedPersonId: 'person1',
              isShiftLead: true
            }
          ], s => constructShift(s))
        }
      ]
    }))
  });

  let idIdx = 0;
  const createValidationReq = (): ScheduleValidationRequest => ({
    schedule: createSched(),
    people: [{
      id: "person1",
      maxHoursPreferred: 50,
      weekAvailability: availabilityRanges.parse([
        {dayOfWeek: 1, start: '09:00', end: '17:00'},
        {dayOfWeek: 2, start: '09:00', end: '17:00'},
        {dayOfWeek: 3, start: '09:00', end: '17:00'},
        {dayOfWeek: 4, start: '09:00', end: '17:00'},
        {dayOfWeek: 5, start: '09:00', end: '17:00'},
        {dayOfWeek: 6, start: '09:00', end: '17:00'},
      ]),
      timeOff: [],
      pendingTimeOff: [],
      storeIds: ["store1"],
      status: "Active",
      training: [],
      age: 18
    }],
    maxShiftDuration: 12,
    minShiftDuration: 4,
    genId: () => 'valMsg_' + idIdx++,
    timezone: "America/Denver",
    storeAreas: [{
      id: 'area1', // matches shiftAreaId in schedule
      title: 'Kitchen',
      createdFromTemplateId: bohAreaId,
      positions: [],
      storeId: 'store1',
      isActive: true,
      order: 0
    }, {
      id: 'fohArea',
      title: 'Front of House',
      createdFromTemplateId: fohAreaId,
      positions: [],
      storeId: 'store1',
      isActive: true,
      order: 0
    }],
    settings: {
      disabledScheduleValidations: []
    },
    weeksHoursMap: {},
    storeState: 'CA' // Default to non-Hawaii state for most tests
  });

  beforeEach(() => {
    idIdx = 0;
  });

  describe('MinorScheduledBeforePermittedDayStart', () => {
    it('should return error when 14/15 minor is scheduled before 7am', () => {
      const request = createValidationReq();
      // Set person age to 14
      request.people[0].age = 14;
      // Set shift to start at 6am
      request.schedule.days[0].areas[0].shifts[0].range = {start: '06:00', end: '10:00'};

      const result = validateSchedule(request);

      expect(result).toMatchObject({
        isValid: false,
        messages: expect.arrayContaining([expect.objectContaining({
          code: "MinorScheduledBeforePermittedDayStart",
          severity: 20, // error severity
          dayOfWeek: 1,
          info: {
            personId: "person1",
            shift: expect.objectContaining({
              range: {start: '06:00', end: '10:00'}
            })
          }
        })])
      });
    });

    it('should not return error when 14/15 minor is scheduled at/after 7am', () => {
      const request = createValidationReq();
      request.people[0].age = 14;
      request.schedule.days[0].areas[0].shifts[0].range = {start: '07:00', end: '10:00'};

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorScheduledBeforePermittedDayStart"
          })
        ])
      );
    });

    it('should not return error when 16/17 is scheduled before 7am', () => {
      const request = createValidationReq();
      request.people[0].age = 16;
      request.schedule.days[0].areas[0].shifts[0].range = {start: '06:00', end: '10:00'};

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorScheduledBeforePermittedDayStart"
          })
        ])
      );
    });

    it('should not return error when adult is scheduled before 7am', () => {
      const request = createValidationReq();
      request.people[0].age = 18;
      request.schedule.days[0].areas[0].shifts[0].range = {start: '06:00', end: '10:00'};

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorScheduledBeforePermittedDayStart"
          })
        ])
      );
    });
  });

  describe('MinorScheduledAfterPermittedDayEnd', () => {
    it('should return error when 14/15 minor is scheduled after 7pm', () => {
      const request = createValidationReq();
      request.people[0].age = 14;
      request.schedule.days[0].areas[0].shifts[0].range = {start: '15:00', end: '19:30'};

      const result = validateSchedule(request);

      expect(result).toMatchObject({
        isValid: false,
        messages: expect.arrayContaining([expect.objectContaining({
          code: "MinorScheduledAfterPermittedDayEnd",
          severity: 10, // warning severity
          dayOfWeek: 1,
          info: {
            personId: "person1",
            shift: expect.objectContaining({
              range: {start: '15:00', end: '19:30'}
            })
          }
        })])
      });
    });

    it('should not return error when 14/15 minor is scheduled before 7pm', () => {
      const request = createValidationReq();
      request.people[0].age = 14;
      request.schedule.days[0].areas[0].shifts[0].range = {start: '15:00', end: '19:00'};

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorScheduledAfterPermittedDayEnd"
          })
        ])
      );
    });

    it('should not return error when 16/17 is scheduled after 7pm', () => {
      const request = createValidationReq();
      request.people[0].age = 16;
      request.schedule.days[0].areas[0].shifts[0].range = {start: '15:00', end: '19:30'};

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorScheduledAfterPermittedDayEnd"
          })
        ])
      );
    });

    it('should not return error when adult is scheduled after 7pm', () => {
      const request = createValidationReq();
      request.people[0].age = 18;
      request.schedule.days[0].areas[0].shifts[0].range = {start: '15:00', end: '19:30'};

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorScheduledAfterPermittedDayEnd"
          })
        ])
      );
    });
  });

  describe('MinorScheduledCloseToPermittedDayEnd', () => {
    it('should return warning when 14/15 minor is scheduled close to 7pm', () => {
      const request = createValidationReq();
      request.people[0].age = 14;
      request.schedule.days[0].areas[0].shifts[0].range = {start: '15:00', end: '18:58'};

      const result = validateSchedule(request);

      expect(result).toMatchObject({
        isValid: false,
        messages: expect.arrayContaining([expect.objectContaining({
          code: "MinorScheduledCloseToPermittedDayEnd",
          severity: 10, // warning severity
          dayOfWeek: 1,
          info: {
            personId: "person1",
            shift: expect.objectContaining({
              range: {start: '15:00', end: '18:58'}
            })
          }
        })])
      });
    });

    it('should not return warning when 14/15 minor ends shift well before 7pm', () => {
      const request = createValidationReq();
      request.people[0].age = 14;
      request.schedule.days[0].areas[0].shifts[0].range = {start: '15:00', end: '18:30'};

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorScheduledCloseToPermittedDayEnd"
          })
        ])
      );
    });

    it('should not return warning when adult ends shift close to 7pm', () => {
      const request = createValidationReq();
      request.people[0].age = 18;
      request.schedule.days[0].areas[0].shifts[0].range = {start: '15:00', end: '18:58'};

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorScheduledCloseToPermittedDayEnd"
          })
        ])
      );
    });
  });

  describe('CloserOpenerShift', () => {
    it('should return warning when less than 10 hours between shifts', () => {
      const request = createValidationReq();
      // Set up a late shift on day 1
      request.schedule.days[0].areas[0].shifts[0].range = {start: '13:00', end: '22:00'};
      // Set up an early shift on day 2
      request.schedule.days[1].areas[0].shifts[0].range = {start: '06:00', end: '14:00'};

      const result = validateSchedule(request);

      expect(result).toMatchObject({
        isValid: false,
        messages: expect.arrayContaining([expect.objectContaining({
          code: "CloserOpenerShift",
          severity: 10, // warning severity
          dayOfWeek: 2, // warning appears on the opener's day
          info: {
            personId: "person1",
            shift: expect.objectContaining({
              range: {start: '06:00', end: '14:00'}
            }),
            gapDurationHours: 8
          }
        })])
      });
    });

    it('should not return warning when exactly 10 hours between shifts', () => {
      const request = createValidationReq();
      // End at 20:00 (8pm)
      request.schedule.days[0].areas[0].shifts[0].range = {start: '12:00', end: '20:00'};
      // Start at 06:00 (6am) next day - exactly 10 hours gap
      request.schedule.days[1].areas[0].shifts[0].range = {start: '06:00', end: '14:00'};

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "CloserOpenerShift"
          })
        ])
      );
    });

    it('should not return warning when more than 10 hours between shifts', () => {
      const request = createValidationReq();
      // End at 18:00 (6pm)
      request.schedule.days[0].areas[0].shifts[0].range = {start: '10:00', end: '18:00'};
      // Start at 07:00 (7am) next day - 13 hours gap
      request.schedule.days[1].areas[0].shifts[0].range = {start: '07:00', end: '15:00'};

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "CloserOpenerShift"
          })
        ])
      );
    });

    it('should only check consecutive days', () => {
      const request = createValidationReq();
      // Late shift on day 1
      request.schedule.days[0].areas[0].shifts[0].range = {start: '14:00', end: '22:00'};
      // Early shift on day 3 (not consecutive)
      request.schedule.days[2].areas[0].shifts[0].range = {start: '06:00', end: '14:00'};

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "CloserOpenerShift"
          })
        ])
      );
    });

    it('should only check for the same person', () => {
      const request = createValidationReq();
      // Late shift for person1 on day 1
      request.schedule.days[0].areas[0].shifts[0].range = {start: '14:00', end: '22:00'};

      // Early shift for person2 on day 2
      request.schedule.days[1].areas[0].shifts[0].assignedPersonId = 'person2';
      request.schedule.days[1].areas[0].shifts[0].range = {start: '06:00', end: '14:00'};

      // Add person2 to the people list
      request.people.push({
        ...request.people[0],
        id: 'person2'
      });

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "CloserOpenerShift"
          })
        ])
      );
    });

    it('should calculate gap duration correctly across midnight', () => {
      const request = createValidationReq();
      // End at 23:00 (11pm)
      request.schedule.days[0].areas[0].shifts[0].range = {start: '15:00', end: '23:00'};
      // Start at 07:00 (7am) next day - 8 hours gap
      request.schedule.days[1].areas[0].shifts[0].range = {start: '07:00', end: '15:00'};

      const result = validateSchedule(request);

      const closerOpenerWarning = result.messages.find(m => m.code === 'CloserOpenerShift');
      expect(closerOpenerWarning).toBeDefined();
      expect(closerOpenerWarning?.info.gapDurationHours).toBe(8); // 8 hours plus one minute for midnight
    });
  });

  describe('TimeOffRequestConflict', () => {
    it('should return warning when shift overlaps with pending time off', () => {
      const request = createValidationReq();
      request.people[0].pendingTimeOff = [{
        requestId: 'timeOff1',
        status: 'Pending',
        start: new Date('2024-01-01T16:00:00Z'), // 9AM MST
        end: new Date('2024-01-01T20:00:00Z')    // 1PM MST
      }];

      const result = validateSchedule(request);

      expect(result).toMatchObject({
        isValid: false,
        messages: expect.arrayContaining([expect.objectContaining({
          code: "TimeOffRequestConflict",
          severity: 10, // warning severity
          dayOfWeek: 1,
          info: {
            personId: "person1",
            timeOffRequestId: 'timeOff1',
            shift: expect.objectContaining({
              range: {start: '09:00', end: '17:00'}
            })
          }
        })])
      });
    });

    it('should not return warning for approved time off (should be error instead)', () => {
      const request = createValidationReq();
      // Add as approved time off instead of pending
      request.people[0].timeOff = [{
        requestId: 'timeOff1',
        status: 'Approved',
        start: new Date('2024-01-01T16:00:00Z'),
        end: new Date('2024-01-01T20:00:00Z')
      }];

      const result = validateSchedule(request);

      // Should not see TimeOffRequestConflict
      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "TimeOffRequestConflict"
          })
        ])
      );

      // Should see ShiftConflictsWithTimeOff instead
      expect(result.messages).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "ShiftConflictsWithTimeOff",
            severity: 20 // error severity
          })
        ])
      );
    });

    it('should handle multiple pending time off requests', () => {
      const request = createValidationReq();
      request.people[0].pendingTimeOff = [
        {
          requestId: 'timeOff1',
          status: 'Pending',
          start: new Date('2024-01-01T16:00:00Z'),
          end: new Date('2024-01-01T20:00:00Z')
        },
        {
          requestId: 'timeOff2',
          status: 'Pending',
          start: new Date('2024-01-02T16:00:00Z'),
          end: new Date('2024-01-02T20:00:00Z')
        }
      ];

      const result = validateSchedule(request);

      const timeOffWarnings = result.messages.filter(m => m.code === 'TimeOffRequestConflict');
      expect(timeOffWarnings).toHaveLength(2);
      expect(timeOffWarnings[0].info.timeOffRequestId).toBe('timeOff1');
      expect(timeOffWarnings[1].info.timeOffRequestId).toBe('timeOff2');
    });

    it('should not return warning when shift does not overlap with pending time off', () => {
      const request = createValidationReq();
      request.people[0].pendingTimeOff = [{
        requestId: 'timeOff1',
        status: 'Pending',
        start: new Date('2025-01-01T22:00:00Z'), // 3PM MST
        end: new Date('2025-01-02T02:00:00Z')    // 7PM MST
      }];

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "TimeOffRequestConflict"
          })
        ])
      );
    });
  });

  describe('ScheduledOvertimeHours', () => {
    it('should return warning when person is scheduled over 40 hours', () => {
      const request = createValidationReq();
      // Set up 5 days of 9-hour shifts (45 hours total)
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts[0].range = {
          start: '09:00',
          end: '18:00'
        };
      }

      const result = validateSchedule(request);

      expect(result).toMatchObject({
        isValid: false,
        messages: expect.arrayContaining([expect.objectContaining({
          code: "ScheduledOvertimeHours",
          severity: 10, // warning severity
          info: {
            personId: "person1"
          }
        })])
      });
    });

    it('should not return warning when exactly 40 hours', () => {
      const request = createValidationReq();
      // Set up 5 days of 8-hour shifts (40 hours total)
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts[0].range = {
          start: '09:00',
          end: '17:00'
        };
      }

      // clear out the Saturday shift
      request.schedule.days[5].areas[0].shifts = [];

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "ScheduledOvertimeHours"
          })
        ])
      );
    });

    it('should not return warning when less than 40 hours', () => {
      const request = createValidationReq();
      // Set up 5 days of 7-hour shifts (35 hours total)
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts[0].range = {
          start: '09:00',
          end: '16:00'
        };
      }
      // clear out the Saturday shift
      request.schedule.days[5].areas[0].shifts = [];

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "ScheduledOvertimeHours"
          })
        ])
      );
    });

    it('should count total hours across multiple shifts per day', () => {
      const request = createValidationReq();
      // Set up 4 days of 8-hour shifts (32 hours)
      for (let i = 0; i < 4; i++) {
        request.schedule.days[i].areas[0].shifts[0].range = {
          start: '09:00',
          end: '17:00'
        };
      }

      // Add two 5-hour shifts on the fifth day (10 more hours, total 42)
      request.schedule.days[4].areas[0].shifts = [
        constructShift({
          id: 'shift5a',
          shiftAreaId: 'area1',
          assignedPersonId: 'person1',
          range: {start: '09:00', end: '14:00'},
          isShiftLead: false
        }),
        constructShift({
          id: 'shift5b',
          shiftAreaId: 'area1',
          assignedPersonId: 'person1',
          range: {start: '15:00', end: '20:00'},
          isShiftLead: false
        })
      ];

      // clear out the Saturday shift
      request.schedule.days[5].areas[0].shifts = [];

      const result = validateSchedule(request);

      expect(result).toMatchObject({
        isValid: false,
        messages: expect.arrayContaining([expect.objectContaining({
          code: "ScheduledOvertimeHours",
          info: {
            personId: "person1"
          }
        })])
      });
    });

    it('should track overtime separately for each person', () => {
      const request = createValidationReq();
      // Add second person
      request.people.push({
        ...request.people[0],
        id: 'person2'
      });

      // Set up 5 days of 9-hour shifts for person1 (45 hours)
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts[0].range = {
          start: '09:00',
          end: '18:00'
        };
      }

      // Add 5 days of 7-hour shifts for person2 (35 hours)
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts.push(
          constructShift({
            id: `shift${i}_person2`,
            shiftAreaId: 'area1',
            assignedPersonId: 'person2',
            range: {start: '11:00', end: '18:00'},
            isShiftLead: false
          })
        );
      }

      const result = validateSchedule(request);

      // Should only have overtime warning for person1
      const overtimeWarnings = result.messages.filter(m => m.code === 'ScheduledOvertimeHours');
      expect(overtimeWarnings).toHaveLength(1);
      expect(overtimeWarnings[0].info.personId).toBe('person1');
    });

    it('should handle fractional hours correctly', () => {
      const request = createValidationReq();
      // Set up 5 days of 8 hours and 6 minutes (40.5 hours total)
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts[0].range = {
          start: '09:00',
          end: '17:06'
        };
      }

      const result = validateSchedule(request);

      expect(result).toMatchObject({
        isValid: false,
        messages: expect.arrayContaining([expect.objectContaining({
          code: "ScheduledOvertimeHours",
          info: {
            personId: "person1"
          }
        })])
      });
    });
  });

  describe('MinorSchoolDayMaxHoursPerDay', () => {
    it('should return warning when 14/15 minor works over 3 hours on a school day', () => {
      const request = createValidationReq();

      // Clear out all shifts
      for (let i = 1; i < 6; i++) {
        request.schedule.days[i].areas[0].shifts = []
      }

      request.people[0].age = 14;
      // Set up a 4-hour shift on Monday (day 1)
      request.schedule.days[0].areas[0].shifts[0].range = {
        start: '14:00',
        end: '18:00'
      };

      const result = validateSchedule(request);

      expect(result).toMatchObject({
        isValid: false,
        messages: expect.arrayContaining([expect.objectContaining({
          code: "MinorSchoolDayMaxHoursPerDay",
          severity: 10, // warning severity
          dayOfWeek: 1,
          info: {
            personId: "person1"
          }
        })])
      });
    });

    it('should not return warning when 14/15 minor works exactly 3 hours on a school day', () => {
      const request = createValidationReq();
      request.people[0].age = 14;

      // Clear out all shifts
      for (let i = 1; i < 6; i++) {
        request.schedule.days[i].areas[0].shifts = []
      }

      request.schedule.days[0].areas[0].shifts[0].range = {
        start: '14:00',
        end: '17:00'
      };

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorSchoolDayMaxHoursPerDay"
          })
        ])
      );
    });

    it('should not return warning for shifts over 3 hours on weekends', () => {
      const request = createValidationReq();
      request.people[0].age = 14;

      // Clear out all shifts
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts = []
      }

      // Set up a 6-hour shift on Saturday (day 6)
      request.schedule.days[5].areas[0].shifts[0].range = {
        start: '12:00',
        end: '20:00'
      };

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorSchoolDayMaxHoursPerDay"
          })
        ])
      );
    });

    it('should not return warning for 16/17 minors working over 3 hours', () => {
      const request = createValidationReq();
      request.people[0].age = 16;
      // 4-hour shift on Monday
      request.schedule.days[0].areas[0].shifts[0].range = {
        start: '14:00',
        end: '18:00'
      };

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorSchoolDayMaxHoursPerDay"
          })
        ])
      );
    });

    it('should not return warning for adults working over 3 hours', () => {
      const request = createValidationReq();
      request.people[0].age = 18;
      request.schedule.days[0].areas[0].shifts[0].range = {
        start: '14:00',
        end: '18:00'
      };

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorSchoolDayMaxHoursPerDay"
          })
        ])
      );
    });

    it('should check total hours across multiple shifts in same school day', () => {
      const request = createValidationReq();
      request.people[0].age = 14;

      // Clear out all shifts
      for (let i = 1; i < 6; i++) {
        request.schedule.days[i].areas[0].shifts = []
      }

      // Two 2-hour shifts on Monday (4 hours total)
      const shift1 = constructShift({
        id: 'shift1a',
        shiftAreaId: 'area1',
        assignedPersonId: 'person1',
        range: {start: '14:00', end: '16:00'},
        isShiftLead: false
      });
      const shift2 = constructShift({
        id: 'shift1b',
        shiftAreaId: 'area1',
        assignedPersonId: 'person1',
        range: {start: '17:00', end: '19:00'},
        isShiftLead: false
      });
      request.schedule.days[0].areas[0].shifts = [
        shift1,
        shift2
      ];
      const result = validateSchedule(request);

      expect(result).toMatchObject({
        isValid: false,
        messages: expect.arrayContaining([expect.objectContaining({
          code: "MinorSchoolDayMaxHoursPerDay",
          dayOfWeek: 1,
          info: {
            personId: "person1"
          }
        })])
      });
    });

    it('should check all weekdays separately', () => {
      const request = createValidationReq();
      request.people[0].age = 14;
      // Set up 4-hour shifts Monday through Friday
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts[0].range = {
          start: '14:00',
          end: '18:00'
        };
      }

      const result = validateSchedule(request);

      // Should have 5 warnings, one for each weekday
      const schoolDayWarnings = result.messages.filter(m =>
        m.code === 'MinorSchoolDayMaxHoursPerDay'
      );
      expect(schoolDayWarnings).toHaveLength(5);
      // Verify warnings are for days 1-5
      expect(new Set(schoolDayWarnings.map(w => w.dayOfWeek)))
        .toEqual(new Set([1, 2, 3, 4, 5]));
    });
  });

  describe('MinorMaxHoursPerSchoolWeek', () => {
    it('should return warning when 14/15 minor exceeds 18 hours in a school week', () => {
      const request = createValidationReq();
      request.people[0].age = 14;

      // Set up 4 hours per day Mon-Fri (20 hours total)
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts[0].range = {
          start: '14:00',
          end: '18:00'
        };
      }

      const result = validateSchedule(request);

      expect(result).toMatchObject({
        isValid: false,
        messages: expect.arrayContaining([expect.objectContaining({
          code: "MinorMaxHoursPerSchoolWeek",
          severity: 10, // warning severity
          info: {
            personId: "person1"
          }
        })])
      });
    });

    it('should not return warning when exactly 18 hours in a school week', () => {
      const request = createValidationReq();
      request.people[0].age = 14;

      // Set up 3 hours per day Mon-Fri (15 hours)
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts[0].range = {
          start: '14:00',
          end: '17:00'
        };
      }

      // Add 3 more hours on Saturday (18 total)
      request.schedule.days[5].areas[0].shifts[0].range = {
        start: '14:00',
        end: '17:00'
      };

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorMaxHoursPerSchoolWeek"
          })
        ])
      );
    });

    it('should not return warning for 16/17 minors exceeding 18 hours', () => {
      const request = createValidationReq();
      request.people[0].age = 16;

      // Set up 5 hours per day Mon-Fri (25 hours total)
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts[0].range = {
          start: '14:00',
          end: '19:00'
        };
      }

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorMaxHoursPerSchoolWeek"
          })
        ])
      );
    });

    it('should not return warning for adults exceeding 18 hours', () => {
      const request = createValidationReq();
      request.people[0].age = 18;

      // Set up 8 hours per day Mon-Fri (40 hours total)
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts[0].range = {
          start: '09:00',
          end: '17:00'
        };
      }

      const result = validateSchedule(request);

      expect(result.messages).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            code: "MinorMaxHoursPerSchoolWeek"
          })
        ])
      );
    });

    it('should count hours from multiple shifts per day', () => {
      const request = createValidationReq();
      request.people[0].age = 14;

      // Set up two 2-hour shifts per day Mon-Fri (20 hours total)
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts = [
          constructShift({
            id: `shift${i}a`,
            shiftAreaId: 'area1',
            assignedPersonId: 'person1',
            range: {start: '14:00', end: '16:00'},
            isShiftLead: false
          }),
          constructShift({
            id: `shift${i}b`,
            shiftAreaId: 'area1',
            assignedPersonId: 'person1',
            range: {start: '17:00', end: '19:00'},
            isShiftLead: false
          })
        ];
      }

      const result = validateSchedule(request);

      expect(result).toMatchObject({
        isValid: false,
        messages: expect.arrayContaining([expect.objectContaining({
          code: "MinorMaxHoursPerSchoolWeek",
          info: {
            personId: "person1"
          }
        })])
      });
    });

    it('should include weekend hours in the total', () => {
      const request = createValidationReq();
      request.people[0].age = 14;

      // Set up 3 hours per day Mon-Fri (15 hours)
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts[0].range = {
          start: '14:00',
          end: '17:00'
        };
      }

      // Add 4 hours on Saturday (19 total)
      request.schedule.days[5].areas[0].shifts[0].range = {
        start: '14:00',
        end: '18:00'
      };

      const result = validateSchedule(request);

      expect(result).toMatchObject({
        isValid: false,
        messages: expect.arrayContaining([expect.objectContaining({
          code: "MinorMaxHoursPerSchoolWeek",
          info: {
            personId: "person1"
          }
        })])
      });
    });

    it('should handle fractional hours correctly', () => {
      const request = createValidationReq();
      request.people[0].age = 14;

      // Set up 3.5 hours per day Mon-Fri (17.5 hours)
      for (let i = 0; i < 5; i++) {
        request.schedule.days[i].areas[0].shifts[0].range = {
          start: '14:00',
          end: '17:30'
        };
      }

      // Add 1 hour on Saturday (18.5 total)
      request.schedule.days[5].areas[0].shifts[0].range = {
        start: '14:00',
        end: '15:00'
      };

      const result = validateSchedule(request);

      expect(result).toMatchObject({
        isValid: false,
        messages: expect.arrayContaining([expect.objectContaining({
          code: "MinorMaxHoursPerSchoolWeek",
          info: {
            personId: "person1"
          }
        })])
      });
    });
  });

  describe('MinorOvertimeDay and MinorOvertimeWeek', () => {
    describe('MinorOvertimeDay', () => {
      it('should return error when 14/15 minor exceeds 8 hours in a day', () => {
        const request = createValidationReq();
        request.people[0].age = 14;
        // Set up a 9-hour shift
        request.schedule.days[0].areas[0].shifts[0].range = {
          start: '09:00',
          end: '18:00'
        };

        const result = validateSchedule(request);

        expect(result).toMatchObject({
          isValid: false,
          messages: expect.arrayContaining([expect.objectContaining({
            code: "MinorOvertimeDay",
            severity: 20, // error severity
            dayOfWeek: 1,
            info: {
              personId: "person1"
            }
          })])
        });
      });

      it('should not return error when 14/15 minor works exactly 8 hours', () => {
        const request = createValidationReq();
        request.people[0].age = 14;
        request.schedule.days[0].areas[0].shifts[0].range = {
          start: '09:00',
          end: '17:00'
        };

        const result = validateSchedule(request);

        expect(result.messages).not.toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              code: "MinorOvertimeDay"
            })
          ])
        );
      });

      it('should count multiple shifts in same day toward 8-hour limit', () => {
        const request = createValidationReq();
        request.people[0].age = 14;
        // Two 5-hour shifts (10 hours total)
        request.schedule.days[0].areas[0].shifts = [
          constructShift({
            id: 'shift1',
            shiftAreaId: 'area1',
            assignedPersonId: 'person1',
            range: {start: '09:00', end: '14:00'},
            isShiftLead: false
          }),
          constructShift({
            id: 'shift2',
            shiftAreaId: 'area1',
            assignedPersonId: 'person1',
            range: {start: '15:00', end: '20:00'},
            isShiftLead: false
          })
        ];

        const result = validateSchedule(request);

        expect(result).toMatchObject({
          isValid: false,
          messages: expect.arrayContaining([expect.objectContaining({
            code: "MinorOvertimeDay",
            dayOfWeek: 1,
            info: expect.objectContaining({
              personId: "person1"
            })
          })])
        });
      });
    });

    describe('MinorOvertimeWeek', () => {
      it('should return error when 14/15 minor exceeds 40 hours in a week', () => {
        const request = createValidationReq();
        request.people[0].age = 14;
        // Set up 8.5-hour shifts Mon-Fri (42.5 hours total)
        for (let i = 0; i < 5; i++) {
          request.schedule.days[i].areas[0].shifts[0].range = {
            start: '09:00',
            end: '17:30'
          };
        }

        const result = validateSchedule(request);

        expect(result).toMatchObject({
          isValid: false,
          messages: expect.arrayContaining([expect.objectContaining({
            code: "MinorOvertimeWeek",
            severity: 20, // error severity
            info: {
              personId: "person1"
            }
          })])
        });
      });

      it('should not return error when 14/15 minor works exactly 40 hours', () => {
        const request = createValidationReq();
        request.people[0].age = 14;
        // Set up 8-hour shifts Mon-Fri (40 hours total)
        for (let i = 0; i < 5; i++) {
          request.schedule.days[i].areas[0].shifts[0].range = {
            start: '09:00',
            end: '17:00'
          };
        }

        // clear out the shift on Saturday
        request.schedule.days[5].areas[0].shifts = [];

        const result = validateSchedule(request);

        expect(result.messages).not.toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              code: "MinorOvertimeWeek"
            })
          ])
        );
      });

      it('should include weekend hours in weekly total', () => {
        const request = createValidationReq();
        request.people[0].age = 14;
        // Set up 7-hour shifts Mon-Fri (35 hours)
        for (let i = 0; i < 5; i++) {
          request.schedule.days[i].areas[0].shifts[0].range = {
            start: '09:00',
            end: '16:00'
          };
        }
        // Add 6-hour shift on Saturday (41 hours total)
        request.schedule.days[5].areas[0].shifts[0].range = {
          start: '09:00',
          end: '15:00'
        };

        const result = validateSchedule(request);

        expect(result).toMatchObject({
          isValid: false,
          messages: expect.arrayContaining([expect.objectContaining({
            code: "MinorOvertimeWeek",
            info: {
              personId: "person1"
            }
          })])
        });
      });
    });

    describe('Age-specific behavior', () => {
      it('should not apply to 16/17 minors', () => {
        const request = createValidationReq();
        request.people[0].age = 16;
        // 9-hour shift (over 8-hour limit)
        request.schedule.days[0].areas[0].shifts[0].range = {
          start: '09:00',
          end: '18:00'
        };

        const result = validateSchedule(request);

        expect(result.messages).not.toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              code: "MinorOvertimeDay"
            })
          ])
        );
        expect(result.messages).not.toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              code: "MinorOvertimeWeek"
            })
          ])
        );
      });

      it('should not apply to adults', () => {
        const request = createValidationReq();
        request.people[0].age = 18;
        // 9-hour shift (over 8-hour limit)
        request.schedule.days[0].areas[0].shifts[0].range = {
          start: '09:00',
          end: '18:00'
        };

        const result = validateSchedule(request);

        expect(result.messages).not.toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              code: "MinorOvertimeDay"
            })
          ])
        );
        expect(result.messages).not.toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              code: "MinorOvertimeWeek"
            })
          ])
        );
      });
    });

    describe('Interaction with other validations', () => {
      it('should show MinorOvertimeDay instead of PotentialOvertimeShift for 14/15 minors', () => {
        const request = createValidationReq();
        request.people[0].age = 14;
        // 9-hour shift
        request.schedule.days[0].areas[0].shifts[0].range = {
          start: '09:00',
          end: '18:00'
        };

        const result = validateSchedule(request);

        // Should see MinorOvertimeDay
        expect(result.messages).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              code: "MinorOvertimeDay"
            })
          ])
        );

        // Should not see PotentialOvertimeShift
        expect(result.messages).not.toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              code: "PotentialOvertimeShift"
            })
          ])
        );
      });

      it('should show MinorOvertimeWeek instead of ScheduledOvertimeHours for 14/15 minors', () => {
        const request = createValidationReq();
        request.people[0].age = 14;
        // Set up 8.5-hour shifts Mon-Fri (42.5 hours total)
        for (let i = 0; i < 5; i++) {
          request.schedule.days[i].areas[0].shifts[0].range = {
            start: '09:00',
            end: '17:30'
          };
        }

        const result = validateSchedule(request);

        // Should see MinorOvertimeWeek
        expect(result.messages).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              code: "MinorOvertimeWeek"
            })
          ])
        );

        // Should not see ScheduledOvertimeHours
        expect(result.messages).not.toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              code: "ScheduledOvertimeHours"
            })
          ])
        );
      });
    });
  });

  describe('ShiftDurationExceedsMax', () => {
    it('should return warning when shift duration exceeds max', () => {
      const request = createValidationReq();
      request.maxShiftDuration = 6;
      const result = validateSchedule(request);
      expect(result).toMatchInlineSnapshot(`
        {
          "draftScheduleId": "schedule1",
          "isValid": false,
          "messages": [
            {
              "code": "ShiftDurationExceedsMax",
              "dayOfWeek": 1,
              "id": "valMsg_0",
              "info": {
                "maxDuration": 6,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift1",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftDurationExceedsMax",
              "dayOfWeek": 2,
              "id": "valMsg_1",
              "info": {
                "maxDuration": 6,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift2",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftDurationExceedsMax",
              "dayOfWeek": 3,
              "id": "valMsg_2",
              "info": {
                "maxDuration": 6,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift3",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftDurationExceedsMax",
              "dayOfWeek": 4,
              "id": "valMsg_3",
              "info": {
                "maxDuration": 6,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift4",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftDurationExceedsMax",
              "dayOfWeek": 5,
              "id": "valMsg_4",
              "info": {
                "maxDuration": 6,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift5",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftDurationExceedsMax",
              "dayOfWeek": 6,
              "id": "valMsg_5",
              "info": {
                "maxDuration": 6,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift6",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ScheduledOvertimeHours",
              "id": "valMsg_6",
              "info": {
                "personId": "person1",
              },
              "severity": 10,
            },
          ],
        }
      `);
    });
  });

  describe('ShiftDurationLessThanMin', () => {
    it('should return warning when shift duration is less than min', () => {
      const request = createValidationReq();
      request.minShiftDuration = 10;
      const result = validateSchedule(request);
      expect(result).toMatchInlineSnapshot(`
        {
          "draftScheduleId": "schedule1",
          "isValid": false,
          "messages": [
            {
              "code": "ShiftDurationLessThanMin",
              "dayOfWeek": 1,
              "id": "valMsg_0",
              "info": {
                "minDuration": 10,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift1",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftDurationLessThanMin",
              "dayOfWeek": 2,
              "id": "valMsg_1",
              "info": {
                "minDuration": 10,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift2",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftDurationLessThanMin",
              "dayOfWeek": 3,
              "id": "valMsg_2",
              "info": {
                "minDuration": 10,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift3",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftDurationLessThanMin",
              "dayOfWeek": 4,
              "id": "valMsg_3",
              "info": {
                "minDuration": 10,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift4",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftDurationLessThanMin",
              "dayOfWeek": 5,
              "id": "valMsg_4",
              "info": {
                "minDuration": 10,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift5",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftDurationLessThanMin",
              "dayOfWeek": 6,
              "id": "valMsg_5",
              "info": {
                "minDuration": 10,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift6",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ScheduledOvertimeHours",
              "id": "valMsg_6",
              "info": {
                "personId": "person1",
              },
              "severity": 10,
            },
          ],
        }
      `);
    });
  });

  describe('ShiftOutsideStoreHours', () => {
    it('should return error when shift is outside store hours', () => {
      const request = createValidationReq();
      request.schedule.days[0].areas[0].shifts[0].range = {start: '08:00', end: '18:00'};
      const result = validateSchedule(request);
      expect(result).toMatchInlineSnapshot(`
        {
          "draftScheduleId": "schedule1",
          "isValid": false,
          "messages": [
            {
              "code": "ShiftOutsideStoreHours",
              "dayOfWeek": 1,
              "id": "valMsg_0",
              "info": {
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift1",
                  "isShiftLead": true,
                  "range": {
                    "end": "18:00",
                    "start": "08:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftOutsideAvailability",
              "dayOfWeek": 1,
              "id": "valMsg_1",
              "info": {
                "personId": "person1",
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift1",
                  "isShiftLead": true,
                  "range": {
                    "end": "18:00",
                    "start": "08:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 20,
            },
            {
              "code": "ScheduledOvertimeHours",
              "id": "valMsg_2",
              "info": {
                "personId": "person1",
              },
              "severity": 10,
            },
          ],
        }
      `);
    });
  });

  describe('ShiftUnassigned', () => {
    it('should return warning when shift is unassigned', () => {
      const request = createValidationReq();
      request.schedule.days[0].areas[0].shifts[0].assignedPersonId = undefined;
      const result = validateSchedule(request);
      expect(result).toMatchInlineSnapshot(`
        {
          "draftScheduleId": "schedule1",
          "isValid": false,
          "messages": [
            {
              "code": "ShiftUnassigned",
              "dayOfWeek": 1,
              "id": "valMsg_0",
              "info": {
                "shift": {
                  "activities": [],
                  "assignedPersonId": undefined,
                  "id": "shift1",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 5,
            },
          ],
        }
      `);
    });
  });

  describe('ShiftOverlaps', () => {
    it('should return error when shifts overlap', () => {
      const request = createValidationReq();
      request.schedule.days[0].areas[0].shifts.push(constructShift({
        id: 'shift_2',
        shiftAreaId: 'area1',
        assignedPersonId: 'person1',
        range: {start: '12:00', end: '20:00'},
        isShiftLead: false
      }));
      const result = validateSchedule(request);
      expect(result).toMatchInlineSnapshot(`
        {
          "draftScheduleId": "schedule1",
          "isValid": false,
          "messages": [
            {
              "code": "ShiftOverlaps",
              "dayOfWeek": 1,
              "id": "valMsg_0",
              "info": {
                "personId": "person1",
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift1",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
                "shifts": [
                  {
                    "activities": [],
                    "assignedPersonId": "person1",
                    "id": "shift_2",
                    "isShiftLead": false,
                    "range": {
                      "end": "20:00",
                      "start": "12:00",
                    },
                    "shiftAreaId": "area1",
                  },
                ],
              },
              "severity": 20,
            },
            {
              "code": "ShiftOutsideStoreHours",
              "dayOfWeek": 1,
              "id": "valMsg_1",
              "info": {
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift_2",
                  "isShiftLead": false,
                  "range": {
                    "end": "20:00",
                    "start": "12:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftOutsideAvailability",
              "dayOfWeek": 1,
              "id": "valMsg_2",
              "info": {
                "personId": "person1",
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift_2",
                  "isShiftLead": false,
                  "range": {
                    "end": "20:00",
                    "start": "12:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 20,
            },
            {
              "code": "ExceedsMaxHoursPreferred",
              "id": "valMsg_3",
              "info": {
                "personId": "person1",
                "preferredHours": 50,
                "scheduledHours": 56,
              },
              "severity": 10,
            },
            {
              "code": "ScheduledOvertimeHours",
              "id": "valMsg_4",
              "info": {
                "personId": "person1",
              },
              "severity": 10,
            },
          ],
        }
      `);
    });
  });

  describe('ShiftOutsideAvailability', () => {
    it('should return error when shift is outside availability', () => {
      const request = createValidationReq();
      request.people[0].weekAvailability = availabilityRanges.parse([{dayOfWeek: 1, start: '10:00', end: '16:00'}]);
      const result = validateSchedule(request);
      expect(result).toMatchInlineSnapshot(`
        {
          "draftScheduleId": "schedule1",
          "isValid": false,
          "messages": [
            {
              "code": "ShiftOutsideAvailability",
              "dayOfWeek": 1,
              "id": "valMsg_0",
              "info": {
                "personId": "person1",
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift1",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 20,
            },
            {
              "code": "ShiftOutsideAvailability",
              "dayOfWeek": 2,
              "id": "valMsg_1",
              "info": {
                "personId": "person1",
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift2",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 20,
            },
            {
              "code": "ShiftOutsideAvailability",
              "dayOfWeek": 3,
              "id": "valMsg_2",
              "info": {
                "personId": "person1",
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift3",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 20,
            },
            {
              "code": "ShiftOutsideAvailability",
              "dayOfWeek": 4,
              "id": "valMsg_3",
              "info": {
                "personId": "person1",
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift4",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 20,
            },
            {
              "code": "ShiftOutsideAvailability",
              "dayOfWeek": 5,
              "id": "valMsg_4",
              "info": {
                "personId": "person1",
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift5",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 20,
            },
            {
              "code": "ShiftOutsideAvailability",
              "dayOfWeek": 6,
              "id": "valMsg_5",
              "info": {
                "personId": "person1",
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift6",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 20,
            },
            {
              "code": "ScheduledOvertimeHours",
              "id": "valMsg_6",
              "info": {
                "personId": "person1",
              },
              "severity": 10,
            },
          ],
        }
      `);
    });
  });

  describe('ShiftConflictsWithTimeOff', () => {
    it('should return error when shift conflicts with time off', () => {
      const request = createValidationReq();
      request.people[0].timeOff = [{
        requestId: 'timeOff1',
        status: 'Approved',
        start: new Date('2024-01-01T16:00:00Z'), // 10AM America/Denver time
        end: new Date('2024-01-01T20:00:00Z') // 2PM America/Denver time
      }];
      const result = validateSchedule(request);
      expect(result).toMatchInlineSnapshot(`
        {
          "draftScheduleId": "schedule1",
          "isValid": false,
          "messages": [
            {
              "code": "ShiftConflictsWithTimeOff",
              "dayOfWeek": 1,
              "id": "valMsg_0",
              "info": {
                "personId": "person1",
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift1",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 20,
            },
            {
              "code": "ScheduledOvertimeHours",
              "id": "valMsg_1",
              "info": {
                "personId": "person1",
              },
              "severity": 10,
            },
          ],
        }
      `);
    });
  });

  describe('ExceedsMaxHoursPreferred', () => {
    it('should return warning when person exceeds preferred hours', () => {
      const request = createValidationReq();
      request.people[0].maxHoursPreferred = 4;
      const result = validateSchedule(request);
      expect(result).toMatchInlineSnapshot(`
        {
          "draftScheduleId": "schedule1",
          "isValid": false,
          "messages": [
            {
              "code": "ExceedsMaxHoursPreferred",
              "id": "valMsg_0",
              "info": {
                "personId": "person1",
                "preferredHours": 4,
                "scheduledHours": 48,
              },
              "severity": 10,
            },
            {
              "code": "ScheduledOvertimeHours",
              "id": "valMsg_1",
              "info": {
                "personId": "person1",
              },
              "severity": 10,
            },
          ],
        }
      `);
    });
  });

  describe('Multiple validation messages', () => {
    it('should return multiple errors and warnings when applicable', () => {
      const request = createValidationReq();
      // Create an overlapping shift
      request.schedule.days[0].areas[0].shifts.push(constructShift({
        id: 'shift_2',
        shiftAreaId: 'area1',
        assignedPersonId: 'person1',
        range: {start: '12:00', end: '20:00'},
        isShiftLead: false
      }));
      // Make shift outside store hours
      request.schedule.days[0].areas[0].shifts[0].range = {start: '08:00', end: '18:00'};
      // Reduce max shift duration
      request.maxShiftDuration = 6;
      // Reduce preferred hours
      request.people[0].maxHoursPreferred = 4;

      const result = validateSchedule(request);

      expect(result).toMatchInlineSnapshot(`
        {
          "draftScheduleId": "schedule1",
          "isValid": false,
          "messages": [
            {
              "code": "ShiftDurationExceedsMax",
              "dayOfWeek": 1,
              "id": "valMsg_0",
              "info": {
                "maxDuration": 6,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift1",
                  "isShiftLead": true,
                  "range": {
                    "end": "18:00",
                    "start": "08:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftOutsideStoreHours",
              "dayOfWeek": 1,
              "id": "valMsg_1",
              "info": {
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift1",
                  "isShiftLead": true,
                  "range": {
                    "end": "18:00",
                    "start": "08:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftOverlaps",
              "dayOfWeek": 1,
              "id": "valMsg_2",
              "info": {
                "personId": "person1",
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift1",
                  "isShiftLead": true,
                  "range": {
                    "end": "18:00",
                    "start": "08:00",
                  },
                  "shiftAreaId": "area1",
                },
                "shifts": [
                  {
                    "activities": [],
                    "assignedPersonId": "person1",
                    "id": "shift_2",
                    "isShiftLead": false,
                    "range": {
                      "end": "20:00",
                      "start": "12:00",
                    },
                    "shiftAreaId": "area1",
                  },
                ],
              },
              "severity": 20,
            },
            {
              "code": "ShiftOutsideAvailability",
              "dayOfWeek": 1,
              "id": "valMsg_3",
              "info": {
                "personId": "person1",
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift1",
                  "isShiftLead": true,
                  "range": {
                    "end": "18:00",
                    "start": "08:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 20,
            },
            {
              "code": "ShiftDurationExceedsMax",
              "dayOfWeek": 1,
              "id": "valMsg_4",
              "info": {
                "maxDuration": 6,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift_2",
                  "isShiftLead": false,
                  "range": {
                    "end": "20:00",
                    "start": "12:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftOutsideStoreHours",
              "dayOfWeek": 1,
              "id": "valMsg_5",
              "info": {
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift_2",
                  "isShiftLead": false,
                  "range": {
                    "end": "20:00",
                    "start": "12:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftOutsideAvailability",
              "dayOfWeek": 1,
              "id": "valMsg_6",
              "info": {
                "personId": "person1",
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift_2",
                  "isShiftLead": false,
                  "range": {
                    "end": "20:00",
                    "start": "12:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 20,
            },
            {
              "code": "ShiftDurationExceedsMax",
              "dayOfWeek": 2,
              "id": "valMsg_7",
              "info": {
                "maxDuration": 6,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift2",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftDurationExceedsMax",
              "dayOfWeek": 3,
              "id": "valMsg_8",
              "info": {
                "maxDuration": 6,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift3",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftDurationExceedsMax",
              "dayOfWeek": 4,
              "id": "valMsg_9",
              "info": {
                "maxDuration": 6,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift4",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftDurationExceedsMax",
              "dayOfWeek": 5,
              "id": "valMsg_10",
              "info": {
                "maxDuration": 6,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift5",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ShiftDurationExceedsMax",
              "dayOfWeek": 6,
              "id": "valMsg_11",
              "info": {
                "maxDuration": 6,
                "shift": {
                  "activities": [],
                  "assignedPersonId": "person1",
                  "id": "shift6",
                  "isShiftLead": true,
                  "range": {
                    "end": "17:00",
                    "start": "09:00",
                  },
                  "shiftAreaId": "area1",
                },
              },
              "severity": 10,
            },
            {
              "code": "ExceedsMaxHoursPreferred",
              "id": "valMsg_12",
              "info": {
                "personId": "person1",
                "preferredHours": 4,
                "scheduledHours": 58,
              },
              "severity": 10,
            },
            {
              "code": "ScheduledOvertimeHours",
              "id": "valMsg_13",
              "info": {
                "personId": "person1",
              },
              "severity": 10,
            },
          ],
        }
      `);
    });
  });

  describe('Valid schedule', () => {
    it('should return no errors or warnings for a valid schedule', () => {
      const request = createValidationReq();
      const sched = mapShifts(request.schedule, s => updateShiftRange(s, {start: "09:00", end: "13:00"}));
      const result = validateSchedule({
        ...request,
        schedule: sched
      });
      expect(result).toMatchInlineSnapshot(`
        {
          "draftScheduleId": "schedule1",
          "isValid": true,
          "messages": [],
        }
      `);
    });
  });
});

describe('hasOverlappingShifts', () => {
  const createScheduleDay = (shifts: Shift[]): ScheduleDay => ({
    dayOfWeek: 1,
    areas: [{id: 'area1', title: 'Area 1', countsTowardsLabor: true, shifts}]
  });

  it('should return empty array when no overlapping shifts', () => {
    const shifts: Shift[] = map([
      {
        id: 'shift1',
        shiftAreaId: 'area1',
        assignedPersonId: 'person1',
        isShiftLead: false,
        range: {start: '09:00', end: '12:00'}
      },
      {
        id: 'shift2',
        shiftAreaId: 'area1',
        assignedPersonId: 'person1',
        isShiftLead: false,
        range: {start: '13:00', end: '17:00'}
      }
    ], s => constructShift(s));
    const day = createScheduleDay(shifts);
    const result = hasOverlappingShifts(shifts[0], day, 'person1');
    expect(result).toHaveLength(0);
  });

  it('should return overlapping shifts', () => {
    const shifts: Shift[] = map([
      {
        id: 'shift1',
        shiftAreaId: 'area1',
        assignedPersonId: 'person1',
        isShiftLead: false,
        range: {start: '09:00', end: '13:00'}
      },
      {
        id: 'shift2',
        shiftAreaId: 'area1',
        assignedPersonId: 'person1',
        isShiftLead: false,
        range: {start: '12:00', end: '17:00'}
      }
    ], s => constructShift(s));
    const day = createScheduleDay(shifts);
    const result = hasOverlappingShifts(shifts[0], day, 'person1');
    expect(result).toHaveLength(1);
    expect(result[0]).toEqual(shifts[1]);
  });

  it('should not return shifts for different persons', () => {
    const shifts: Shift[] = map([
      {
        id: 'shift1',
        shiftAreaId: 'area1',
        assignedPersonId: 'person1',
        isShiftLead: false,
        range: {start: '09:00', end: '13:00'}
      },
      {
        id: 'shift2',
        shiftAreaId: 'area1',
        assignedPersonId: 'person2',
        isShiftLead: false,
        range: {start: '12:00', end: '17:00'}
      }
    ], s => constructShift(s));
    const day = createScheduleDay(shifts);
    const result = hasOverlappingShifts(shifts[0], day, 'person1');
    expect(result).toHaveLength(0);
  });
});

describe('isWithinAvailability', () => {
  it('should return true when shift is within availability', () => {
    const shift = {start: '10:00', end: '14:00'};
    const dayOfWeek = 1;
    const availabilityRanges: WeekDayTimeRange[] = [
      {dayOfWeek: 1, start: '09:00', end: '17:00'}
    ];
    const result = isWithinAvailability(shift, dayOfWeek, availabilityRanges);
    expect(result).toBe(true);
  });

  it('should return false when shift is outside availability', () => {
    const shift = {start: '08:00', end: '12:00'};
    const dayOfWeek = 1;
    const availabilityRanges: WeekDayTimeRange[] = [
      {dayOfWeek: 1, start: '09:00', end: '17:00'}
    ];
    const result = isWithinAvailability(shift, dayOfWeek, availabilityRanges);
    expect(result).toBe(false);
  });

  it('should return false when availability is for a different day', () => {
    const shift = {start: '10:00', end: '14:00'};
    const dayOfWeek = 1;
    const availabilityRanges: WeekDayTimeRange[] = [
      {dayOfWeek: 2, start: '09:00', end: '17:00'}
    ];
    const result = isWithinAvailability(shift, dayOfWeek, availabilityRanges);
    expect(result).toBe(false);
  });

  it('should return true when shift matches contiguous availability ranges', () => {
    const shift = {start: '10:00', end: '14:00'};
    const dayOfWeek = 1;
    const ranges = availabilityRanges.parse([
      {dayOfWeek: 1, start: '09:00', end: '12:00'},
      {dayOfWeek: 1, start: '12:00', end: '17:00'}
    ]);
    const result = isWithinAvailability(shift, dayOfWeek, ranges);
    expect(result).toBe(true);
  });
});

describe('isWithinTimeOff', () => {
  it('should return true when shift overlaps with time off', () => {
    const shiftRange = {start: new Date('2024-01-01T10:00:00Z'), end: new Date('2024-01-01T18:00:00Z')};
    const timeOffRanges: DateTimeRange[] = [
      {start: new Date('2024-01-01T12:00:00Z'), end: new Date('2024-01-01T14:00:00Z')}
    ];
    const result = isWithinTimeOff(shiftRange, timeOffRanges);
    expect(result).toBe(true);
  });

  it('should return false when shift does not overlap with time off', () => {
    const shiftStart = new Date('2024-01-01T10:00:00Z');
    const shiftEnd = new Date('2024-01-01T12:00:00Z');
    const shiftRange = {start: shiftStart, end: shiftEnd};
    const timeOffRanges: DateTimeRange[] = [
      {start: new Date('2024-01-01T14:00:00Z'), end: new Date('2024-01-01T16:00:00Z')}
    ];
    const result = isWithinTimeOff(shiftRange, timeOffRanges);
    expect(result).toBe(false);
  });

  it('should return true when shift starts during time off', () => {
    const shiftStart = new Date('2024-01-01T13:00:00Z');
    const shiftEnd = new Date('2024-01-01T17:00:00Z');
    const shiftRange = {start: shiftStart, end: shiftEnd};
    const timeOffRanges: DateTimeRange[] = [
      {start: new Date('2024-01-01T12:00:00Z'), end: new Date('2024-01-01T14:00:00Z')}
    ];
    const result = isWithinTimeOff(shiftRange, timeOffRanges);
    expect(result).toBe(true);
  });

  it('should return true when shift ends during time off', () => {
    const shiftStart = new Date('2024-01-01T10:00:00Z');
    const shiftEnd = new Date('2024-01-01T13:00:00Z');
    const shiftRange = {start: shiftStart, end: shiftEnd};
    const timeOffRanges: DateTimeRange[] = [
      {start: new Date('2024-01-01T12:00:00Z'), end: new Date('2024-01-01T14:00:00Z')}
    ];
    const result = isWithinTimeOff(shiftRange, timeOffRanges);
    expect(result).toBe(true);
  });

  it('should return true when time off is entirely within shift', () => {
    const shiftStart = new Date('2024-01-01T09:00:00Z');
    const shiftEnd = new Date('2024-01-01T17:00:00Z');
    const shiftRange = {start: shiftStart, end: shiftEnd};
    const timeOffRanges: DateTimeRange[] = [
      {start: new Date('2024-01-01T12:00:00Z'), end: new Date('2024-01-01T14:00:00Z')}
    ];
    const result = isWithinTimeOff(shiftRange, timeOffRanges);
    expect(result).toBe(true);
  });
});

describe('validateHawaiiACAEligibility', () => {
  const mockPerson = {
    id: 'person1',
    maxHoursPreferred: 40,
    weekAvailability: availabilityRanges.parse([]),
    timeOff: [],
    storeIds: ['store1'],
    status: 'Active' as const,
    training: [],
    age: 25,
    pendingTimeOff: []
  };

  let idCounter = 0;
  const genId = () => `id-${++idCounter}`;

  it('should detect ACA eligibility when person works 20+ hours for 4 consecutive weeks', () => {
    const currentWeek = createScheduleWithHours(2024, 10, 'person1', 25);
    const weeksHoursMap: HawaiiACAPersonHours = {
      'person1': {
        precedingWeeks: [22, 24, 21], // Hours for weeks 7, 8, 9
        followingWeeks: [15, 10, 5]   // Hours for weeks 11, 12, 13
      }
    };

    const result = validateHawaiiACAEligibility(
      mockPerson,
      currentWeek,
      weeksHoursMap,
      genId,
      'HI' // Hawaii state for this test
    );

    expect(result).toHaveLength(1);
    expect(result[0].code).toBe('HawaiiACAEligible');
    if (result[0].code === 'HawaiiACAEligible') {
      expect(result[0].info.personId).toBe('person1');
      expect(result[0].info.consecutiveWeeksStart).toBe(7);
      expect(result[0].info.consecutiveWeeksYear).toBe(2024);
      expect(result[0].info.weeklyHours).toEqual([22, 24, 21, 25]);
    }
  });

  it('should not detect ACA eligibility when no 4-week span has all weeks >= 20 hours', () => {
    const currentWeek = createScheduleWithHours(2024, 10, 'person1', 15); // Less than 20 hours
    const weeksHoursMap: HawaiiACAPersonHours = {
      'person1': {
        precedingWeeks: [22, 15, 21], // Hours for weeks 7, 8, 9 (week 8 < 20 hours)
        followingWeeks: [15, 24, 22]  // Hours for weeks 11, 12, 13 (week 11 < 20 hours)
      }
    };

    const result = validateHawaiiACAEligibility(
      mockPerson,
      currentWeek,
      weeksHoursMap,
      genId,
      'HI' // Hawaii state for this test
    );

    expect(result).toHaveLength(0);
  });

  it('should detect ACA eligibility in the later 4-week span when first span does not qualify', () => {
    const currentWeek = createScheduleWithHours(2024, 10, 'person1', 25);
    const weeksHoursMap: HawaiiACAPersonHours = {
      'person1': {
        precedingWeeks: [15, 22, 21], // Hours for weeks 7, 8, 9 (week 7 < 20 hours)
        followingWeeks: [23, 10, 5]   // Hours for weeks 11, 12, 13
      }
    };

    const result = validateHawaiiACAEligibility(
      mockPerson,
      currentWeek,
      weeksHoursMap,
      genId,
      'HI' // Hawaii state for this test
    );

    expect(result).toHaveLength(1);
    expect(result[0].code).toBe('HawaiiACAEligible');
    if (result[0].code === 'HawaiiACAEligible') {
      expect(result[0].info.consecutiveWeeksStart).toBe(8);
      expect(result[0].info.weeklyHours).toEqual([22, 21, 25, 23]);
    }
  });

  it('should not validate ACA eligibility for non-Hawaii stores', () => {
    const currentWeek = createScheduleWithHours(2024, 10, 'person1', 25);
    const weeksHoursMap: HawaiiACAPersonHours = {
      'person1': {
        precedingWeeks: [22, 24, 21], // Hours for weeks 7, 8, 9
        followingWeeks: [23, 20, 25]  // Hours for weeks 11, 12, 13
      }
    };

    // Test with California (non-Hawaii state)
    const result = validateHawaiiACAEligibility(
      mockPerson,
      currentWeek,
      weeksHoursMap,
      genId,
      'CA' // California state
    );

    // Should return no messages for non-Hawaii states, even with qualifying hours
    expect(result).toHaveLength(0);
  });

  it('should return empty array when person is not found in weeksHoursMap', () => {
    const currentWeek = createScheduleWithHours(2024, 10, 'person1', 25);
    const weeksHoursMap: HawaiiACAPersonHours = {
      'person2': { // Different person ID - person1 is not in the map
        precedingWeeks: [22, 24, 21], // Hours for weeks 7, 8, 9
        followingWeeks: [23, 20, 25]  // Hours for weeks 11, 12, 13
      }
    };

    const result = validateHawaiiACAEligibility(
      mockPerson, // mockPerson.id is 'person1'
      currentWeek,
      weeksHoursMap, // but weeksHoursMap only has 'person2'
      genId,
      'HI' // Hawaii state
    );

    // Should return no messages when person has no hours recorded
    expect(result).toHaveLength(0);
  });

  it('should return empty array when weeksHoursMap is completely empty', () => {
    const currentWeek = createScheduleWithHours(2024, 10, 'person1', 25);
    const weeksHoursMap: HawaiiACAPersonHours = {}; // Empty map

    const result = validateHawaiiACAEligibility(
      mockPerson, // mockPerson.id is 'person1'
      currentWeek,
      weeksHoursMap, // Empty map
      genId,
      'HI' // Hawaii state
    );

    // Should return no messages when no one has hours recorded
    expect(result).toHaveLength(0);
  });

  // Note: Array length validation tests removed because TypeScript now enforces
  // exactly 3 elements at compile time with HawaiiACAWeekSchedules type
});

describe('Integration: Hawaii ACA validation with location awareness', () => {
  it('should include Hawaii ACA validation when storeState is HI', () => {
    const request = createTestValidationReq();
    // Override to Hawaii state
    request.storeState = 'HI';

    // Create a person with qualifying hours across multiple weeks
    request.people[0].id = 'person1';

    // Mock multi-week hours with qualifying hours
    request.weeksHoursMap = {
      'person1': {
        precedingWeeks: [22, 24, 21], // Hours for weeks 7, 8, 9
        followingWeeks: [23, 0, 0]    // Hours for weeks 11, 12, 13 (only week 11 has hours)
      }
    };

    // Update the current schedule to have qualifying hours
    const currentSchedule = createScheduleWithHours(2024, 10, 'person1', 25);
    request.schedule = currentSchedule;

    const result = validateSchedule(request);

    // Should find Hawaii ACA validation message
    const hawaiiMessages = result.messages.filter(m => m.code === 'HawaiiACAEligible');
    expect(hawaiiMessages).toHaveLength(1);
  });

  it('should not include Hawaii ACA validation when storeState is not HI', () => {
    const request = createTestValidationReq();
    // Keep default CA state
    expect(request.storeState).toBe('CA');

    // Create same qualifying conditions as above
    request.people[0].id = 'person1';
    request.weeksHoursMap = {
      'person1': {
        precedingWeeks: [22, 24, 21], // Hours for weeks 7, 8, 9
        followingWeeks: [23, 0, 0]    // Hours for weeks 11, 12, 13 (only week 11 has hours)
      }
    };
    const currentSchedule = createScheduleWithHours(2024, 10, 'person1', 25);
    request.schedule = currentSchedule;

    const result = validateSchedule(request);

    // Should NOT find Hawaii ACA validation message for non-Hawaii states
    const hawaiiMessages = result.messages.filter(m => m.code === 'HawaiiACAEligible');
    expect(hawaiiMessages).toHaveLength(0);
  });

  it('should work with backwards compatibility when storeState defaults to Unknown', () => {
    // Create a basic validation request that would trigger Hawaii ACA if it were in Hawaii
    const request = createTestValidationReq();
    // The default storeState should be 'CA' from createTestValidationReq
    expect(request.storeState).toBe('CA');

    // Set up qualifying conditions
    request.people[0].id = 'person1';
    request.weeksHoursMap = {
      'person1': {
        precedingWeeks: [22, 24, 21], // Hours for weeks 7, 8, 9
        followingWeeks: [23, 0, 0]    // Hours for weeks 11, 12, 13 (only week 11 has hours)
      }
    };
    const currentSchedule = createScheduleWithHours(2024, 10, 'person1', 25);
    request.schedule = currentSchedule;

    // Validate that non-Hawaii state doesn't trigger Hawaii ACA
    const result = validateSchedule(request);
    const hawaiiMessages = result.messages.filter(m => m.code === 'HawaiiACAEligible');
    expect(hawaiiMessages).toHaveLength(0);
  });

  it('should default storeState to Unknown when using utility function without storeState', () => {
    // Test the utility function without storeState parameter (backwards compatibility)
    const validationRequest = scheduleAndPeopleToValidationRequest({
      schedule: createTestSched(),
      people: [{
        id: "person1",
        firstName: "Test",
        lastName: "Person",
        profileImageUrl: undefined,
        jobTitle: "Employee",
        proficiencyRanking: undefined,
        availability: [],
        timeOff: [],
        pendingTimeOff: [],
        maxHoursPreferred: 50,
        age: 18,
        training: [],
        storeIds: ["store1"],
        status: "Active",
        payRate: null,
        weekAvailability: availabilityRanges.parse([]),
        weekPreferences: preferenceRanges.parse([])
      }],
      timezone: "America/Denver",
      storeAreas: [],
      settings: { disabledScheduleValidations: [] }
      // Note: storeState not provided - should default to 'Unknown'
    });

    expect(validationRequest.storeState).toBe('Unknown');

    // Validate that Unknown state doesn't trigger Hawaii ACA
    const result = validateSchedule(validationRequest);
    const hawaiiMessages = result.messages.filter(m => m.code === 'HawaiiACAEligible');
    expect(hawaiiMessages).toHaveLength(0);
  });
});

