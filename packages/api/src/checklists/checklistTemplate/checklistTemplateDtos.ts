import {z} from "zod";
import {ItemInstruction, ItemRequirement, NonBooleanRequirement} from "../checklistItem/checklistItemTypes";
import {FileAttachmentDto, fileAttachmentDto, toFileAttachmentDto} from "../../fileAttachment.dto";
import {GetImageUrl} from "../../types";
import {compact, find, flatMap, map} from "lodash";
import {ChecklistTemplate, ChecklistTemplateItem} from "./checklistTemplateTypes";
import {assertUnreachable} from "../../util";
import {FileAttachmentWithIncludes} from "../../fileAttachment.schemas";

export const requirementId = z.string();

export const baseRequirementDto = z.object({
  id: requirementId,
  isRequired: z.boolean(),
});

export const imageRequirementDto = baseRequirementDto.extend({
  type: z.literal("addImage"),
});
export type ImageRequirementDto = z.infer<typeof imageRequirementDto>;

export const commentRequirementDto = baseRequirementDto.extend({
  type: z.literal("writeComment"),
});
export type CommentRequirementDto = z.infer<typeof commentRequirementDto>;

export const numberTypeSchema = z.enum(["number", "currency", "percentage", "temperature"]);
export type NumberTypeSchema = z.infer<typeof numberTypeSchema>;

export const numberRequirementDto = baseRequirementDto.extend({
  type: z.literal("inputNumber"),
  numberType: numberTypeSchema,
});
export type NumberRequirementDto = z.infer<typeof numberRequirementDto>;

const requirementConditionalDto = z.object({
  if: z.boolean(),
  thenText: z.string(),
  isNonCompliant: z.boolean().optional().default(false),
  thenRequirements: z.array(z.discriminatedUnion("type",[
    imageRequirementDto,
    commentRequirementDto,
    numberRequirementDto,
  ])),
});
export type RequirementConditionalDto = z.infer<typeof requirementConditionalDto>;

export const booleanRequirementDto = baseRequirementDto.extend({
  type: z.literal("inputBoolean"),
  conditionals: z.array(requirementConditionalDto),
});
export type BooleanRequirementDto = z.infer<typeof booleanRequirementDto>;

export const itemRequirementDto = z.discriminatedUnion("type", [
  imageRequirementDto,
  commentRequirementDto,
  numberRequirementDto,
  booleanRequirementDto,
]);
export type ItemRequirementDto = z.infer<typeof itemRequirementDto>;

export const itemInstructionDto = z.object({
  id: z.string(),
  text: z.string().optional(),
  attachment: fileAttachmentDto.optional(),
});
export type ItemInstructionDto = z.infer<typeof itemInstructionDto>;

export const checklistTemplateItemDto = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().optional(),
  attachments: z.array(fileAttachmentDto),
  requirements: z.array(itemRequirementDto),
  instructions: z.array(itemInstructionDto).nullish()
});

export type ChecklistTemplateItemDto = z.infer<typeof checklistTemplateItemDto>;

export const checklistTemplateDto = z.object({
  id: z.string(),
  storeId: z.string(),
  title: z.string(),
  description: z.string().optional(),
  items: z.array(checklistTemplateItemDto),
  tags: z.array(z.string()),
});

export type ChecklistTemplateDto = z.infer<typeof checklistTemplateDto>;

export const anonymousChecklistTemplateDto = checklistTemplateDto.omit({storeId: true});
export type AnonymousChecklistTemplateDto = z.infer<typeof anonymousChecklistTemplateDto>;

export type ContextForChecklistTemplateDtos = {
  getImageUrl: GetImageUrl;
  files: FileAttachmentWithIncludes[];
};

export const checklistItemInstructionUploadDto = z.object({
  id: z.string(),
  attachment: fileAttachmentDto.optional(),
  text: z.string().optional(),
})

export type ChecklistItemInstructionUploadDto = z.infer<typeof checklistItemInstructionUploadDto>;

export function toItemRequirementDto(requirement: ItemRequirement): ItemRequirementDto {
  if (requirement.type === "inputBoolean") {
    return {
      id: requirement.id,
      isRequired: requirement.isRequired,
      type: "inputBoolean",
      conditionals: map(requirement.conditionals, conditional => ({
        if: conditional.if,
        thenText: conditional.thenText,
        isNonCompliant: conditional.isNonCompliant ?? false,
        thenRequirements: map(conditional.thenRequirements, req => toNonBooleanRequirementDto(req)),
      })),
    };
  } else {
    return toNonBooleanRequirementDto(requirement);
  }
}

function toNonBooleanRequirementDto(requirement: NonBooleanRequirement): ImageRequirementDto | CommentRequirementDto | NumberRequirementDto {
  if (requirement.type === "addImage") {
    return {
      id: requirement.id,
      isRequired: requirement.isRequired,
      type: "addImage"
    };
  } else if (requirement.type === "writeComment") {
    return {
      id: requirement.id,
      isRequired: requirement.isRequired,
      type: "writeComment"
    };
  } else if (requirement.type === "inputNumber") {
    return {
      id: requirement.id,
      isRequired: requirement.isRequired,
      type: "inputNumber",
      numberType: requirement.numberType,
    };
  }

  assertUnreachable(requirement);
}

export function getFileAttachmentDto(ctx: ContextForChecklistTemplateDtos, attachmentId: string | undefined): FileAttachmentDto | undefined {
  if (!attachmentId) {
    return undefined;
  }

  const file = find(ctx.files, f => f.id === attachmentId);
  if (file) {
    return toFileAttachmentDto(ctx, file);
  }
}

export function toItemInstructionDto(ctx: ContextForChecklistTemplateDtos, instruction: ItemInstruction): ItemInstructionDto {
  return {
    id: instruction.id,
    text: instruction.text,
    attachment: getFileAttachmentDto(ctx, instruction.attachment),
  };
}

export function toChecklistTemplateItemDto(ctx: ContextForChecklistTemplateDtos, item: ChecklistTemplateItem): ChecklistTemplateItemDto {
  return {
    id: item.id,
    title: item.title,
    description: item.description ?? undefined,
    attachments: compact(map(item.attachments, fileId => {
      return getFileAttachmentDto(ctx, fileId);
    })),
    requirements: map(item.requirements, requirement => toItemRequirementDto(requirement)),
    instructions: map(item.instructions, instruction => toItemInstructionDto(ctx, instruction)),
  };
}

export function toChecklistTemplateDto(ctx: ContextForChecklistTemplateDtos, template: ChecklistTemplate): ChecklistTemplateDto {
  return {
    id: template.id,
    storeId: template.storeId,
    title: template.title,
    description: template.description ?? undefined,
    items: map(template.items, item => toChecklistTemplateItemDto(ctx, item)),
    tags: template.tags,
  }
}

export function toAnonymousChecklistTemplateDto(ctx: ContextForChecklistTemplateDtos, template: ChecklistTemplate): AnonymousChecklistTemplateDto {
  return {
    id: template.id,
    title: template.title,
    description: template.description ?? undefined,
    items: map(template.items, item => toChecklistTemplateItemDto(ctx, item)),
    tags: template.tags,
  }
}

export function getChecklistTemplateItemFileAttachmentIds(item: ChecklistTemplateItem): string[] {
  return [// Get file IDs from item attachments
    ...item.attachments,
    // Get file IDs from item instructions that have attachments
    ...compact(flatMap(item.instructions, instruction => instruction.attachment))
  ]
}

export function getChecklistTemplateFileAttachmentIds(template: ChecklistTemplate): string[] {
  return flatMap(template.items, getChecklistTemplateItemFileAttachmentIds)
}
