import * as Prisma from "@prisma/client";
import {PrismaClient} from "@prisma/client";
import {ChecklistTemplate, ChecklistTemplateId, ChecklistTemplateItem} from "./checklistTemplateTypes";
import {DomainEventId} from "../../eventSourcing/domainEvent";
import {EnforceSecureId, SecureBusinessId, SecureStoreId} from "../../database.types";
import SuperJSON from "superjson";
import {FileAttachmentId} from "../checklistItem/checklistItemTypes";
import {constructChecklistTemplate} from "./checklistTemplate";
import {find, isEmpty, map} from "lodash";
import {FileAttachmentWithIncludes} from "../../fileAttachment.schemas";
import {FileAttachmentDto} from "../../fileAttachment.dto";
import {getFileAttachmentS3Key} from "../../fileAttachment.util";
import {diffItems} from "../../nestedUpdates";
import {getChecklistTemplateFileAttachmentIds} from "./checklistTemplateDtos";
import * as Sentry from "@sentry/node";

function toChecklistTemplateUpdateInput(checklistTemplate: ChecklistTemplate) {
  return {
    id: checklistTemplate.id,
    storeId: checklistTemplate.storeId,
    title: checklistTemplate.title,
    description: checklistTemplate.description ?? null,
    tags: checklistTemplate.tags,
    items: SuperJSON.serialize(checklistTemplate.items) as any,
    isArchived: checklistTemplate.isArchived,
  }
}

export function hydrateChecklistTemplate(row: Prisma.VChecklistTemplate): ChecklistTemplate {
  const items = SuperJSON.deserialize(row.items as any) as ChecklistTemplateItem[];
  return constructChecklistTemplate({
    id: row.id,
    storeId: row.storeId as SecureStoreId,
    title: row.title,
    description: row.description ?? undefined,
    tags: row.tags ?? [],
    items: items,
    isArchived: row.isArchived,
  })
}

export const checklistTemplateDb = (prisma: PrismaClient) => {
  const dbImpl = {
    findChecklistTemplateFileAttachments(
      storeId: SecureStoreId,
      checklistTemplateId: ChecklistTemplateId,
    ) {
      return prisma.fileAttachment.findMany({
        where: {
          vChecklistTemplateId: checklistTemplateId,
          file: {
            storeId: storeId
          },
        },
        include: {
          file: true
        }
      });
    },

    attachFileToChecklistTemplate(
      storeId: SecureStoreId,
      {
        checklistTemplateId,
        attachment,
        businessId,
      }: {
        checklistTemplateId: ChecklistTemplateId;
        attachment: FileAttachmentDto;
        businessId: SecureBusinessId;
      },
    ) {
      const s3ObjectKey = getFileAttachmentS3Key({
        businessId: businessId,
        fileId: attachment.fileId,
        storeId: storeId,
        mediaType: attachment.mediaType,
        folderName: "checklist"
      });

      return prisma.fileAttachment.create({
        data: {
          id: attachment.id,
          vChecklistTemplate: {
            connect: {
              id: checklistTemplateId,
            },
          },
          file: {
            create: {
              id: attachment.fileId,
              businessId: businessId,
              storeId: storeId,
              filename: attachment.filename,
              mimeType: attachment.mimeType,
              s3ObjectKey: s3ObjectKey,
              width: attachment.width,
              height: attachment.height,
              mediaType: attachment.mediaType,
            },
          },
        },
      });
    },

    disconnectManyFileAttachments(
      storeId: SecureStoreId,
      fileAttachmentIds: string[],
    ) {
      return prisma.fileAttachment.updateMany({
        where: {
          id: {
            in: fileAttachmentIds,
          },
          file: {
            storeId: storeId
          },
        },
        data: {
          vChecklistTemplateId: null,
        },
      });
    },

    /**
     * Conditionally update a person time off request. If the version of the event is not the expected version, then the update will be ignored.
     * Note that this does not throw an error if the version is not the expected version. It will just silently ignore the update.
     * This is useful for optimistic concurrency control.
     * Why not throw an error? Because the source of the truth is the event store. This is a projection. The projection
     * can be updated in the background. If there is a race conditions between two projection updates, then another update
     * may have increased the version number here. The version number only ever increases. So a version conflict would mean that
     * newer data has already been written. In this case, we want to silently ignore the update because there is no point in trying
     * to write the old data.
     */
    async conditionallyUpdateChecklistTemplate(businessId: SecureBusinessId, storeId: SecureStoreId, {
      checklistTemplate,
      newVersion,
      newAttachments
    }: {
      checklistTemplate: ChecklistTemplate,
      newAttachments: FileAttachmentDto[],
      newVersion: DomainEventId
    }): Promise<void> {
      await prisma.vChecklistTemplate.updateMany({
        where: {
          storeId: storeId,
          id: checklistTemplate.id,
          version: {lt: newVersion}
        },
        data: {
          ...toChecklistTemplateUpdateInput(checklistTemplate),
          version: newVersion
        }
      });

      // create or delete attachment as needed
      const oldAttachments = await dbImpl.findChecklistTemplateFileAttachments(storeId, checklistTemplate.id);
      const oldAttachmentIds = map(oldAttachments, a => a.id);
      const existingOrNewAttachmentIds = getChecklistTemplateFileAttachmentIds(checklistTemplate);
      const attachmentDiff = diffItems({
        oldItems: oldAttachmentIds,
        newItems: existingOrNewAttachmentIds,
        getId: id => id
      })

      for (const attachmentId of attachmentDiff.toCreate) {
        const newAttachment = find(newAttachments, a => a.id === attachmentId);
        if (newAttachment) {
          await dbImpl.attachFileToChecklistTemplate(storeId, {
            checklistTemplateId: checklistTemplate.id,
            businessId,
            attachment: newAttachment
          });
        } else {
          Sentry.captureMessage("Could not find new FileAttachmentDto to attach to checklist template. This is not something that should be happening right now.", {
            extra: {
              attachmentId,
              checklistTemplateId: checklistTemplate.id,
              storeId: checklistTemplate.storeId,
            }
          });
        }
      }

      if (!isEmpty(attachmentDiff.toDelete)) {
        await dbImpl.disconnectManyFileAttachments(storeId, attachmentDiff.toDelete)
      }
    },

    async createChecklistTemplate(storeId: SecureStoreId, businessId: SecureBusinessId, params: {
      checklistTemplate: ChecklistTemplate;
      initVersion: DomainEventId;
      newAttachments: FileAttachmentDto[];
    }) {
      const newTemplate = await prisma.vChecklistTemplate.create({
        data: {
          ...toChecklistTemplateUpdateInput(params.checklistTemplate),
          storeId: storeId,
          version: params.initVersion
        }
      });


      // If we are creating attachments along with this checklist template (e.g. through copying it from another store)
      if (!isEmpty(params.newAttachments)) {
        for (const newAttachment of params.newAttachments) {
          const attachmentId = newAttachment.id;
          if (newAttachment) {
            await dbImpl.attachFileToChecklistTemplate(storeId, {
              checklistTemplateId: newTemplate.id,
              businessId,
              attachment: newAttachment
            });
          } else {
            Sentry.captureMessage("Could not find new FileAttachmentDto to attach to checklist template. This is not something that should be happening right now.", {
              extra: {
                attachmentId,
                checklistTemplateId: newTemplate.id,
                storeId: newTemplate.storeId,
              }
            });
          }
        }
      } else {
        // if backfilling a checklist template, it may have attachments. Connect/copy those existing attachments to the new template
        const attachmentIds = getChecklistTemplateFileAttachmentIds(params.checklistTemplate);
        if (isEmpty(attachmentIds)) {
          return; // No attachments to process
        }

        await prisma.fileAttachment.updateMany({
          where: {
            id: {
              in: attachmentIds
            },
            file: {
              storeId: storeId
            }
          },
          data: {
            vChecklistTemplateId: newTemplate.id
          }
        })
      }
    },

    async doesChecklistTemplateExist(storeId: SecureStoreId, checklistTemplateId: ChecklistTemplateId): Promise<boolean> {
      const checklistTemplate = await prisma.vChecklistTemplate.findFirst({
        where: {
          id: checklistTemplateId,
          storeId: storeId,
        },
      });

      return Boolean(checklistTemplate);
    },

    async getChecklistTemplate(storeId: SecureStoreId, checklistTemplateId: ChecklistTemplateId): Promise<ChecklistTemplate> {
      const row = await prisma.vChecklistTemplate.findUniqueOrThrow({
        where: {
          id: checklistTemplateId,
          storeId: storeId,
        },
      });

      return hydrateChecklistTemplate(row);
    },

    async getChecklistTemplateInsecure(businessId: SecureBusinessId, checklistTemplateId: ChecklistTemplateId): Promise<ChecklistTemplate> {
      const row = await prisma.vChecklistTemplate.findUniqueOrThrow({
        where: {
          id: checklistTemplateId,
        },
      });

      return hydrateChecklistTemplate(row);
    },

    async getFileAttachments(storeId: SecureStoreId, fileAttachmentIds: FileAttachmentId[]): Promise<FileAttachmentWithIncludes[]> {
      return prisma.fileAttachment.findMany({
        where: {
          id: {
            in: fileAttachmentIds
          },
          file: {
            storeId: storeId
          }
        },
        include: {
          file: true
        }
      });
    },

    async getActiveChecklistTemplates(storeId: SecureStoreId): Promise<ChecklistTemplate[]> {
      const rows = await prisma.vChecklistTemplate.findMany({
        where: {
          storeId: storeId,
          isArchived: false,
        }
      });

      return map(rows, row => hydrateChecklistTemplate(row));
    },

    async getChecklistTemplatesAcrossAllBusinesses(_businessId: SecureBusinessId, {
      filter,
      pageIndex,
      pageSize,
      tags,
      omitStoreId,
      includePaginationMetadata = true,
    }: {
      filter?: string;
      pageIndex: number;
      pageSize: number;
      tags?: string[];
      omitStoreId?: string;
      includePaginationMetadata?: boolean;
    }): Promise<ChecklistTemplate[] | {
      items: ChecklistTemplate[];
      totalCount: number;
      pageCount: number;
    }> {
      const where = {
        ...(omitStoreId ? { storeId: { not: omitStoreId } } : {}),
        isArchived: false,
        ...(filter && {
          OR: [
            { title: { contains: filter, mode: "insensitive" as const } },
            { description: { contains: filter, mode: "insensitive" as const } },
          ]
        }),
        ...(tags && tags.length > 0 && {
          tags: {
            hasEvery: tags
          }
        })
      };

      // Get paginated results
      const rows = await prisma.vChecklistTemplate.findMany({
        where,
        take: pageSize,
        skip: pageIndex * pageSize,
        orderBy: { title: "asc" }
      });

      const items = map(rows, row => hydrateChecklistTemplate(row));

      if (!includePaginationMetadata) {
        return items;
      }

      // Get total count for pagination metadata
      const totalCount = await prisma.vChecklistTemplate.count({ where });
      const pageCount = Math.ceil(totalCount / pageSize);

      return {
        items,
        totalCount,
        pageCount,
      };
    }
  }

  return dbImpl as EnforceSecureId<typeof dbImpl>;
}
