import {ChecklistTemplateItem} from "../checklistTemplate/checklistTemplateTypes";

export interface BaseItemRequirement {
  id: string;
  isRequired: boolean;
}

export type FileAttachmentId = string;

export interface ImageRequirement extends BaseItemRequirement {
  type: "addImage";
}

export interface CommentRequirement extends BaseItemRequirement {
  type: "writeComment";
}

export type NumberInputType = "number" | "currency" | "percentage" | "temperature";

export interface NumberRequirement extends BaseItemRequirement {
  type: "inputNumber";
  numberType: NumberInputType;
}

export interface RequirementConditional {
  if: boolean;
  thenText: string;
  isNonCompliant: boolean | undefined;
  thenRequirements: NonBooleanRequirement[];
}

export interface BooleanRequirement extends BaseItemRequirement {
  type: "inputBoolean";
  conditionals: RequirementConditional[];
}

export type NonBooleanRequirement =
  | ImageRequirement
  | CommentRequirement
  | NumberRequirement;

export type ItemRequirement =
  | NonBooleanRequirement
  | BooleanRequirement;

export interface ItemInstruction {
  id: string;
  text: string | undefined;
  attachment: FileAttachmentId | undefined;
}

export type ChecklistItemId = string;

export interface ChecklistItem extends ChecklistTemplateItem {
  // TODO overrides the checklist's assignment rule for this specific item
  // assignmentRule?: AssignmentRule;
}
