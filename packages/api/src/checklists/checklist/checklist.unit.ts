import {describe, expect, it} from 'vitest';
import {areChecklistItemRequirementsComplete, getRequiredRequirements} from './checklist';
import {RequirementCompletionsDto} from './checklistDto';
import {ChecklistItem} from '../checklistItem/checklistItemTypes';

describe('areChecklistItemRequirementsComplete', () => {
  const createChecklistItem = (requirements: any[]): ChecklistItem => ({
    id: 'item-1',
    title: 'Test Item',
    description: 'Test Description',
    attachments: [],
    requirements,
    instructions: []
  });

  describe('addImage requirement type', () => {
    it('should validate a completed image requirement', () => {
      const requirements = [{
        id: 'req-1',
        type: 'addImage',
        isRequired: true
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'addImage',
          requirementId: 'req-1',
          attachment: {
            id: 'attachment-1',
            fileId: 'file-1',
            mediaType: 'image'
          }
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(true);
      expect(result.rejectionReasons).toHaveLength(0);
    });

    it('should reject when image attachment is missing', () => {
      const requirements = [{
        id: 'req-1',
        type: 'addImage',
        isRequired: true
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'addImage',
          requirementId: 'req-1',
          attachment: undefined as any
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Missing or invalid image attachment');
    });

    it('should reject when attachment is not an image', () => {
      const requirements = [{
        id: 'req-1',
        type: 'addImage',
        isRequired: true
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'addImage',
          requirementId: 'req-1',
          attachment: {
            id: 'attachment-1',
            fileId: 'file-1',
            mediaType: 'document'
          }
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Missing or invalid image attachment');
    });

    it('should reject when completion type does not match requirement type', () => {
      const requirements = [{
        id: 'req-1',
        type: 'addImage',
        isRequired: true
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'writeComment', // Wrong type
          requirementId: 'req-1',
          text: 'Some comment'
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Please select an image');
    });
  });

  describe('writeComment requirement type', () => {
    it('should validate a completed comment requirement', () => {
      const requirements = [{
        id: 'req-1',
        type: 'writeComment',
        isRequired: true
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'writeComment',
          requirementId: 'req-1',
          text: 'This is a valid comment'
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(true);
      expect(result.rejectionReasons).toHaveLength(0);
    });

    it('should reject when comment text is empty', () => {
      const requirements = [{
        id: 'req-1',
        type: 'writeComment',
        isRequired: true
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'writeComment',
          requirementId: 'req-1',
          text: ''
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Missing or empty comment');
    });

    it('should reject when comment text is only whitespace', () => {
      const requirements = [{
        id: 'req-1',
        type: 'writeComment',
        isRequired: true
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'writeComment',
          requirementId: 'req-1',
          text: '   '
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Missing or empty comment');
    });

    it('should reject when completion type does not match requirement type', () => {
      const requirements = [{
        id: 'req-1',
        type: 'writeComment',
        isRequired: true
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'inputNumber', // Wrong type
          requirementId: 'req-1',
          numberInput: 42
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Please enter a comment');
    });
  });

  describe('inputNumber requirement type', () => {
    it('should validate a completed number requirement', () => {
      const requirements = [{
        id: 'req-1',
        type: 'inputNumber',
        isRequired: true,
        numberType: 'number'
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'inputNumber',
          requirementId: 'req-1',
          numberInput: 42
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(true);
      expect(result.rejectionReasons).toHaveLength(0);
    });

    it('should validate a completed number requirement with zero', () => {
      const requirements = [{
        id: 'req-1',
        type: 'inputNumber',
        isRequired: true,
        numberType: 'number'
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'inputNumber',
          requirementId: 'req-1',
          numberInput: 0
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(true);
      expect(result.rejectionReasons).toHaveLength(0);
    });

    it('should reject when number input is undefined', () => {
      const requirements = [{
        id: 'req-1',
        type: 'inputNumber',
        isRequired: true,
        numberType: 'number'
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'inputNumber',
          requirementId: 'req-1',
          numberInput: undefined as any
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Missing or invalid number input');
    });

    it('should reject when number input is not finite', () => {
      const requirements = [{
        id: 'req-1',
        type: 'inputNumber',
        isRequired: true,
        numberType: 'number'
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'inputNumber',
          requirementId: 'req-1',
          numberInput: NaN
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Missing or invalid number input');
    });

    it('should reject when completion type does not match requirement type', () => {
      const requirements = [{
        id: 'req-1',
        type: 'inputNumber',
        isRequired: true,
        numberType: 'number'
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'inputBoolean', // Wrong type
          requirementId: 'req-1',
          booleanInput: true
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Please enter a number');
    });
  });

  describe('inputBoolean requirement type', () => {
    it('should validate a completed boolean requirement with true', () => {
      const requirements = [{
        id: 'req-1',
        type: 'inputBoolean',
        isRequired: true,
        conditionals: []
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'inputBoolean',
          requirementId: 'req-1',
          booleanInput: true
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(true);
      expect(result.rejectionReasons).toHaveLength(0);
    });

    it('should validate a completed boolean requirement with false', () => {
      const requirements = [{
        id: 'req-1',
        type: 'inputBoolean',
        isRequired: true,
        conditionals: []
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'inputBoolean',
          requirementId: 'req-1',
          booleanInput: false
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(true);
      expect(result.rejectionReasons).toHaveLength(0);
    });

    it('should reject when boolean input is undefined', () => {
      const requirements = [{
        id: 'req-1',
        type: 'inputBoolean',
        isRequired: true,
        conditionals: []
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'inputBoolean',
          requirementId: 'req-1',
          booleanInput: undefined as any
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Please select Yes or No');
    });

    it('should reject when completion type does not match requirement type', () => {
      const requirements = [{
        id: 'req-1',
        type: 'inputBoolean',
        isRequired: true,
        conditionals: []
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'addImage', // Wrong type
          requirementId: 'req-1',
          attachment: {
            id: 'attachment-1',
            fileId: 'file-1',
            mediaType: 'image'
          }
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Please select Yes or No');
    });
  });

  describe('unsupported requirement type', () => {
    it('should reject unsupported requirement types', () => {
      const requirements = [{
        id: 'req-1',
        type: 'unsupportedType', // Unsupported type
        isRequired: true
      }];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'unsupportedType' as any,
          requirementId: 'req-1'
        } as any
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Unsupported requirement type');
    });
  });

  describe('missing completions', () => {
    it('should reject when completion is missing for a requirement', () => {
      const requirements = [{
        id: 'req-1',
        type: 'writeComment',
        isRequired: true
      }];

      const completions: RequirementCompletionsDto = {}; // No completions

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Please enter a comment');
    });
  });

  describe('multiple requirements', () => {
    it('should validate when all requirements are complete', () => {
      const requirements = [
        {
          id: 'req-1',
          type: 'writeComment',
          isRequired: true
        },
        {
          id: 'req-2',
          type: 'inputNumber',
          isRequired: true,
          numberType: 'number'
        },
        {
          id: 'req-3',
          type: 'inputBoolean',
          isRequired: true,
          conditionals: []
        }
      ];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'writeComment',
          requirementId: 'req-1',
          text: 'Valid comment'
        },
        'req-2': {
          type: 'inputNumber',
          requirementId: 'req-2',
          numberInput: 42
        },
        'req-3': {
          type: 'inputBoolean',
          requirementId: 'req-3',
          booleanInput: true
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(true);
      expect(result.rejectionReasons).toHaveLength(0);
    });

    it('should reject when any requirement is incomplete', () => {
      const requirements = [
        {
          id: 'req-1',
          type: 'writeComment',
          isRequired: true
        },
        {
          id: 'req-2',
          type: 'inputNumber',
          isRequired: true,
          numberType: 'number'
        },
        {
          id: 'req-3',
          type: 'inputBoolean',
          isRequired: true,
          conditionals: []
        }
      ];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'writeComment',
          requirementId: 'req-1',
          text: 'Valid comment'
        },
        'req-2': {
          type: 'inputNumber',
          requirementId: 'req-2',
          numberInput: undefined as any // Invalid
        },
        'req-3': {
          type: 'inputBoolean',
          requirementId: 'req-3',
          booleanInput: true
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Missing or invalid number input');
    });

    it('should report all incomplete requirements', () => {
      const requirements = [
        {
          id: 'req-1',
          type: 'writeComment',
          isRequired: true
        },
        {
          id: 'req-2',
          type: 'inputNumber',
          isRequired: true,
          numberType: 'number'
        },
        {
          id: 'req-3',
          type: 'inputBoolean',
          isRequired: true,
          conditionals: []
        }
      ];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'writeComment',
          requirementId: 'req-1',
          text: '' // Invalid
        },
        'req-2': {
          type: 'inputNumber',
          requirementId: 'req-2',
          numberInput: NaN // Invalid
        },
        'req-3': {
          type: 'inputBoolean',
          requirementId: 'req-3',
          booleanInput: undefined as any // Invalid
        }
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(3);
    });
  });

  describe('non-required requirements', () => {
    it('should ignore non-required requirements', () => {
      const requirements = [
        {
          id: 'req-1',
          type: 'writeComment',
          isRequired: false // Not required
        }
      ];

      const completions: RequirementCompletionsDto = {}; // No completions

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(true);
      expect(result.rejectionReasons).toHaveLength(0);
    });

    it('should validate mix of required and non-required requirements', () => {
      const requirements = [
        {
          id: 'req-1',
          type: 'writeComment',
          isRequired: true // Required
        },
        {
          id: 'req-2',
          type: 'inputNumber',
          isRequired: false, // Not required
          numberType: 'number'
        }
      ];

      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'writeComment',
          requirementId: 'req-1',
          text: 'Valid comment'
        }
        // No completion for req-2, but that's ok since it's not required
      };

      const item = createChecklistItem(requirements);
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});

      expect(result.isComplete).toBe(true);
      expect(result.rejectionReasons).toHaveLength(0);
    });
  });

  describe('conditional requirements', () => {
    it('should validate conditional requirements when "Yes" is selected', () => {
      // Create a boolean requirement with conditional requirements
      const requirements = [
        {
          id: 'req-1',
          type: 'inputBoolean' as const,
          isRequired: true,
          conditionals: [
            {
              if: true, // If "Yes" is selected
              isNonCompliant: false,
              thenText: 'Please provide details',
              thenRequirements: [
                {
                  id: 'req-2',
                  type: 'writeComment' as const,
                  isRequired: true
                }
              ]
            }
          ]
        }
      ];

      // User selected "Yes" and provided the required comment
      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'inputBoolean',
          requirementId: 'req-1',
          booleanInput: true // "Yes"
        },
        'req-2': {
          type: 'writeComment',
          requirementId: 'req-2',
          text: 'Details provided'
        }
      };

      const item = createChecklistItem(requirements);

      // First test getRequiredRequirements function
      const requiredReqs = getRequiredRequirements(requirements, completions);
      expect(requiredReqs).toHaveLength(2); // Both the boolean and the conditional comment are required

      // Then test the validation function
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});
      expect(result.isComplete).toBe(true);
      expect(result.rejectionReasons).toHaveLength(0);
    });

    it('should validate conditional requirements when "No" is selected', () => {
      // Create a boolean requirement with conditional requirements
      const requirements = [
        {
          id: 'req-1',
          type: 'inputBoolean' as const,
          isRequired: true,
          conditionals: [
            {
              if: false, // If "No" is selected
              isNonCompliant: false,
              thenText: 'Please provide reason',
              thenRequirements: [
                {
                  id: 'req-2',
                  type: 'writeComment' as const,
                  isRequired: true
                }
              ]
            }
          ]
        }
      ];

      // User selected "No" and provided the required comment
      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'inputBoolean',
          requirementId: 'req-1',
          booleanInput: false // "No"
        },
        'req-2': {
          type: 'writeComment',
          requirementId: 'req-2',
          text: 'Reason provided'
        }
      };

      const item = createChecklistItem(requirements);

      // First test getRequiredRequirements function
      const requiredReqs = getRequiredRequirements(requirements, completions);
      expect(requiredReqs).toHaveLength(2); // Both the boolean and the conditional comment are required

      // Then test the validation function
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});
      expect(result.isComplete).toBe(true);
      expect(result.rejectionReasons).toHaveLength(0);
    });

    it('should reject when conditional requirement is not completed', () => {
      // Create a boolean requirement with conditional requirements
      const requirements = [
        {
          id: 'req-1',
          type: 'inputBoolean' as const,
          isRequired: true,
          conditionals: [
            {
              if: true, // If "Yes" is selected
              isNonCompliant: false,
              thenText: 'Please provide details',
              thenRequirements: [
                {
                  id: 'req-2',
                  type: 'writeComment' as const,
                  isRequired: true
                }
              ]
            }
          ]
        }
      ];

      // User selected "Yes" but did not provide the required comment
      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'inputBoolean',
          requirementId: 'req-1',
          booleanInput: true // "Yes"
        }
        // Missing req-2 completion
      };

      const item = createChecklistItem(requirements);

      // First test getRequiredRequirements function
      const requiredReqs = getRequiredRequirements(requirements, completions);
      expect(requiredReqs).toHaveLength(2); // Both the boolean and the conditional comment are required

      // Then test the validation function
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});
      expect(result.isComplete).toBe(false);
      expect(result.rejectionReasons).toHaveLength(1);
      expect(result.rejectionReasons[0].reason).toContain('Please enter a comment');
    });

    it('should not require conditional requirements for the unselected branch', () => {
      // Create a boolean requirement with conditional requirements for both Yes and No
      const requirements = [
        {
          id: 'req-1',
          type: 'inputBoolean' as const,
          isRequired: true,
          conditionals: [
            {
              if: true, // If "Yes" is selected
              isNonCompliant: false,
              thenText: 'Please provide details',
              thenRequirements: [
                {
                  id: 'req-2',
                  type: 'writeComment' as const,
                  isRequired: true
                }
              ]
            },
            {
              if: false, // If "No" is selected
              isNonCompliant: false,
              thenText: 'Please provide reason',
              thenRequirements: [
                {
                  id: 'req-3',
                  type: 'writeComment' as const,
                  isRequired: true
                }
              ]
            }
          ]
        }
      ];

      // User selected "Yes" and provided the required comment for the "Yes" branch
      const completions: RequirementCompletionsDto = {
        'req-1': {
          type: 'inputBoolean',
          requirementId: 'req-1',
          booleanInput: true // "Yes"
        },
        'req-2': {
          type: 'writeComment',
          requirementId: 'req-2',
          text: 'Details provided'
        }
        // No completion for req-3, but that's ok since "No" wasn't selected
      };

      const item = createChecklistItem(requirements);

      // First test getRequiredRequirements function
      const requiredReqs = getRequiredRequirements(requirements, completions);
      expect(requiredReqs).toHaveLength(2); // Only the boolean and the "Yes" branch comment are required

      // Then test the validation function
      const result = areChecklistItemRequirementsComplete({item, requirementCompletions: completions});
      expect(result.isComplete).toBe(true);
      expect(result.rejectionReasons).toHaveLength(0);
    });
  });
});
