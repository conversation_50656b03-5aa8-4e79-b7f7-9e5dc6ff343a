import {EnforceSecureId, SecureBusinessId, SecureChecklistId, SecureStoreId} from "../../database.types";
import {FileAttachmentDto} from "../../fileAttachment.dto";
import {getFileAttachmentS3Key} from "../../fileAttachment.util";
import {DomainEventId} from "../../eventSourcing/domainEvent";
import {compact, filter, find, isEmpty, isEqual, map, uniq} from "lodash";
import {diffItems} from "../../nestedUpdates";
import {FileAttachmentId} from "../checklistItem/checklistItemTypes";
import {FileAttachmentWithIncludes} from "../../fileAttachment.schemas";
import * as Prisma from "@prisma/client";
import {PrismaClient} from "@prisma/client";
import {
  Checklist,
  ChecklistBase,
  ChecklistId,
  ChecklistInstance,
  ChecklistSingle,
  ChecklistWithInteractions,
  ChecklistWithInteractionSummary
} from "./checklistTypes";
import {
  getCommonVEventData,
  getEventInstance,
  getEventsInRange,
  hydrateVEvent,
  VEventWithMeta
} from "../../calendar/vevent/veventRepository";
import * as Sentry from "@sentry/node";
import {getCustomData, getEventRange, getId, updateEventCustomData} from "../../calendar/vevent/vevent";
import {
  getAllItems,
  getChecklistCompletedAt,
  getChecklistFileAttachmentIds,
  getChecklistItemsFileAttachmentIds,
  getChecklistTiming,
  getItems,
  updateBaseItems
} from "./checklist";
import {DateTime} from "luxon";
import {getRecurrenceId, isVEventInstance} from "../../calendar/recurrenceSet/recurrenceSet";
import {getRecurrenceDateTime} from "../../calendar/recurrenceSet/recurrenceId";
import {
  constructChecklistItemInteraction,
  constructInteractions,
  getItemCompletedBy
} from "../checklistItem/checklistItemInteraction";
import {RecurrenceId} from "../../calendar/recurrenceSet/recurrenceId.types";
import {ChecklistItemInteractions} from "../checklistItem/checklistItemInteractionTypes";
import {genChecklistInteractionSummaryId} from "../../schemas";
import {getEventStore} from "../../eventSourcing/eventStore";
import {getEnd, getStart, isExplicitTiming} from "../checklistTiming/checklistTiming";
import {produce} from "immer";

function toChecklistUpdateInput(checklist: Checklist, recurrenceId: RecurrenceId | undefined, interactions: ChecklistItemInteractions | undefined) {
  const timing = getChecklistTiming(checklist);
  const start = getStart(timing);
  let end = getEnd(timing);

  // for non-explicit-timing, update the end date to when it got completed, so that they don't clutter up
  // queries. The non-explicit end date is never shown to the user anyway. We set to the end of the
  // day so that the checklist still shows up as completed until end of day.
  if (!isExplicitTiming(timing)) {
    const completedAt = interactions ? getChecklistCompletedAt(checklist, recurrenceId, interactions) : false;
    // if a non-explicit-timing checklist is UN-completed, then set the end to 10 years in future
    end = completedAt ? completedAt.toJSDate() : DateTime.fromJSDate(start).plus({years: 10}).toJSDate();
  }

  return {
    ...getCommonVEventData(checklist, (base, overrides) => {
      // if an override's items have not changed, then don't write the items to the DB. This is purely to save space, data traffic, memory.
      const baseItems = base.items;
      return map(overrides, override => {
        const overrideItems = getItems(override, undefined);
        if (isEqual(baseItems, overrideItems)) {
          return produce(override, draft => {
            draft.customData.items = undefined as any; // special value
          });
        }
        return override;
      })
    }),
    end
  }
}

/**
 * If a checklist override has "undefined" items in the database, that signifies that the override does not override the base items.
 * So then use the items from the base checklist
 * @param base
 * @param overrides
 */
export function hydrateChecklistOverrides(base: ChecklistBase, overrides: ChecklistInstance[]) {
  const baseItems = base.items;
  return map(overrides, override => {
    if (override.customData.items === undefined) {
      return updateEventCustomData(override, updateBaseItems(getCustomData(override), baseItems))
    }
    return override;
  });
}

function hydrateChecklist(row: Prisma.VEvent): Checklist {
  return hydrateVEvent<ChecklistBase>(row, hydrateChecklistOverrides);
}

function toChecklistInteractionSummaryUpdateInput({checklist, checklistVersion, recurrenceId, interactions, now}: {
  checklist: Checklist,
  checklistVersion: DomainEventId,
  interactions: ChecklistItemInteractions;
  recurrenceId: RecurrenceId | undefined;
  now: Date;
}) {
  const items = getItems(checklist, recurrenceId);
  const itemCompletedByIds = compact(map(items, i => getItemCompletedBy(interactions, i.id)));
  const isComplete = items.length === itemCompletedByIds.length;

  return {
    veventVersion: checklistVersion,
    isComplete: isComplete,
    itemCompletedCount: itemCompletedByIds.length,
    itemCount: items.length,
    completedByPersonIds: uniq(itemCompletedByIds),
    completedAt: isComplete ? now : null,
  }
}

export const checklistDb = (prisma: PrismaClient) => {
  const dbImpl = {
    findChecklistFileAttachments(
      storeId: SecureStoreId,
      checklistId: ChecklistId,
    ) {
      return prisma.fileAttachment.findMany({
        where: {
          vEventId: checklistId,
          file: {
            storeId: storeId
          },
        },
      });
    },

    createChecklistFileAttachment(
      storeId: SecureStoreId,
      {
        checklistId,
        attachment,
        businessId,
      }: {
        checklistId: ChecklistId;
        attachment: FileAttachmentDto;
        businessId: SecureBusinessId;
      },
    ) {
      const s3ObjectKey = getFileAttachmentS3Key({
        businessId: businessId,
        fileId: attachment.fileId,
        storeId: storeId,
        mediaType: attachment.mediaType,
        folderName: "checklist"
      });

      return prisma.fileAttachment.create({
        data: {
          id: attachment.id,
          vEvent: {
            connect: {
              id: checklistId,
            },
          },
          file: {
            create: {
              id: attachment.fileId,
              businessId: businessId,
              storeId: storeId,
              filename: attachment.filename,
              mimeType: attachment.mimeType,
              s3ObjectKey: s3ObjectKey,
              width: attachment.width,
              height: attachment.height,
              mediaType: attachment.mediaType,
            },
          },
        },
      });
    },

    disconnectManyFileAttachments(
      storeId: SecureStoreId,
      fileAttachmentIds: string[],
    ) {
      return prisma.fileAttachment.updateMany({
        where: {
          id: {
            in: fileAttachmentIds,
          },
          file: {
            storeId: storeId
          },
        },
        data: {
          vEventId: null,
        },
      });
    },

    /**
     * Conditionally update a checklist. If the version of the event is not the expected version, then the update will be ignored.
     * Note that this does not throw an error if the version is not the expected version. It will just silently ignore the update.
     * This is useful for optimistic concurrency control.
     * Why not throw an error? Because the source of the truth is the event store. This is a projection. The projection
     * can be updated in the background. If there is a race conditions between two projection updates, then another update
     * may have increased the version number here. The version number only ever increases. So a version conflict would mean that
     * newer data has already been written. In this case, we want to silently ignore the update because there is no point in trying
     * to write the old data.
     */
    async conditionallyUpdateChecklist(businessId: SecureBusinessId, storeId: SecureStoreId, {
      checklist,
      newVersion,
      newAttachments,
      recurrenceId,
      interactions
    }: {
      checklist: Checklist,
      newAttachments: FileAttachmentDto[],
      newVersion: DomainEventId;
      recurrenceId: RecurrenceId | undefined;
      interactions: ChecklistItemInteractions | undefined;
    }): Promise<{
      oldSummary: Prisma.VChecklistInteractionSummary;
      newSummary: Prisma.VChecklistInteractionSummary;
    } | undefined> {
      const data = toChecklistUpdateInput(checklist, recurrenceId, interactions);
      await prisma.vEvent.updateMany({
        where: {
          storeId: storeId,
          id: checklist.id,
          version: {lt: newVersion}
        },
        data: {
          ...data,
          version: newVersion,
        }
      });

      let oldSummary: Prisma.VChecklistInteractionSummary | undefined | null = undefined;
      let newSummary: Prisma.VChecklistInteractionSummary | undefined = undefined;
      if (interactions) {
        const recurrenceDt = recurrenceId
          ? getRecurrenceDateTime(recurrenceId).toJSDate()
          : null;
        oldSummary = await prisma.vChecklistInteractionSummary.findFirst({
          where: {
            storeId: storeId,
            veventId: checklist.id,
            recurrenceId: recurrenceDt,
          }
        });
        const summaryUpdateInput = toChecklistInteractionSummaryUpdateInput({
          checklist: checklist,
          checklistVersion: newVersion,
          interactions: interactions,
          now: new Date(),
          recurrenceId: recurrenceId
        });

        if (oldSummary) {
          newSummary = await prisma.vChecklistInteractionSummary.update({
            where: {
              id: oldSummary.id
            },
            data: {
              ...summaryUpdateInput
            }
          });
        } else {
          newSummary = await prisma.vChecklistInteractionSummary.create({
            data: {
              id: genChecklistInteractionSummaryId(),
              storeId: storeId,
              veventId: checklist.id,
              recurrenceId: recurrenceDt,
              ...summaryUpdateInput
            }
          })
        }
      }

      // create or delete attachment as needed
      const oldAttachments = await dbImpl.findChecklistFileAttachments(storeId, checklist.id);
      const oldAttachmentIds = map(oldAttachments, a => a.id);
      const existingOrNewAttachmentIds = getChecklistItemsFileAttachmentIds(getAllItems(checklist), interactions);
      const attachmentDiff = diffItems({
        oldItems: oldAttachmentIds,
        newItems: existingOrNewAttachmentIds,
        getId: id => id
      })

      for (const attachmentId of attachmentDiff.toCreate) {
        const newAttachment = find(newAttachments, a => a.id === attachmentId);
        if (newAttachment) {
          await dbImpl.createChecklistFileAttachment(storeId, {
            checklistId: checklist.id,
            businessId,
            attachment: newAttachment
          });
        } else {
          Sentry.captureMessage("Could not find new FileAttachmentDto to attach to checklist. This is not something that should be happening right now.", {
            extra: {
              attachmentId,
              checklistId: checklist.id,
              storeId: checklist.storeId,
            }
          });
        }
      }

      if (!isEmpty(attachmentDiff.toDelete)) {
        await dbImpl.disconnectManyFileAttachments(storeId, attachmentDiff.toDelete)
      }

      return oldSummary && newSummary ? {
        oldSummary: oldSummary,
        newSummary: newSummary,
      } : undefined;
    },

    async createChecklist(storeId: SecureStoreId, params: {
      checklist: Checklist;
      initVersion: DomainEventId;
    }) {
      const row = await prisma.vEvent.create({
        data: {
          ...toChecklistUpdateInput(params.checklist, undefined, undefined),
          id: getId(params.checklist),
          storeId: storeId,
          version: params.initVersion,
        }
      });

      // have to connect all the file attachments from the template too
      const fileAttachmentIds = getChecklistItemsFileAttachmentIds(getAllItems(params.checklist), undefined);
      await prisma.fileAttachment.updateMany({
        where: {
          id: {
            in: fileAttachmentIds
          },
          file: {
            storeId: storeId
          }
        },
        data: {
          vEventId: row.id
        }
      })
    },

    async doesChecklistExist(storeId: SecureStoreId, checklistId: ChecklistId): Promise<boolean> {
      const checklist = await prisma.vEvent.findFirst({
        where: {
          id: checklistId,
          storeId: storeId,
        },
      });

      return Boolean(checklist);
    },

    async getFileAttachments(storeId: SecureStoreId, fileAttachmentIds: FileAttachmentId[]): Promise<FileAttachmentWithIncludes[]> {
      return prisma.fileAttachment.findMany({
        where: {
          id: {
            in: fileAttachmentIds
          },
          file: {
            storeId: storeId
          }
        },
        include: {
          file: true
        }
      });
    },

    async populateChecklistInteractionSummary(storeId: SecureStoreId, checklists: Array<VEventWithMeta<ChecklistBase>>) {
      const getChecklistInstanceDateTime = (checklist: Checklist): DateTime | null => {
        return isVEventInstance(checklist) ? getRecurrenceDateTime(getRecurrenceId(checklist)) : null;
      }
      const instanceDts = compact(map(checklists, getChecklistInstanceDateTime));

      const interactionRows = await prisma.vChecklistInteractionSummary.findMany({
        where: {
          storeId: storeId,
          veventId: {in: map(checklists, e => e.id)},
          // we're looking for interaction rows that have either the recurrence IDs (for recurring checklists)
          // or no recurrence ID (for single checklists)
          OR: [
            ...!isEmpty(instanceDts) ? [{recurrenceId: {in: map(instanceDts, dt => dt.toJSDate())}}] : [],
            {recurrenceId: null}
          ]
        }
      });

      return map(checklists, (checklist): ChecklistWithInteractionSummary => {
        const recurrenceId = isVEventInstance(checklist) ? getRecurrenceId(checklist) : undefined;
        const interactionRow = find(interactionRows, r => r.veventId === checklist.id
          && (!r.recurrenceId || r.recurrenceId.getTime() === getChecklistInstanceDateTime(checklist)?.toMillis()));

        return {
          ...checklist,
          recurrenceId: recurrenceId,
          parentRRule: "parentRRule" in checklist ? checklist.parentRRule : undefined,
          isComplete: Boolean(interactionRow?.isComplete),
          completedAt: interactionRow?.completedAt ? DateTime.fromJSDate(interactionRow.completedAt) : undefined,
          completedByPersonIds: interactionRow?.completedByPersonIds ?? [],
          itemCompletedCount: interactionRow?.itemCompletedCount ?? 0,
          itemCount: interactionRow?.itemCount ?? 0,
        }
      })
    },

    async getChecklistOccurrencesInRange(storeId: SecureStoreId, {range}: {
      range: {
        start: DateTime;
        end: DateTime;
      }
    }): Promise<DateTime[]> {
      const checklists = await getEventsInRange<ChecklistBase>({
        prisma,
        storeId,
        range: range,
        customType: "checklist",
        hydrateOverrides: hydrateChecklistOverrides
      });

      return map(checklists, checklist => getEventRange(checklist).start);
    },

    async getChecklistsInRange(storeId: SecureStoreId, {checklistId, range}: {
      checklistId?: ChecklistId;
      range: {
        start: DateTime;
        end: DateTime;
      }
    }): Promise<ChecklistWithInteractionSummary[]> {
      const checklists = await getEventsInRange<ChecklistBase>({
        prisma,
        storeId,
        id: checklistId,
        range: range,
        customType: "checklist",
        hydrateOverrides: hydrateChecklistOverrides
      });

      return dbImpl.populateChecklistInteractionSummary(storeId, checklists);
    },

    /**
     * Get either a single checklist or an instance of a recurring checklist, depending on if you pass in recurrenceId
     * @param storeId
     * @param checklistId
     * @param recurrenceId
     */
    async getChecklist(storeId: SecureStoreId, {checklistId, recurrenceId}: {
      checklistId: ChecklistId;
      recurrenceId: RecurrenceId | undefined;
    }): Promise<VEventWithMeta<ChecklistBase>> {
      if (recurrenceId) {
        const checklist = await getEventInstance<ChecklistBase>({
          prisma,
          storeId,
          id: checklistId,
          recurrenceId,
          customType: "checklist",
          hydrateOverrides: hydrateChecklistOverrides
        });

        const checklistsWithInteractionSummary = await dbImpl.populateChecklistInteractionSummary(storeId, [checklist]);
        return checklistsWithInteractionSummary[0];
      } else {
        const row = await prisma.vEvent.findUniqueOrThrow({
          where: {
            storeId,
            id: checklistId
          },
        });

        const checklist = hydrateChecklist(row) as ChecklistSingle;
        return {
          ...checklist,
          version: row.version,
        }
      }
    },

    async getChecklistTemplateStore(businessId: SecureBusinessId, checklistTemplateId: string) {
      const row = await prisma.vChecklistTemplate.findUniqueOrThrow({
        where: {
          id: checklistTemplateId,
          store: {
            businessId: businessId
          }
        },
        select: {
          store: {
            select: {
              id: true,
              businessId: true,
            }
          }
        }
      });

      // can trust the storeId here because they come from the row scoped to a secure businessId
      return row.store.id as SecureStoreId;
    },

    async getChecklistStore(businessId: SecureBusinessId, checklistId: string) {
      const row = await prisma.vEvent.findUniqueOrThrow({
        where: {
          id: checklistId,
          store: {
            businessId: businessId
          }
        },
        select: {
          store: {
            select: {
              id: true,
              businessId: true,
            }
          }
        }
      });

      // can trust the storeId here because they come from the row scoped to a secure businessId
      return row.store.id as SecureStoreId;
    },

    async getChecklistBusinessAndStore(checklistId: SecureChecklistId) {
      const row = await prisma.vEvent.findUniqueOrThrow({
        where: {
          id: checklistId
        },
        select: {
          store: {
            select: {
              id: true,
              businessId: true,
            }
          }
        }
      });

      // can trust the storeId and businessId here because they come from the row that got loaded with a SecureChecklistId
      const storeId = row.store.id as SecureStoreId;
      const businessId = row.store.businessId as SecureBusinessId;

      return {
        storeId,
        businessId,
      }
    },

    async getChecklistWithInteractionsAndFiles(businessId: SecureBusinessId, storeId: SecureStoreId, params: {
      checklistId: ChecklistId;
      recurrenceId: RecurrenceId | undefined;
    }): Promise<ChecklistWithInteractions & { files: FileAttachmentWithIncludes[] }> {
      const checklist = await this.getChecklist(storeId, params);
      const checklistsWithInteractionSummary = await this.populateChecklistInteractionSummary(storeId, [checklist])

      // use the event store here to get the interaction events
      const eventStore = getEventStore({prisma, businessId});
      const events = await eventStore.queryEvents({
        streamQuery: {
          checklistId: params.checklistId,
          checklistRecurrenceId: params.recurrenceId ? getRecurrenceDateTime(params.recurrenceId).toJSDate() : undefined,
          checklistItemId: {not: null},
          eventType: {
            in: ["ChecklistItemCompletedEvent", "ChecklistItemUncompletedEvent"]
          }
        }
      })

      // This is just to make TS happy
      const interactionEvents = filter(events, e => e.type === "ChecklistItemCompletedEvent" || e.type === "ChecklistItemUncompletedEvent");
      const interactions = constructInteractions(map(interactionEvents, event => constructChecklistItemInteraction(event)));
      const fileAttachmentIds = getChecklistFileAttachmentIds(checklist, params.recurrenceId, interactions);
      const files = await this.getFileAttachments(storeId, fileAttachmentIds);

      return {
        ...checklistsWithInteractionSummary[0],
        interactions: interactions,
        files
      }
    },

    async getChecklistTags(storeId: SecureStoreId) {
      return prisma.checklistTags.findMany({
        where: {
          storeId: storeId
        }
      });
    },

    async addChecklistTag(storeId: SecureStoreId, title: string) {
      return prisma.checklistTags.create({
        data: {
          storeId: storeId,
          title: title
        }
      });
    },

    async removeChecklistTag(storeId: SecureStoreId, title: string) {
      return prisma.checklistTags.delete({
        where: {
          storeId_title: {
            storeId: storeId,
            title: title
          }
        }
      });
    },

    async getTagsAcrossAllBusinesses(_businessId: SecureBusinessId) {
      return prisma.checklistTags.findMany({
        select: {
          title: true
        },
        distinct: ["title"],
      });
    }
  }

  return dbImpl as EnforceSecureId<typeof dbImpl>;
}

