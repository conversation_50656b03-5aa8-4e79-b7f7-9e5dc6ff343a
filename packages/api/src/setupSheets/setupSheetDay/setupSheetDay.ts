import {
  CellAssignments,
  Minutes,
  Rotation,
  RotationId,
  SetupDay,
  TimeFrame,
  TimeFrameId,
  validSetupDay,
  ValidSetupDay
} from "./setupSheetDaySchemas";
import {Cells, Coordinates, ValidStoreLayout} from "../storeLayout/storeLayoutSchemas";
import * as LTF from "../timeFrame/layoutTimeFrame"
import * as TF from "./timeFrame";
import {constructFromLayoutTimeFrame, isTimeFrameIdEqual, updateAssignments, updateBreaks} from "./timeFrame";
import * as SL from "../storeLayout/storeLayout";
import {IsoWeekDateWithDay, TimeOfDay} from "../../timeSchemas";
import {compact, find, flatMap, map, maxBy} from "lodash";
import {assignPersonToCell, removePersonFromAssignment, removePersonFromAssignments} from "./cellAssignments";
import * as C from "../storeLayout/cells";
import {constructCells, getCell, isPositionCell} from "../storeLayout/cells";
import {endBreak, startBreak, updateBreak, updateSetupDayBreak} from "./breaks";
import * as R from "./rotation";
import {rotate} from "./rotation";
import {BreakUpdate, RotationUpdate} from "../setupSheetsDto";
import {coordinateMapEntries, filterCoordinateMap} from "../storeLayout/coordinateMap";
import {ValidStoreTimeFrames} from "../timeFrame/storeTimeFramesSchemas";
import {LayoutTimeFrames} from "../timeFrame/layoutTimeFrameSchemas";
import * as STF from "../timeFrame/storeTimeFrames";

/*********************************************************************************************
 * TODO THIS ENTIRE FILE OF CODE IS DEPRECATED. IT'S ONLY HERE FOR BACKWARDS COMPATIBILITY WITH THE OLD APP VERSION. REMOVE ONCE APP IS RELEASED.
 * ********************************************************************************************/

export function constructSetupDay(setupDay: SetupDay): ValidSetupDay {
  return validSetupDay.parse(setupDay);
}

export function constructFromStoreTimeFrames({storeTimeFrames, layouts, setupDayId, date}: {
  storeTimeFrames: ValidStoreTimeFrames,
  layouts: ValidStoreLayout[],
  setupDayId: string;
  date: IsoWeekDateWithDay;
}): ValidSetupDay {

  const storeId = STF.getStoreId(storeTimeFrames);
  const setupDay: SetupDay = {
    id: setupDayId,
    storeId: storeId,
    date: date,
    timeFrames: map(STF.getTimeFrames(storeTimeFrames), timeFrame => {
      return constructFromLayoutTimeFrame(layouts, timeFrame);
    }),
  }

  return validSetupDay.parse(setupDay);
}

export function findTimeFrame(setupDay: SetupDay, predicate: (tf: TimeFrame) => boolean): TimeFrame | undefined {
  return find(setupDay.timeFrames, predicate);
}

export function findTimeFrameById(setupDay: SetupDay, timeFrameId: TimeFrameId): TimeFrame | undefined {
  return findTimeFrame(setupDay, tf => isTimeFrameIdEqual(tf, timeFrameId));
}

export function getTimeFrames(setupDay: SetupDay): TimeFrame[] {
  return setupDay.timeFrames;
}

export function findLastTimeFrame(setupDay: SetupDay): TimeFrame | undefined {
  const tfs = getTimeFrames(setupDay);
  return maxBy(tfs, tf => tf.end);
}

export function findActiveTimeFrame(setupDay: SetupDay, currentTime: TimeOfDay): TimeFrame | undefined {
  return findTimeFrame(setupDay, tf => tf.start <= currentTime && tf.end >= currentTime);
}

export function getAssignmentsAtTimeFrame(setupDay: SetupDay, timeFrameId: TimeFrameId): CellAssignments | undefined {
  const timeFrame = findTimeFrameById(setupDay, timeFrameId);
  return timeFrame ? TF.getAssignments(timeFrame) : undefined;
}

export function updateTimeFrame(setupDay: SetupDay, timeFrameId: TimeFrameId, updateFn: (tf: TimeFrame) => TimeFrame): ValidSetupDay {
  const newDay = {
    ...setupDay,
    timeFrames: map(setupDay.timeFrames, tf => {
      if (isTimeFrameIdEqual(tf, timeFrameId)) return updateFn(tf);
      return tf;
    })
  }

  return validSetupDay.parse(newDay);
}

/**
 * Assigns a person to a position.
 * @param setupDay
 * @param cell The cell to assign the person to. Must be a setup position cell.
 * @param personId The person to assign
 * @param timeFrame The range that uniquely identifies the time frame
 * @param start The time to start the assignment. Should be now.
 */
export function assignPosition(setupDay: SetupDay, {cell, personId, timeFrameId, start}: {
  cell: Coordinates;
  personId: string;
  timeFrameId: TimeFrameId;
  start: TimeOfDay;
}): ValidSetupDay {
  return updateTimeFrame(setupDay, timeFrameId, timeFrame => {
    // check that the target cell is a setup position cell
    const targetCell = getCell(TF.getCells(timeFrame), cell);
    if (!targetCell || targetCell.cellType !== "SetupPosition") return timeFrame;

    let newTf = updateAssignments(timeFrame, assignments =>
      assignPersonToCell(assignments, {
        personId,
        cell,
        start
      }));

    // end any break the person may be on
    newTf = updateBreaks(newTf, breaks => endBreak(breaks, personId));

    return newTf;
  });
}

/**
 * Unassigns a person from a position they are assigned to.
 * @param setupDay
 * @param cell
 * @param timeFrameId
 * @param personId
 */
export function unassignPersonFromPosition(setupDay: SetupDay, {cell, timeFrameId, personId}: {
  cell: Coordinates;
  timeFrameId: TimeFrameId;
  personId: string;
}): ValidSetupDay {
  return updateTimeFrame(setupDay, timeFrameId, tf =>
    updateAssignments(tf, assignments =>
      removePersonFromAssignment(assignments, personId, cell)));
}

/**
 * Sends a person on break with optional break timer. Ends any assignment the person may be on.
 * @param setupDay
 * @param personId
 * @param timeFrameId
 * @param until
 */
export function sendPersonOnBreak(setupDay: SetupDay, {personId, timeFrameId, start, duration, fromActivityId}: {
  personId: string;
  timeFrameId: TimeFrameId;
  start: TimeOfDay;
  duration: Minutes | null;
  fromActivityId: string;
}): ValidSetupDay {
  return updateTimeFrame(setupDay, timeFrameId, tf => {
    // End any assignment the person may be on
    let newTf = updateAssignments(tf, assignments => removePersonFromAssignments(assignments, personId));

    // Send them on break
    return updateBreaks(newTf, breaks => startBreak(breaks, {
      personId,
      start,
      duration,
      fromActivityId,
    }));
  });
}

/**
 * Updates a person's break. E.g. updating the duration.
 * @param setupDay
 * @param personId
 * @param timeFrameId
 * @param updateFn
 */
export function updatePersonBreak(setupDay: SetupDay, {personId, timeFrameId, update}: {
  personId: string;
  timeFrameId: TimeFrameId;
  update: BreakUpdate;
}): ValidSetupDay {
  return updateTimeFrame(setupDay, timeFrameId, tf =>
    updateBreaks(tf, breaks =>
      updateBreak(breaks, personId, brk =>
        updateSetupDayBreak(brk, update))));
}

/**
 * Ends a person's break. Doesn't assign the person to a position.
 * @param setupDay
 * @param personId
 * @param timeFrameId
 */
export function endPersonBreak(setupDay: SetupDay, {personId, timeFrameId}: {
  personId: string;
  timeFrameId: TimeFrameId;
}): ValidSetupDay {
  return updateTimeFrame(setupDay, timeFrameId, tf =>
    updateBreaks(tf, breaks => endBreak(breaks, personId)));
}

/**
 * Adds a rotation to a time frame.
 * @param setupDay
 * @param rotation
 * @param timeFrameId
 */
export function addRotation(setupDay: SetupDay, {rotation, timeFrameId}: {
  rotation: Rotation;
  timeFrameId: TimeFrameId;
}): ValidSetupDay {
  return updateTimeFrame(setupDay, timeFrameId, tf => TF.addRotation(tf, rotation));
}

/**
 * Removes a rotation from a time frame.
 * @param setupDay
 * @param rotationId
 * @param timeFrameId
 */
export function removeRotation(setupDay: SetupDay, {rotationId, timeFrameId}: {
  rotationId: RotationId;
  timeFrameId: TimeFrameId;
}): ValidSetupDay {
  return updateTimeFrame(setupDay, timeFrameId, tf => TF.removeRotation(tf, rotationId));
}

/**
 * Updates a rotation in a time frame.
 * @param setupDay
 * @param update
 * @param rotationId
 * @param timeFrameId
 */
export function updateRotation(setupDay: SetupDay, {update, rotationId, timeFrameId}: {
  update: RotationUpdate;
  rotationId: RotationId;
  timeFrameId: TimeFrameId;
}): ValidSetupDay {
  return updateTimeFrame(setupDay, timeFrameId, tf =>
    TF.updateRotation(tf, rotationId, rotation =>
      R.updateRotation(rotation, update)));
}

/**
 * Executes a rotation. It will take each person in each cell in the rotation and shift them right in the rotation cells.
 * If nobody is assigned to a cell, then the right-shifted cell becomes empty.
 * @param setupDay
 * @param rotationId
 * @param timeFrameId
 * @param start The time to start the rotation. Should be now.
 */
export function executeRotation(setupDay: SetupDay, {rotationId, timeFrameId, start}: {
  rotationId: RotationId;
  timeFrameId: TimeFrameId;
  start: TimeOfDay;
}): ValidSetupDay {
  return updateTimeFrame(setupDay, timeFrameId, tf => rotate(tf, {rotationId, start}));
}

export function getIsoWeekDate(setupDay: SetupDay): IsoWeekDateWithDay {
  return setupDay.date;
}

export function getId(setupDay: SetupDay): string {
  return setupDay.id;
}

export function getStoreId(setupDay: SetupDay): string {
  return setupDay.storeId;
}

export interface PersonAssignment {
  cell: Coordinates;
  start: TimeOfDay;
  storePositionId: string | undefined;
  timeFrameTitle: string;
  setupPositionTitle: string;
}

export function getPersonAssignmentsInAllTimeFrames(setupDay: SetupDay, personId: string): PersonAssignment[] {
  const timeFrames = getTimeFrames(setupDay);
  return flatMap(timeFrames, timeFrame => {
    const assignmentsAtTimeFrame = timeFrame
      ? getAssignmentsAtTimeFrame(setupDay, TF.getId(timeFrame)) ?? {}
      : {};
    const setupDayAssignmentMap = filterCoordinateMap(assignmentsAtTimeFrame, a => a.personId === personId);
    const cells = TF.getCells(timeFrame);
    return compact(map(coordinateMapEntries(setupDayAssignmentMap), ({x, y, value}) => {
      const cell = C.getCell(cells, {x, y});
      if (cell && isPositionCell(cell)) {
        return {
          cell: {x, y},
          storePositionId: C.getCellStorePositionId(cell),
          start: value.start,
          timeFrameTitle: TF.getTitle(timeFrame),
          setupPositionTitle: C.getTitle(cell)
        }
      }
    }))
  })
}

export function updateTimeFrames(setupDay: SetupDay, {timeFrames, layouts}: {
  layouts: ValidStoreLayout[];
  timeFrames: LayoutTimeFrames
}): ValidSetupDay {
  const newDay: SetupDay = {
    ...setupDay,
    timeFrames: map(timeFrames, timeFrame => {
      // if there is a time frame with the same start and end time, keep the assignments, breaks, and rotations and replace the title, cells, and store layout
      // otherwise, create a new time frame
      const oldTimeFrame = findTimeFrameById(setupDay, TF.getId(timeFrame));
      if (oldTimeFrame) {
        const storeLayoutId = LTF.getStoreLayoutId(timeFrame);
        const layout = find(layouts, layout => SL.getId(layout) === storeLayoutId);

        let newTf = oldTimeFrame;
        newTf = TF.replaceTitle(newTf, TF.getTitle(timeFrame));
        newTf = TF.replaceCells(newTf, layout ? SL.getCells(layout) : constructCells());
        newTf = TF.replaceStoreLayoutId(newTf, storeLayoutId);
        return newTf;
      } else {
        return constructFromLayoutTimeFrame(layouts, timeFrame);
      }
    })
  }

  return validSetupDay.parse(newDay);
}

export function updateTimeFrameLayoutCells(setupDay: SetupDay, {timeFrameId, cells}: {
  timeFrameId: TimeFrameId;
  cells: Cells;
}): ValidSetupDay {
  return updateTimeFrame(setupDay, timeFrameId, tf => TF.replaceCells(tf, cells));
}

export function getWeekdayLabel(setupDay: SetupDay): string {
  const isoWeek = getIsoWeekDate(setupDay);
  if (isoWeek.day === 1) return "Monday";
  if (isoWeek.day === 2) return "Tuesday";
  if (isoWeek.day === 3) return "Wednesday";
  if (isoWeek.day === 4) return "Thursday";
  if (isoWeek.day === 5) return "Friday";
  if (isoWeek.day === 6) return "Saturday";
  if (isoWeek.day === 7) return "Sunday";
  return "Unknown";
}
