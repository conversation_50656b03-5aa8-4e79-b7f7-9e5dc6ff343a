import {
  SetupPositionTitle,
  storePositionCountsSchema,
  storePositionTitlesSchema,
  StoreSetupPositions,
  ValidStoreSetupPositions,
  validStoreSetupPositions
} from "./storeSetupPositionSchemas";
import {clamp, compact, keys, map, orderBy, reduce, sumBy, values} from "lodash";
import {produce} from "immer";
import {z} from "zod";
import {StoreAreaDto} from "../../schemas";

export function constructStoreSetupPositions(storeSetupPositions: StoreSetupPositions): ValidStoreSetupPositions {
  return validStoreSetupPositions.parse(storeSetupPositions);
}

function addPositionsInPlace({draft, baseTitle: _baseTitle, storePositionId, count}: {
  draft: StoreSetupPositions;
  baseTitle: string;
  storePositionId: string | undefined;
  count: number;
}) {
  const baseTitle = _baseTitle.trim() || "Unknown";
  for (let i = 0; i < count; i++) {
    let titleSuffix = i + 1;
    let title = baseTitle + " " + titleSuffix;
    let sanityLimit = 400;
    // if this title already exists, increment the title suffix until it is unique
    while (draft[title] && sanityLimit-- > 0) {
      titleSuffix++;
      title = baseTitle + " " + titleSuffix;
    }
    draft[title] = {
      storePositionId,
      order: titleSuffix
    }
  }
}

export function addPositions(storeSetupPositions: StoreSetupPositions, {baseTitle, storePositionId, count}: {
  baseTitle: string;
  storePositionId: string | undefined;
  count: number;
}): ValidStoreSetupPositions {
  const newPositions = produce(storeSetupPositions, draft => {
    addPositionsInPlace({draft, baseTitle, storePositionId, count});
  });

  return constructStoreSetupPositions(newPositions);
}

export function removePosition(storeSetupPositions: StoreSetupPositions, title: string): ValidStoreSetupPositions {
  const newPositions = produce(storeSetupPositions, draft => {
    delete draft[title];
  });

  return constructStoreSetupPositions(newPositions);
}

export function renamePosition(storeSetupPositions: StoreSetupPositions, oldTitle: string, newTitle: string): ValidStoreSetupPositions {
  const trimmedNewTitle = newTitle.trim();

  if (!trimmedNewTitle) {
    throw new Error("Position title cannot be empty");
  }

  if (trimmedNewTitle === oldTitle) {
    return constructStoreSetupPositions(storeSetupPositions);
  }

  if (storeSetupPositions[trimmedNewTitle]) {
    throw new Error("A position with this name already exists");
  }

  if (!storeSetupPositions[oldTitle]) {
    throw new Error("Position to rename does not exist");
  }

  const newPositions = produce(storeSetupPositions, draft => {
    // Copy the position data to the new title
    draft[trimmedNewTitle] = draft[oldTitle];
    // Remove the old title
    delete draft[oldTitle];
  });

  return constructStoreSetupPositions(newPositions);
}

export function updatePosition(storeSetupPositions: StoreSetupPositions, title: string, update: {
  start?: string;
  end?: string;
}): ValidStoreSetupPositions {
  const newPositions = produce(storeSetupPositions, draft => {
    const position = draft[title];
    if (!position) return;

    if (update.start !== undefined) {
      position.start = update.start;
    }
    if (update.end !== undefined) {
      position.end = update.end;
    }
  });

  return constructStoreSetupPositions(newPositions);
}

/**
 * Update the count of each store position that the setup positions should have. E.g. if the setup positions have
 * 3 FC Order Taker store positions, and the count is updated to 2, then 1 setup position will be removed.
 * If the count is updated to 4, then 1 setup position will be added. The new setup position title will have a
 * number suffix that is 1 greater than the last setup position with the same store position. For example,
 * if the last setup position with the same store position is "FC Order Taker 4", then the new setup position
 * will be "FC Order Taker 5".
 * @param storeSetupPositions
 * @param storePositionTitles A record of store position IDs to the title of the store position.
 * @param storePositionCounts A record of store position IDs to the count of setup positions that should exist for that store position.
 */
function updatePositionsImpl(storeSetupPositions: ValidStoreSetupPositions, storePositionTitles: Record<string, string>, storePositionCounts: Record<string, number>): ValidStoreSetupPositions {
  return produce(storeSetupPositions, draft => {
    for (const storePositionId of keys(storePositionCounts)) {
      // count the number of existing cells that use this store position
      const existingCount = sumBy(values(storeSetupPositions), pos => pos.storePositionId === storePositionId ? 1 : 0);
      const numToRemove = existingCount - storePositionCounts[storePositionId];
      const numToAdd = storePositionCounts[storePositionId] - existingCount;

      if (numToRemove > 0) {
        let numRemoved = 0;
        // remove cells in reverse insertion order
        for (const title of keys(draft).sort((a, b) => draft[b].order - draft[a].order)) {
          if (draft[title].storePositionId === storePositionId) {
            delete draft[title];
            numRemoved++;
            if (numRemoved >= numToRemove) {
              break;
            }
          }
        }
      }

      if (numToAdd > 0) {
        addPositionsInPlace({
          draft,
          baseTitle: storePositionTitles[storePositionId] ?? "Unknown",
          storePositionId,
          count: numToAdd
        });
      }
    }
  });
}

export const updatePositionCountsSchema = z.function({
  input: [validStoreSetupPositions, storePositionTitlesSchema, storePositionCountsSchema],
  output: validStoreSetupPositions
})
export const updatePositionCounts = updatePositionCountsSchema.implement(updatePositionsImpl);

/**
 * Converts a record of store position IDs to the count of setup positions that exist for that store position.
 * @param storeSetupPositions
 */
export function toStorePositionCounts(storeSetupPositions: StoreSetupPositions): Record<string, number> {
  return reduce(storeSetupPositions, (acc, pos) => {
    if (!pos.storePositionId) return acc;
    acc[pos.storePositionId] = clamp((acc[pos.storePositionId] ?? 0) + 1, 0, 100);
    return acc;
  }, {} as Record<string, number>);
}

export interface SetupPositionItem {
  title: string;
  storePositionId: string | undefined;
  areaId: string | undefined;
  areaTitle: string | undefined;
  order: number;
  start: string | undefined;
  end: string | undefined;
}

type PositionToAreaMap = Map<string, { areaId: string; areaTitle: string; areaOrder: number; positionOrder: number }>;

export function sortSetupPositionItems(setupPositions: SetupPositionItem[], positionToAreaMap: PositionToAreaMap): SetupPositionItem[] {
  // Sort by area order first, then by position order within each area, then by setup position order
  return orderBy(
    setupPositions,
    [
      // First sort key: items without area info go to the end (1), items with area info go first (0)
      (item) => {
        const areaInfo = item.storePositionId ? positionToAreaMap.get(item.storePositionId) : undefined;
        return areaInfo ? 0 : 1;
      },
      // Second sort key: area order (only relevant for items with area info)
      (item) => {
        const areaInfo = item.storePositionId ? positionToAreaMap.get(item.storePositionId) : undefined;
        return areaInfo?.areaOrder ?? 0;
      },
      // Third sort key: position order within the same area (only relevant for items with area info)
      (item) => {
        const areaInfo = item.storePositionId ? positionToAreaMap.get(item.storePositionId) : undefined;
        return areaInfo?.positionOrder ?? 0;
      },
      // Fourth sort key: setup position order (insertion order) - used as tiebreaker for all items
      (item) => item.order
    ],
    ['asc', 'asc', 'asc', 'asc']
  );
}

export function storeAreasToPositionAreaMap(storeAreas: StoreAreaDto[]): PositionToAreaMap {
  const positionToAreaMap = new Map<string, { areaId: string; areaTitle: string; areaOrder: number; positionOrder: number }>();

  storeAreas.forEach(area => {
    area.positions.forEach((position, posIdx) => {
      positionToAreaMap.set(position.id, {
        areaId: area.id,
        areaTitle: area.title,
        areaOrder: area.order,
        positionOrder: position.order ?? posIdx
      });
    });
  });

  return positionToAreaMap;
}

/**
 * Convert the setup positions to an array sorted by area and position order given by storeAreas
 * @param storeSetupPositions
 * @param storeAreas
 * @param filterToSetupPositions If provided, only include setup positions with these titles out of the total storeSetupPositions
 */
export function toArray({storeSetupPositions, storeAreas, filterToSetupPositions}: {
  storeSetupPositions: StoreSetupPositions,
  storeAreas: StoreAreaDto[];
  filterToSetupPositions?: SetupPositionTitle[];
}): Array<SetupPositionItem> {
  const positionToAreaMap = storeAreasToPositionAreaMap(storeAreas);

  // Convert setup positions to array with area and position information
  const items = compact(map(filterToSetupPositions ?? keys(storeSetupPositions), (title): SetupPositionItem | undefined => {
    const setupPosition = storeSetupPositions[title];
    if (!setupPosition) return; // could be undefined if their layout contains a position that they removed from their StoreSetupPositions
    const positionInfo = setupPosition.storePositionId ? positionToAreaMap.get(setupPosition.storePositionId) : undefined;

    return {
      title,
      storePositionId: setupPosition.storePositionId,
      areaId: positionInfo?.areaId,
      areaTitle: positionInfo?.areaTitle,
      order: setupPosition.order,
      start: setupPosition.start,
      end: setupPosition.end,
    };
  }));

  return sortSetupPositionItems(items, positionToAreaMap);
}

export function findPosition(storeSetupPositions: StoreSetupPositions, title: string, areaMap?: PositionToAreaMap): SetupPositionItem | undefined {
  const setupPosition = storeSetupPositions[title];
  if (!setupPosition) return;
  const area = setupPosition.storePositionId ? areaMap?.get(setupPosition.storePositionId) : undefined;

  return {
    title,
    storePositionId: setupPosition.storePositionId,
    areaId: area?.areaId,
    areaTitle: area?.areaTitle,
    order: setupPosition.order,
    start: setupPosition.start,
    end: setupPosition.end,
  };
}
