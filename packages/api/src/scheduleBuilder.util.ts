import {chain, clamp, filter, find, flatMap, isEmpty, isEqual, map, omit, sortBy, sumBy, times, trim} from "lodash";
import {ShiftWithIsoWeek} from "./scheduleNotifications.types";
import {DailyTimeRange, DateTimeRange, DayOfWeek, IsoWeekDate, IsoWeekDateWithDay} from "./timeSchemas";
import {
  compareTimeOfDay,
  dateTo24HourTimeInTimezone,
  dateTo24HrTime,
  doIncrementRangesOverlap,
  getDateFromWeekDayTime,
  getRangeDurationHours,
  IncrementTimeRange,
  minutesTo24HourTime,
  timeToMinutes,
  to12HourTime
} from "./date.util";
import {ScheduleEventDto} from "./scheduleEventSchemas";
import {
  BaseSchedule,
  DayPart,
  DraftSchedule,
  PublishedSchedule,
  Shift,
  ShiftActivityType,
  ShiftArea,
  ShiftOfferDto
} from "./scheduleSchemas";
import {SchedulePersonClientDto, SchedulePersonDto} from "./schedulePersonDto";
import {
  severityCritical,
  severityError,
  severityInfo,
  severityWarning,
  ValidationMessage
} from "./scheduleValidation.types";
import {DateTime} from "luxon";
import {formatInTimeZone, getTimezoneOffset} from "date-fns-tz";
import {UTCDate} from "@date-fns/utc";
import {getISODay, getISOWeek, parse} from "date-fns"
import {SuggestionReason} from "./shiftSuggestion.types";
import {getShift} from "./scheduling.util";
import {StorePositionDto} from "./schemas";
import {to24HourRange} from "./scheduleIncrement";
import {getShiftOpsHours} from "./shift";
import {
  closerOpenerHourThreshold,
  minorMaxHoursPerSchoolWeek,
  minorOvertimeHoursPerDayThreshold,
  minorOvertimeHoursPerWeekThreshold,
  minorSchoolDayMaxHoursPerDay,
  overtimeHoursPerShiftThreshold,
  overtimeHoursPerWeekThreshold
} from "./scheduleValidation";
import {getShiftWeekday} from "./schedule";
import {omitNullish} from "./omitNullish";

export type ShiftOrTimeOff = { type: "noShifts" } | ShiftWithIsoWeek | {
  type: "timeOffStart" | "timeOffEnd",
  time: string
};

/**
 * Get the rows that should display on a person's schedule for a day
 * @param isoWeek
 * @param shifts
 * @param timeOff
 * @param timezone
 */
export function getDayScheduleRows({isoWeek, shifts, timeOff, timezone}: {
  isoWeek: IsoWeekDateWithDay;
  // the shifts assigned to the person for this day
  shifts: ShiftWithIsoWeek[];
  // all the person's time off ranges (no need to filter for this day)
  timeOff: DateTimeRange[];
  timezone: string | null;
}) {
  const dayStart = getDateFromWeekDayTime({
    ...isoWeek,
    time: "00:00",
    timezone: timezone
  });
  const dayEnd = getDateFromWeekDayTime({
    ...isoWeek,
    time: "23:59",
    // all-day time off requests get created with 23:59:59.000, so we have to make sure to include seconds here.
    second: 59,
    millisecond: 999,
    timezone: timezone
  });

  const dayTimeOffStarts = chain(timeOff)
    .filter(to => to.start >= dayStart && to.start <= dayEnd)
    .map((to): ShiftOrTimeOff => ({
      time: dateTo24HourTimeInTimezone(to.start, timezone),
      type: "timeOffStart"
    })).value();
  const dayTimeOffEnds = chain(timeOff)
    .filter(to => to.end >= dayStart && to.end <= dayEnd)
    .map((to): ShiftOrTimeOff => ({
      time: dateTo24HourTimeInTimezone(to.end, timezone),
      type: "timeOffEnd"
    })).value()

  const unsortedItems: Array<ShiftOrTimeOff> = [
    ...isEmpty(shifts) ? [{type: "noShifts" as const}] : shifts,
    ...dayTimeOffStarts,
    ...dayTimeOffEnds
  ];

  return sortBy(unsortedItems, item => {
    if ("type" in item) {
      if (item.type === "timeOffEnd" || item.type === "timeOffStart") {
        return item.time;
      } else { // we want "no scheduled shifts" to show up first for the day if there are no shifts
        return -1;
      }
    } else {
      return item.range.start;
    }
  })
}

export type ScheduleShiftChangeStatus = "unchanged" | "created" | "updated" | "deleted";

export interface IncrementMetrics {
  numShifts: number;
  numAssignedShifts: number;
  numOpsShifts: number;
}

export interface ShiftActivity {
  id: string;
  activityType: ShiftActivityType;
  title: string;
  description: string | undefined;
  start: number;
  end: number;
  countsTowardsLabor: boolean;
  range: DailyTimeRange;
  setupPositionTitle: string | undefined;
  payStatus: "paid" | "unpaid" | undefined;
}

export interface ScheduleRowInfo {
  type: "shift" | "area" | "add";
  isCollapsed: boolean;
  start: number;
  end: number;
  range: DailyTimeRange | null;
  title: string;
  description?: string;
  id: string;
  assignedTo?: SchedulePersonClientDto;
  assignedPersonTotalWeekHours: number | null;
  areaId: string | null;
  areaTitle: string | null;
  isShiftLead?: boolean;
  shiftNum?: number;
  storePositionId?: string;
  storePositionTitle: string | undefined;
  changeStatus?: ScheduleShiftChangeStatus;
  incrementMetrics: IncrementMetrics[] | null;
  numShiftsInArea: number;
  numShiftHoursInArea: number;
  activities: ShiftActivity[] | null;
  shiftOffer: ShiftOfferDto | null;
  dayOfWeek: DayOfWeek;
  hasNotes: boolean;
}

export function isHouseOffer(shiftOffer: ShiftOfferDto): boolean {
  return !shiftOffer.offerorPersonId;
}

export function shiftChangeStatusToLabel(status: ScheduleShiftChangeStatus): string {
  switch (status) {
    case "unchanged":
      return "Published";
    case "created":
      return "Draft";
    case "updated":
      return "Edited";
    case "deleted":
      return "Deleted";
  }
}

/**
 * Convert an absolute date to an increment within a day of the week in an ISO week date.
 * @param date The absolute date
 * @param isoWeek The week of the schedule
 * @param storeHours
 * @param dayOfWeek The day of the week
 * @returns The increment or a number. If the date is before store hours, the number will be negative. If the date is after store hours, the number will be positive.
 */
export function dateToIncrement({date, isoWeek, storeHours, dayOfWeek, timezone}: {
  date: Date,
  isoWeek: IsoWeekDate,
  storeHours: DailyTimeRange,
  dayOfWeek: number,
  timezone: string | null
}): {
  increment: number;
} | number {
  const tz = timezone ?? "America/New_York";
  const utcDate = new UTCDate(date.getTime() + getTimezoneOffset(tz, date));
  const isoWeekNumber = getISOWeek(utcDate);
  const dateDayOfWeek = getISODay(utcDate);

  if (isoWeekNumber !== isoWeek.week) {
    return isoWeekNumber - isoWeek.week
  }

  if (dateDayOfWeek !== dayOfWeek) {
    return dateDayOfWeek - dayOfWeek;
  }

  const time = dateTo24HrTime(utcDate);
  const startComparison = compareTimeOfDay(time, storeHours.start);
  const isAfterStoreHoursStart = startComparison >= 0;
  const endComparison = compareTimeOfDay(time, storeHours.end);
  const isBeforeStoreHoursEnd = endComparison <= 0;

  if (isAfterStoreHoursStart && isBeforeStoreHoursEnd) {
    const increment = storeTimeToIncrement(storeHours, time);
    return {increment};
  }
  return isAfterStoreHoursStart ? 1 : -1;
}

/**
 * Convert a date range to an increment range on a particular day of the week. If the date range
 * start is before the store hours start on this day, the increment start will be 0. If the date range
 * end is after the store hours end on this day, the increment end will be the last increment of the day.
 * If both the start and end are outside the store hours and/or ISO week, this function will return null.
 * @param dayOfWeek
 * @param range
 * @param isoWeek
 * @param storeHours
 */
export function dateRangeToIncrementRange({dayOfWeek, range, isoWeek, storeHours, timezone}: {
  dayOfWeek: DayOfWeek;
  range: DateTimeRange;
  isoWeek: IsoWeekDate,
  storeHours: DailyTimeRange;
  timezone: string | null;
}): {
  start: number;
  end: number;
} | null {
  const start = dateToIncrement({date: range.start, isoWeek, storeHours, dayOfWeek, timezone});
  const end = dateToIncrement({date: range.end, isoWeek, storeHours, dayOfWeek, timezone});
  const lastIncrement = storeTimeToIncrement(storeHours, storeHours.end);

  if (typeof start === 'number' && typeof end === 'number') {
    if (start < 0 && end > 0) { // full day event
      return {start: 0, end: lastIncrement};
    }
    return null;
  }

  return {
    start: typeof start === "number" ? 0 : start.increment,
    end: typeof end === "number" ? lastIncrement : end.increment
  };
}

/**
 * Convert the 24 time in the store's time range to an abstract increment
 */
export function storeTimeToIncrement(storeHours: DailyTimeRange, time: string): number {
  // the store hours start is increment 0
  // get the difference in minutes between the time and the store hours start
  const givenMinutes = timeToMinutes(time);
  const storeStartMinutes = timeToMinutes(storeHours.start);
  const minutesDiff = givenMinutes - storeStartMinutes;
  return minutesDiff / incrementDurationMinutes;
}

export function dailyTimeRangeToIncrementRange(range: DailyTimeRange, storeHours: DailyTimeRange): IncrementTimeRange {
  const minInc = storeTimeToIncrement(storeHours, storeHours.start);
  const maxInc = storeTimeToIncrement(storeHours, storeHours.end);
  const start = storeTimeToIncrement(storeHours, range.start);
  const end = storeTimeToIncrement(storeHours, range.end);
  return {
    start: clamp(start, minInc, maxInc),
    end: clamp(end, minInc, maxInc),
  }
}

export function incrementRangeToDailyTimeRange(range: IncrementTimeRange, storeHours: DailyTimeRange): DailyTimeRange {
  const start = incrementTo24HourTime(storeHours, range.start);
  const end = incrementTo24HourTime(storeHours, range.end);
  return {
    start,
    end
  }
}

/**
 * Convert an increment to a 24-hour time in the store hours time range
 */
export function incrementTo24HourTime(storeHours: DailyTimeRange, increment: number): string {
  const storeStartMinutes = timeToMinutes(storeHours.start);
  const minutes = storeStartMinutes + (increment * incrementDurationMinutes);
  return minutesTo24HourTime(minutes);
}

export function incrementToFriendlyTime(storeHours: DailyTimeRange, increment: number, chopMinutes?: boolean): string {
  const time24 = incrementTo24HourTime(storeHours, increment);
  return to12HourTime(time24, chopMinutes);
}

export function getShiftChangeStatus({
                                       draftShift,
                                       draftShiftDay,
                                       publishedShift,
                                       publishedShiftDay
                                     }: {
  draftShift: Shift,
  draftShiftDay: DayOfWeek,
  publishedShift?: Shift,
  publishedShiftDay?: DayOfWeek
}): ScheduleShiftChangeStatus {
  if (!publishedShift) {
    return "created";
  }

  // treat isShiftLead specially, because Ayron populated the default schedule and default schedule template WITHOUT isShiftLead on shifts, because it used to be optional, with non-existence indicating false. This messes up the diff and causes confusing UI for the user.
  if (draftShift.isShiftLead !== undefined && publishedShift.isShiftLead !== undefined && draftShift.isShiftLead !== publishedShift.isShiftLead) {
    return "updated";
  }

  const tempDraftShift = omit(draftShift, ["isShiftLead"]);
  const tempPublishedShift = omit(publishedShift, ["isShiftLead"]);

  // is equal while ignoring undefined and null values
  if (!isEqual(omitNullish(tempDraftShift), omitNullish(tempPublishedShift))) {
    return "updated";
  }

  if (draftShiftDay !== publishedShiftDay) {
    return "updated";
  }

  return "unchanged";
}

export function getPersonTotalWeekHours(draft: BaseSchedule, person: SchedulePersonDto) {
  const personShifts: Shift[] = [];
  for (const day of draft.days) {
    for (const area of day.areas) {
      for (const shift of area.shifts) {
        if (shift.assignedPersonId === person.id) {
          personShifts.push(shift);
        }
      }
    }
  }

  return sumBy(personShifts, shift => getRangeDurationHours(shift.range));
}

export function shiftOverlapsIncrement(shift: ScheduleRowInfo, increment: number): boolean {
  return shift.type === "shift" &&
    doIncrementRangesOverlap({
      start: increment,
      end: increment + 1
    }, {
      start: shift.start,
      end: shift.end
    });
}

export function getAreaIncrementMetrics({shifts, numIncrements, storeHours, countOpenShiftsTowardsLabor}: {
  shifts: ScheduleRowInfo[],
  numIncrements: number,
  storeHours: DailyTimeRange,
  countOpenShiftsTowardsLabor: boolean;
}): IncrementMetrics[] {
  return times(numIncrements, increment => {
    const shiftsAtIncrement = filter(shifts, shift => shiftOverlapsIncrement(shift, increment));
    const assignedShiftsAtIncrement = filter(shiftsAtIncrement, shift => Boolean(shift.assignedTo));
    const numShifts = shiftsAtIncrement.length;
    const numAssignedShifts = assignedShiftsAtIncrement.length;
    const opsShifts = countOpenShiftsTowardsLabor
      ? shiftsAtIncrement
      : assignedShiftsAtIncrement;
    const numOpsShifts = filter(opsShifts, shift => getShiftOpsHours({
      shift: {
        range: shift.range ?? {start: "00:00", end: "00:00"}, // should be defined
        activities: shift.activities ?? []
      },
      countsTowardsLabor: true,
      range: to24HourRange(storeHours, {start: increment, end: increment + 1})
    }) > 0).length;

    return {
      numShifts,
      numAssignedShifts,
      numOpsShifts: numOpsShifts
    }
  })
}

export interface AreaSettings {
  [areaTitle: string]: {
    isCollapsed: boolean;
  }
}

export function shiftToScheduleRowInfo(shift: Shift, {
  draft,
  person,
  area,
  position,
  idx,
  shiftChangeStatus,
  shiftOffers,
  dayOfWeek
}: {
  draft: DraftSchedule;
  person: SchedulePersonClientDto | undefined;
  position: StorePositionDto | undefined;
  area: ShiftArea;
  idx: number;
  shiftChangeStatus: ScheduleShiftChangeStatus;
  shiftOffers: ShiftOfferDto[];
  dayOfWeek: DayOfWeek;
}): ScheduleRowInfo {
  const toInc = (time: string) => storeTimeToIncrement(draft.storeHours, time);
  const offer = find(shiftOffers, o => o.shiftId === shift.id);

  return {
    type: "shift",
    hasNotes: !isEmpty(trim(shift.description)),
    dayOfWeek: dayOfWeek,
    start: toInc(shift.range.start),
    end: toInc(shift.range.end),
    range: shift.range,
    title: shift.title ?? "",
    description: shift.description,
    id: shift.id,
    assignedTo: person,
    areaId: area.id,
    areaTitle: area.title,
    isShiftLead: shift.isShiftLead,
    shiftNum: idx + 1,
    storePositionId: shift.storePositionId,
    storePositionTitle: position?.title,
    changeStatus: shiftChangeStatus,
    assignedPersonTotalWeekHours: person ? getPersonTotalWeekHours(draft, person) : null,
    incrementMetrics: null,
    numShiftsInArea: 0,
    numShiftHoursInArea: 0,
    isCollapsed: false,
    activities: map(shift.activities, a => {
      return {
        id: a.id,
        activityType: a.activityType,
        title: a.title,
        description: a.description,
        start: toInc(a.range.start),
        end: toInc(a.range.end),
        range: a.range,
        countsTowardsLabor: a.countsTowardsLabor,
        payStatus: a.payStatus,
        setupPositionTitle: a.setupPositionTitle
      }
    }),
    shiftOffer: offer ?? null,
  };
}

export function genShifts({draft, published, people, dayOfWeek, positions, areaSettings, shiftOffers, countOpenShiftsTowardsLabor = false}: {
  draft: DraftSchedule,
  published?: PublishedSchedule,
  people: SchedulePersonClientDto[],
  dayOfWeek: number;
  positions: StorePositionDto[];
  areaSettings?: AreaSettings;
  shiftOffers: ShiftOfferDto[];
  countOpenShiftsTowardsLabor?: boolean;
}): Array<ScheduleRowInfo> {
  const day = find(draft.days, d => d.dayOfWeek === dayOfWeek);
  if (!day) {
    return [];
  }
  const toInc = (time: string) => storeTimeToIncrement(draft.storeHours, time);

  return flatMap(day.areas, (area): ScheduleRowInfo[] => {
    const shifts = map(area.shifts, (shift, idx): ScheduleRowInfo => {
      const publishedShift = published ? getShift(published, shift.id) : undefined;
      const person = find(people, p => p.id === shift.assignedPersonId);
      const shiftChangeStatus = getShiftChangeStatus({
        draftShift: shift,
        draftShiftDay: day.dayOfWeek,
        publishedShift: publishedShift,
        publishedShiftDay: published ? getShiftWeekday(published, shift.id) : undefined
      });
      const position = find(positions, p => p.id === shift.storePositionId);

      return shiftToScheduleRowInfo(shift, {
        draft,
        person,
        position,
        area,
        idx,
        shiftChangeStatus,
        shiftOffers,
        dayOfWeek
      });
    });

    const numIncrements = toInc(draft.storeHours.end);

    const isCollapsed = areaSettings?.[area.title]?.isCollapsed ?? false;
    return [
      {
        type: "area",
        title: area.title,
        hasNotes: false,
        id: area.id,
        incrementMetrics: getAreaIncrementMetrics({
          shifts: shifts,
          numIncrements: numIncrements,
          storeHours: draft.storeHours,
          countOpenShiftsTowardsLabor: countOpenShiftsTowardsLabor
        }),
        start: 0, end: 0,
        assignedTo: undefined,
        areaId: null,
        areaTitle: area.title,
        assignedPersonTotalWeekHours: null,
        storePositionTitle: undefined,
        numShiftsInArea: shifts.length,
        numShiftHoursInArea: sumBy(shifts, s => s.range ? getRangeDurationHours(s.range) : 0),
        activities: null,
        range: null,
        isCollapsed: isCollapsed,
        shiftOffer: null,
        dayOfWeek
      },
      ...isCollapsed ? [] : shifts,
      ...isCollapsed ? [] : [{
        type: "add" as const,
        title: "Add shift",
        hasNotes: false,
        id: `add${area.id}`,
        start: 0, end: 0,
        assignedTo: undefined,
        areaId: area.id,
        areaTitle: area.title,
        assignedPersonTotalWeekHours: null,
        storePositionTitle: undefined,
        incrementMetrics: null,
        numShiftsInArea: 0,
        numShiftHoursInArea: 0,
        activities: null,
        range: null,
        isCollapsed: false,
        shiftOffer: null,
        dayOfWeek
      }]
    ]
  })

}

export type PointerDownOutsideEvent = CustomEvent<{
  originalEvent: PointerEvent;
}>;
export type FocusOutsideEvent = CustomEvent<{
  originalEvent: FocusEvent;
}>;
export const formatDateInStoreTZ = new Intl.DateTimeFormat('en-US', {
  month: 'numeric',
  day: 'numeric',
  timeZone: 'UTC'
});
// just the day of the week in UTC like "Mon", "Tue", etc
export const formatDayOfWeek = new Intl.DateTimeFormat('en-US', {
  weekday: 'short',
  timeZone: 'UTC'
});

export interface ScheduleArea {
  id: string;
  title: string;
  description?: string;
  countsTowardsLabor: boolean;
  storeAreaId?: string;
}

export interface ScheduleBuilderEvent extends ScheduleEventDto {
  incrementRange: {
    start: number;
    end: number;
  } | null;
}

export function parseDatetimeLocalInput(dateString: string): Date {
  return parse(dateString, "yyyy-MM-dd'T'HH:mm", new Date())
}

export function formatForDatetimeLocalInput(date: Date, timezone: string | null): string {
  const str = formatInTimeZone(date, timezone ?? "America/New_York", "yyyy-MM-dd'T'HH:mm");
  return str;
}

export function parseHtmlDateInputToLocalDate(dateString: string): Date {
  return parse(dateString, "yyyy-MM-dd", new Date());
}

export function parseHtmlTimeInputToLocalTime(timeString: string): Date {
  return parse(timeString, "HH:mm", new Date(2000, 0, 1))
}

export function hourFloatToFriendlyDuration(hourFloat: number): string {
  const hours = Math.floor(hourFloat);
  const minutes = Math.round((hourFloat - hours) * 60);

  return `${hours}h ${minutes ? minutes + "m" : ""}`
}

export type NumberRange<T> = { start: number, end: number; meta?: T };

export function fillRangeGaps<T>(start: number, end: number, intervals: NumberRange<T>[]): NumberRange<T>[] {
  // sort intervals by start time
  const sortedIntervals = [...intervals].sort((a, b) => a.start - b.start);

  const result: NumberRange<T>[] = [];
  let currentStart = start;

  for (const interval of sortedIntervals) {
    const intervalStart = interval.start;
    const intervalEnd = interval.end;

    // if there's a gap before the current interval, add it
    if (currentStart < intervalStart) {
      result.push({start: currentStart, end: intervalStart});
    }

    // add the current interval (if it's within the range)
    if (intervalEnd > currentStart && intervalStart < end) {
      result.push({
        start: Math.max(currentStart, intervalStart),
        end: Math.min(intervalEnd, end),
        meta: interval.meta
      });
    }

    currentStart = Math.max(currentStart, intervalEnd);
  }

  // if there's a gap after the last interval, add it
  if (currentStart < end) {
    result.push({start: currentStart, end: end});
  }

  return result;
}

export function getDayPartsCssBackground({storeHours, incrementWidth, dayParts, offsetX = 0}: {
  storeHours: DailyTimeRange,
  incrementWidth: number,
  dayParts: DayPart[],
  offsetX?: number;
}): string {
  if (isEmpty(dayParts)) {
    return "";
  }

  // we're not dealing with pixels yet. Use an abstract range in the increment timeline
  const intervals = fillRangeGaps(
    0,
    storeTimeToIncrement(storeHours, storeHours.end),
    map(dayParts, part => ({
      start: storeTimeToIncrement(storeHours, part.range.start),
      end: storeTimeToIncrement(storeHours, part.range.end),
      meta: part
    })));

  const gradientParts = map(intervals, (interval, idx, parts) => {
    const isFirst = idx === 0;
    let startPx = interval.start * incrementWidth;
    let endPx = interval.end * incrementWidth;
    if (!isFirst) {
      startPx += offsetX;
      endPx += offsetX;
    }
    if (isFirst) {
      endPx += offsetX;
    }

    const color = interval.meta?.color ?? "transparent";
    return `${color} ${startPx}px ${endPx}px`;
  })
    // this little kludge is so the end period doesn't stretch out into the deadzone after the last increment
    .concat(`transparent ${storeTimeToIncrement(storeHours, storeHours.end) * incrementWidth}px`);

  return `linear-gradient(to right, ${gradientParts.join(', ')})`;
}

export function assertUnreachable<T>(x: never, retn: T): T {
  return retn;
}

export const messageToHumanReadable = (people: SchedulePersonDto[]) => (msg?: ValidationMessage): string => {
  if (!msg) {
    return "(no message)";
  }

  const person = find(people, p => p.id === ("personId" in msg.info ? msg.info.personId : undefined));
  const personFullName = person?.firstName + " " + person?.lastName;

  switch (msg.code) {
    case "ExceedsMaxHoursPreferred":
      return `${personFullName} is scheduled for more than their preferred max hours: ${msg.info.scheduledHours} > ${msg.info.preferredHours}`;
    case "ShiftOutsideStoreHours":
      return `Shift is outside of store hours`;
    case "ShiftConflictsWithTimeOff":
      return `Shift conflicts with ${personFullName}'s time off`;
    case "ShiftOverlaps":
      return `${personFullName} has overlapping shifts`;
    case "ShiftDurationExceedsMax":
      return `Shift duration exceeds maximum (${msg.info.maxDuration} hours)`;
    case "ShiftDurationLessThanMin":
      return `Shift duration is less than minimum (${msg.info.minDuration} hours)`;
    case "ShiftOutsideAvailability":
      return `${personFullName} is scheduled outside of their availability`;
    case "ShiftUnassigned":
      return `Shift is unassigned`;
    case "ShiftIdNotUnique":
      return `Shift ID is not unique`;
    case "AreaIdNotUnique":
      return `Area ID is not unique`;
    case "ShiftLeaderNotAssigned":
      return `Some periods of the day do not have a shift leader assigned: ${map(msg.info.gaps, gap => {
        return `${to12HourTime(gap.start, true)} - ${to12HourTime(gap.end, true)}`
      }).join(", ")}`
    case "PersonNotAssignedToStore":
      return `Team member ${person ? personFullName : ""} assigned to shift is not assigned to this store`;
    case "PersonNotActive":
      return `Team member ${person ? personFullName : ""} assigned to shift is not active in this store`;
    case "NotTrainedInPosition":
      return `Team member ${person ? personFullName : ""} is not trained in the ${msg.info.positionTitle} position.`;
    case "ScheduledOvertimeHours":
      return `Team member ${person ? personFullName : ""} scheduled more than ${overtimeHoursPerWeekThreshold} hours this week`;
    case "MinorSchoolDayMaxHoursPerDay":
      return `Minor ${person ? personFullName : ""} scheduled more than ${minorSchoolDayMaxHoursPerDay} hours on a school day (federal labor law)`;
    case "MinorMaxHoursPerSchoolWeek":
      return `Minor ${person ? personFullName : ""} scheduled more than ${minorMaxHoursPerSchoolWeek} hours on a school week (federal labor law)`;
    case "MinorScheduledBeforePermittedDayStart":
      return `Minor ${person ? personFullName : ""} scheduled before 7AM (federal labor law)`;
    case "MinorScheduledAfterPermittedDayEnd":
      return `Minor ${person ? personFullName : ""} scheduled after 7PM (federal labor law)`;
    case "PotentialOvertimeShift":
      return `Team member ${person ? personFullName : ""} scheduled more than ${overtimeHoursPerShiftThreshold} hours on a day (potential overtime ― state dependent)`;
    case "MinorScheduledCloseToPermittedDayEnd":
      return `Minor ${person ? personFullName : ""} scheduled close to 7PM cutoff (federal labor law)`;
    case "CloserOpenerShift":
      return `Closer & opener shift for ${person ? personFullName : ""} are less than ${closerOpenerHourThreshold} hours apart: ${hourFloatToFriendlyDuration(msg.info.gapDurationHours)}`;
    case "TimeOffRequestConflict":
      return `Shift conflicts with ${personFullName}'s pending time off request`
    case "MinorOvertimeDay":
      return `Minor ${person ? personFullName : ""} scheduled more than ${minorOvertimeHoursPerDayThreshold} hours on a day (federal labor law)`;
    case "MinorOvertimeWeek":
      return `Minor ${person ? personFullName : ""} scheduled more than ${minorOvertimeHoursPerWeekThreshold} hours in a week (federal labor law)`;
    case "HawaiiACAEligible":
      return `Warning: ${person ? personFullName : "Employee"} scheduled for at least 20 hours/week for 4 consecutive weeks, qualifying them for Hawaii ACA health insurance`;
    default:
      return assertUnreachable(msg, "Unknown validation message");
  }
}

export function getHumanReadableMessageCodeSummary(code: ValidationMessage["code"]): string {
  switch (code) {
    case "ShiftDurationExceedsMax":
      return "Shift duration exceeds maximum";
    case "ShiftDurationLessThanMin":
      return "Shift duration is less than minimum";
    case "ShiftOutsideStoreHours":
      return "Shift outside store hours";
    case "ShiftUnassigned":
      return "Shift is unassigned";
    case "ShiftOverlaps":
      return "Shift overlaps with another assigned shift";
    case "ShiftOutsideAvailability":
      return "Shift outside availability";
    case "ShiftConflictsWithTimeOff":
      return "Shift conflicts with time off";
    case "PersonNotAssignedToStore":
      return "Person not assigned to store";
    case "PersonNotActive":
      return "Person not active";
    case "AreaIdNotUnique":
      return "Area ID is not unique";
    case "ShiftLeaderNotAssigned":
      return "Some periods of the day do not have a shift leader assigned";
    case "ExceedsMaxHoursPreferred":
      return "Exceeds max hours preferred";
    case "ShiftIdNotUnique":
      return "Shift ID is not unique";
    case "NotTrainedInPosition":
      return "Not trained in position";
    case "ScheduledOvertimeHours":
      return `Scheduled more than ${overtimeHoursPerWeekThreshold} hours this week`
    case "MinorSchoolDayMaxHoursPerDay":
      return `Minor scheduled more than ${minorSchoolDayMaxHoursPerDay} hours on a school day`;
    case "MinorMaxHoursPerSchoolWeek":
      return `Minor scheduled more than ${minorMaxHoursPerSchoolWeek} hours on a school week`;
    case "MinorScheduledBeforePermittedDayStart":
      return `Minor scheduled before 7AM`;
    case "MinorScheduledAfterPermittedDayEnd":
      return `Minor scheduled after 7PM`;
    case "PotentialOvertimeShift":
      return `Potential overtime`;
    case "MinorScheduledCloseToPermittedDayEnd":
      return `Minor scheduled close to 7PM cutoff`;
    case "CloserOpenerShift":
      return `Closer/Opener`;
    case "TimeOffRequestConflict":
      return `Pending time off conflict`;
    case "MinorOvertimeDay":
      return `Minor daily overtime`;
    case "MinorOvertimeWeek":
      return `Minor weekly overtime`;
    case "HawaiiACAEligible":
      return `Scheduled more than 20 hours/week for 4 consecutive weeks, making them Hawaii ACA eligible`;
    default:
      return assertUnreachable(code, code);
  }
}

export function messageSeverityToLabel(severity: number): string {
  if (severity >= severityCritical) {
    return "Error"
  } else if (severity >= severityError) {
    return "Warning"
  } else if (severity >= severityWarning) {
    return "Caution"
  } else if (severity >= severityInfo) {
    return "Note"
  }
  return "Info"
}

export function messageSeverityToColor(severity: number): string {
  if (severity >= severityCritical) {
    return "bg-red-100"
  } else if (severity >= severityError) {
    return "bg-red-100"
  } else if (severity >= severityWarning) {
    return "bg-orange-100"
  } else if (severity >= severityInfo) {
    return "bg-yellow-100"
  }
  return "bg-gray-100"
}

export function dateRangeToFriendlyWeekFormat(range: DateTimeRange, timezone: string | null): string {
  const tz = timezone ?? "America/New_York";
  const tzOffset = getTimezoneOffset(tz, range.start);
  const zonedStart = new Date(range.start.getTime() + tzOffset);
  const zonedEnd = new Date(range.end.getTime() + tzOffset);
  const startDayOfWeekAbbr = formatDayOfWeek.format(zonedStart);
  const endDayOfWeekAbbr = formatDayOfWeek.format(zonedEnd);

  return startDayOfWeekAbbr + " " + to12HourTime(dateTo24HrTime(zonedStart), true) + " - " +
    (endDayOfWeekAbbr !== startDayOfWeekAbbr ? endDayOfWeekAbbr : "") + " "
    + to12HourTime(dateTo24HrTime(zonedEnd), true);
}

export function takeFromGenerator<T>(generator: Generator<T>, count: number): T[] {
  return Array.from({length: count}, () => generator.next().value);
}

export function* generateIsoWeekDates(start: IsoWeekDate, timezone: string | null, nextDate: (date: DateTime) => DateTime = date => date.plus({weeks: 1})): Generator<IsoWeekDate> {
  // Start from week 1 of the given year, then use Luxon arithmetic to get to the target week
  // This automatically handles negative weeks by rolling back to previous years
  let current = DateTime.fromObject({
    weekYear: start.year,
    weekNumber: 1
  }, { zone: timezone ?? undefined }).plus({ weeks: start.week - 1 });

  while (true) {
    yield { year: current.weekYear, week: current.weekNumber };
    current = nextDate(current);
  }
}

export function getChangeStatusColor(status: ScheduleShiftChangeStatus): string {
  switch (status) {
    case "unchanged":
      return "#4ade80";
    case "created":
      return "white";
    case "updated":
      return "#facc15";
    case "deleted":
      return "#f87171";
  }
  return "white";
}

export function getChangeStatusColorMuted(status: ScheduleShiftChangeStatus): string {
  switch (status) {
    case "unchanged":
      return "#4ade8070";
    case "created":
      return "white";
    case "updated":
      return "#facc1570";
    case "deleted":
      return "#f8717170";
  }
  return "white";
}

export function getChangeStatusTextColor(status: ScheduleShiftChangeStatus): string {
  switch (status) {
    case "unchanged":
      return "text-gray-900";
    case "created":
      return "text-gray-900";
    case "updated":
      return "text-gray-900";
    case "deleted":
      return "text-gray-900";
  }
}

export function stripSchedForDiff(sched: BaseSchedule): Partial<BaseSchedule> {
  return omit(sched, [
    "publishedAt", "isPublished"
  ]);
}

export const incrementDurationMinutes = 15;

export function getReasonLabel({reason, person, shift}: {
  reason: SuggestionReason,
  person: SchedulePersonClientDto,
  shift?: Shift
}): string {
  switch (reason.type) {
    case "conflictsWithLaborLaws":
      return `The shift duration conflicts with labor laws for the ${person.firstName}'s labor status`;
    case "conflictsWithTimeOff":
      return `The shift conflicts with ${person.firstName}'s time off`;
    case "greaterThanMaxPreferredHours":
      return `Assigning the shift would exceed ${person.firstName}'s preferred maximum hours this week`;
    case "is1Star":
      return `${person.firstName} has basic proficiency (1 star rating)`;
    case "is2Star":
      return `${person.firstName} has intermediate proficiency (2 star rating)`;
    case "is3Star":
      return `${person.firstName} has advanced proficiency (3 star rating)`;
    case "outsideAvailability":
      return `The shift is outside of ${person.firstName}'s availability`;
    case "overlappingShift":
      return `The shift overlaps with another assigned shift`;
    case "training":
      return `${person.firstName}'s training on positions in this area`;
    case "hasNoTrainingInArea":
      return `${person.firstName} has no training in the ${reason.areaTitle} store area`;
    case "personAlreadyScheduledThisDay":
      return `${person.firstName} is already scheduled for a shift this day`;
    case "notTrainedInPosition":
      return `${person.firstName} is not trained in the ${reason.positionTitle} position`;
    case "availabilityFit": {
      const hrs = Math.floor(reason.fit.distance / 60);
      const mins = Math.floor(reason.fit.distance % 60);
      const hrsStr = hrs > 0 ? `${hrs}h` : "";
      const minsStr = mins > 0 ? `${mins}m` : "";
      const duration = hrsStr + minsStr;
      switch (reason.fit.case) {
        case "inside":
          return `The shift is inside ${person.firstName}'s availability, with ${duration} slack`;
        case "partialOverlap":
          return `The shift partially overlaps with ${person.firstName}'s availability, with ${duration} outside`;
        case "outside":
          return `The shift is outside of ${person.firstName}'s availability`;
      }
      return `The shift is outside of ${person.firstName}'s availability`;
    }
    case "scheduledOvertimeHours":
      return `Assigning the shift would exceed ${overtimeHoursPerWeekThreshold} hours this week (potential overtime)`;
    case "minorSchoolDayMaxHoursPerDay":
      return `Assigning the shift would exceed ${minorSchoolDayMaxHoursPerDay} hours on this day (labor law for minors)`;
    case "minorMaxHoursPerSchoolWeek":
      return `Assigning the shift would exceed ${minorMaxHoursPerSchoolWeek} hours this week (labor law for minors)`;
    case "minorScheduledBeforePermittedDayStart":
      return `The shift starts before 7AM for a minor`;
    case "minorScheduledAfterPermittedDayEnd":
      return `The shift ends after 7PM for a minor`;
    case "potentialOvertimeShift":
      return `Assigning the shift would exceed ${overtimeHoursPerShiftThreshold} hours on this day (potential overtime)`;
    case "minorScheduledCloseToPermittedDayEnd":
      return `The shift ends close to 7PM cutoff for a minor`;
    case "closerOpenerShift":
      return `The shift is less than ${closerOpenerHourThreshold} hours from a shift the previous day (Closer & Opener)`;
    case "minorOvertimeDay":
      return `Assigning the shift would exceed ${minorOvertimeHoursPerDayThreshold} hours per day for a minor (labor law)`;
    case "minorOvertimeWeek":
      return `Assigning the shift would exceed ${minorOvertimeHoursPerWeekThreshold} hours per week for a minor (labor law)`;
    case "hawaiiACAEligible":
      return `Assigning the shift would qualify the employee for Hawaii ACA health insurance (20+ hours/week for 4 consecutive weeks)`;
    default:
      return assertUnreachable(reason, "Unknown reason");
  }
}

