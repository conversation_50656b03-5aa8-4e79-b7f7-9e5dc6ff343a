import {z} from "zod";
import {dailyTimeRange, dayOfWeek, isoWeekDate, weekDayTimeRange} from "./timeSchemas";
import {transformActivitiesToValid} from "./shiftActivityValidation";
import {sanitizeTimeRange} from "./date.util";

import {baseShiftOffer} from "./baseShiftOffer";
import {validScheduleHourlySalesForecast} from "./scheduling/metrics/scheduleHourlySalesForecast/scheduleHourlySalesForecastTypes";
import {setupPositionTitle} from "./setupSheets/storeSetupPositions/storeSetupPositionSchemas";

const color = z.string().regex(/^#[0-9a-f]{6}$/i);


export const activityType = z.enum(["admin", "setups", "breaks", "training", "custom"]);
export type ShiftActivityType = z.infer<typeof activityType>;

// only relevant to break type activities... The pay status of the break -- paid or unpaid.
export const activityPayStatus = z.enum(["paid", "unpaid"]);

/**
 * A shift activity is a time range on a shift with some metadata. We can think of it as an "activity" a person
 * does during the shift, such as setup sheet position, breaks, admin/non-ops, etc.
 */
export const activity = z.object({
  id: z.string(),
  activityType: activityType,
  title: z.string().min(0).max(256),
  description: z.string().max(9000).optional(),
  range: dailyTimeRange,
  countsTowardsLabor: z.boolean(),
  setupPositionTitle: setupPositionTitle.optional(),
  payStatus: activityPayStatus.optional()
});

export type Activity = z.infer<typeof activity>;

export const unvalidatedShift = z.object({
  id: z.string(),
  range: dailyTimeRange,
  shiftAreaId: z.string(),
  title: z.string().min(0).max(256).optional(),
  description: z.string().max(9000).optional(),
  storePositionId: z.string().optional(),
  assignedPersonId: z.string().optional(),
  isShiftLead: z.boolean().optional(),
  activities: z.array(activity).optional()
});

export type UnvalidatedShift = z.infer<typeof unvalidatedShift>;

export const transformShift = (shift: UnvalidatedShift) => {
  return {
    ...shift,
    activities: transformActivitiesToValid(shift.activities ?? [], shift.id, shift.range),
    range: sanitizeTimeRange(shift.range)
  }
};

export const shiftSchema = unvalidatedShift.transform(transformShift).brand("Shift")
export type Shift = z.infer<typeof shiftSchema>;

export interface AssignedShift extends Shift {
  assignedPersonId: string;
}

export const shiftArea = z.object({
  id: z.string(),
  storeAreaId: z.string().optional(),
  title: z.string().min(0).max(256),
  description: z.string().max(1024).optional(),
  shifts: z.array(shiftSchema),
  countsTowardsLabor: z.boolean(),
});

export type ShiftArea = z.infer<typeof shiftArea>;

export const dayPart = z.object({
  title: z.string().min(1).max(256),
  range: dailyTimeRange,
  color: color,
});

export type DayPart = z.infer<typeof dayPart>;

export const scheduleMetricsInput = z.object({
  projectedRevenue: z.number().nonnegative().optional(),
  productivityGoal: z.number().nonnegative().optional(),
  averagePayRate: z.number().nonnegative().optional(),
});

export type ScheduleMetricsInput = z.infer<typeof scheduleMetricsInput>;

export const scheduleDay = z.object({
  dayOfWeek: dayOfWeek,
  areas: z.array(shiftArea),
  metricsInput: scheduleMetricsInput.optional(),
  createdFromTemplateId: z.string().optional(),
});

export type ScheduleDay = z.infer<typeof scheduleDay>;

export const dayParts = z.array(dayPart).min(0).max(20);
export const peakHours = z.array(weekDayTimeRange);
export type PeakHours = z.infer<typeof peakHours>;
export const scheduleDays = z.array(scheduleDay).min(7).max(7).refine(days => {
  // must have a day for every day of the week. However, order doesn't matter.
  const daysActual = new Set(days.map(d => d.dayOfWeek));
  for (let i = 1; i <= 7; i++) {
    if (!daysActual.has(i)) {
      return false;
    }
  }
  return true;
}, {
  message: "There must be a day for every day of the week"
});
export type ScheduleDays = z.infer<typeof scheduleDays>;

export const baseSchedule = z.object({
  title: z.string().min(1).max(256).optional(),
  description: z.string().max(1024).optional(),
  businessId: z.string(),
  storeId: z.string(),
  id: z.string(),
  createdFromId: z.string().optional(),
  week: isoWeekDate,
  storeHours: dailyTimeRange,
  dayParts: dayParts,
  peakHours: peakHours,
  days: scheduleDays,
  isTemplate: z.boolean(),
  isPublished: z.boolean(),
  publishedAt: z.date().optional(),
});

export type BaseSchedule = z.infer<typeof baseSchedule>;

/**
 * Zod schema for exactly 3 consecutive weeks of schedules for Hawaii ACA validation.
 * Each element can be either a BaseSchedule or null (for missing weeks).
 */
export const hawaiiACAWeekSchedules = z.tuple([
  baseSchedule.nullable(),
  baseSchedule.nullable(),
  baseSchedule.nullable()
]);

export type HawaiiACAWeekSchedules = z.infer<typeof hawaiiACAWeekSchedules>;

/**
 * Zod schema for Hawaii ACA hours data - maps person IDs to arrays of 3 numbers
 * representing hours worked in each of the 3 weeks (in chronological order).
 */
export const hawaiiACAWeekHours = z.record(z.string(), z.tuple([z.number(), z.number(), z.number()]));

export type HawaiiACAWeekHours = z.infer<typeof hawaiiACAWeekHours>;

/**
 * Zod schema for Hawaii ACA person hours data - maps person IDs to an object containing
 * both preceding and following week hours arrays.
 */
export const hawaiiACAPersonHours = z.record(z.string(), z.object({
  precedingWeeks: z.tuple([z.number(), z.number(), z.number()]),
  followingWeeks: z.tuple([z.number(), z.number(), z.number()])
}));

export type HawaiiACAPersonHours = z.infer<typeof hawaiiACAPersonHours>;

export const draftSchedule = baseSchedule.extend({
  isPublished: z.literal(false),
});

export type DraftSchedule = z.infer<typeof draftSchedule>;

export const publishedSchedule = baseSchedule.extend({
  isPublished: z.literal(true),
  publishedAt: z.date(),
}).brand();

export type PublishedSchedule = z.infer<typeof publishedSchedule>;

export const scheduleRevision = z.object({
  draft: draftSchedule.optional(),
  draftVersion: z.number().int().nonnegative().optional(),
  published: publishedSchedule.optional(),
  publishedVersion: z.number().int().nonnegative().optional(),
})

export type ScheduleRevision = z.infer<typeof scheduleRevision>;

export const scheduleRevisionWithForecast = scheduleRevision.extend({
  forecast: validScheduleHourlySalesForecast.optional()
});

export type ScheduleRevisionWithForecast = z.infer<typeof scheduleRevisionWithForecast>;

/**
 * Represents the changes to a schedule that are being proposed by a user.
 */
export interface ScheduleEdits {
  /**
   * The draft schedule with the proposed changes.
   */
  draft: DraftSchedule;
  draftVersion?: number;
  /**
   * The currently published schedule that the draft will modify
   */
  published?: PublishedSchedule;
  /**
   * The currently published schedule version that the draft will modify
   */
  publishedVersion?: number;
}

export const shiftOfferDto = baseShiftOffer.extend({
  status: z.string(),
});

export type ShiftOfferDto = z.infer<typeof shiftOfferDto>;

export const scheduleDto = z.object({
  hasDraft: z.boolean(),
  draft: draftSchedule,
  draftVersion: z.number().int().nonnegative(),
  published: publishedSchedule.optional(),
  publishedVersion: z.number().int().nonnegative().optional(),
  timezone: z.string(),
  shiftOffers: z.array(shiftOfferDto),
  forecast: validScheduleHourlySalesForecast.optional()
})
export type ScheduleDto = z.infer<typeof scheduleDto>;

export const scheduleIncludes = {
  ScheduleDay: {
    include: {
      ShiftArea: {
        include: {
          Shift: {
            include: {
              shiftActivities: true
            }
          }
        }
      }
    }
  }
} as const
