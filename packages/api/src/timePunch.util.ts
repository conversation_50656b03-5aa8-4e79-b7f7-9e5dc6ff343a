/**
 * Utilities for matching time punch entries with scheduled shifts
 */

import {Shift} from "./scheduleSchemas";
import {filter, map, orderBy, sortBy, groupBy} from "lodash";
import {getShiftId} from "./shift";
import {convertTo12HourFormat, dateTo24HrTime} from "./date.util";
import {Decimal} from "@prisma/client/runtime/library";
import {genId} from "./schemas";

/**
 * Shift data with both domain object and date information for matching
 */
export interface ShiftWithDateInfo {
  shift: Shift;
  startAbs: Date | null; // Absolute start date from database
  assignedPersonId: string | null;
}

export interface TimePunchMatchResult {
  scheduledTimeRange: string | null;
  scheduledShiftId: string | null;
  matchDistance: number;
  clockInVariance: number | null;    // Variance in minutes (negative = less work time, positive = more work time)
  clockOutVariance: number | null;   // Variance in minutes (negative = less work time, positive = more work time)
  varianceTotal: number | null;      // Total variance in minutes (negative = less work time, positive = more work time)
}

export interface TimePunchMatchOptions {
  timezone: string | null;
  punchDate?: Date | null; // Date of the time punch entry for same-day validation
  employeePersonId?: string | null; // Person ID to filter shifts by employee assignment
}



/**
 * Convert time string to minutes for comparison
 * Handles various time formats from PDF parsing
 */
export function timeToMinutes(timeStr: string): number {
  if (!timeStr || timeStr.trim() === "") return 0;

  const cleanTime = timeStr.trim().toLowerCase();
  let hours: number;
  let minutes: number;

  if (cleanTime.includes('a') || cleanTime.includes('p')) {
    const isAm = cleanTime.includes('a');
    const timeWithoutAmPm = cleanTime.replace(/[ap]m?/g, '').trim();

    if (timeWithoutAmPm.includes(':')) {
      [hours, minutes] = map(timeWithoutAmPm.split(':'), Number);
    } else {
      if (timeWithoutAmPm.length >= 3) {
        hours = parseInt(timeWithoutAmPm.slice(0, -2));
        minutes = parseInt(timeWithoutAmPm.slice(-2));
      } else {
        hours = parseInt(timeWithoutAmPm);
        minutes = 0;
      }
    }

    if (!isAm && hours !== 12) hours += 12;
    if (isAm && hours === 12) hours = 0;
  } else {
    [hours, minutes] = map(timeStr.split(':'), Number);
  }

  return hours * 60 + minutes;
}

/**
 * Format time from 24-hour to 12-hour format
 */
export function formatTime(timeStr: string): string {
  if (!timeStr) return "";

  try {
    const [hours, minutes] = map(timeStr.split(':'), Number);
    const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    const ampm = hours >= 12 ? 'PM' : 'AM';
    return `${hour12}:${minutes.toString().padStart(2, '0')} ${ampm}`;
  } catch {
    return timeStr;
  }
}



/**
 * Calculate variance between actual and scheduled times
 * Returns variance in minutes representing additional work time
 * Positive = more work time, Negative = less work time
 */
export function calculateTimeVariance(
  actualTimeStr: string | null,
  scheduledTimeStr: string | null,
  isClockIn: boolean = true
): number | null {
  if (!actualTimeStr || !scheduledTimeStr) return null;

  // Handle "Open Punch" values - cannot calculate variance
  if (actualTimeStr === "Open Punch" || scheduledTimeStr === "Open Punch") return null;

  const actualMinutes = timeToMinutes(actualTimeStr);
  const scheduledMinutes = timeToMinutes(scheduledTimeStr);

  if (actualMinutes <= 0 || scheduledMinutes <= 0) return null;

  const diffMinutes = actualMinutes - scheduledMinutes;

  // For clock-in: early = more work time (positive), late = less work time (negative)
  // For clock-out: late = more work time (positive), early = less work time (negative)
  if (diffMinutes === 0) return 0;
  return isClockIn ? -diffMinutes : diffMinutes;
}

/**
 * Calculate total variance (sum of clock-in and clock-out variances)
 * Returns the total variance in minutes (negative = less work time, positive = more work time)
 */
export function calculateTotalVariance(
  clockInVariance: number | null,
  clockOutVariance: number | null
): number | null {
  if (clockInVariance === null && clockOutVariance === null) return null;

  const clockInMinutes = clockInVariance || 0;
  const clockOutMinutes = clockOutVariance || 0;
  return clockInMinutes + clockOutMinutes;
}

/**
 * Calculate the "distance" between punch times and shift times
 * Simply finds the closest start and end times regardless of overlap
 * Lower distance indicates a better match
 */
export function calculateShiftMatchDistance(
  punchInMinutes: number,
  punchOutMinutes: number,
  shiftStartMinutes: number,
  shiftEndMinutes: number
): number {
  // Calculate absolute difference between start times
  const startTimeDifference = Math.abs(punchInMinutes - shiftStartMinutes);

  // Calculate absolute difference between end times
  const endTimeDifference = Math.abs(punchOutMinutes - shiftEndMinutes);

  // Return combined distance (sum of both differences)
  return startTimeDifference + endTimeDifference;
}

/**
 * Combine time punches with unpaid time punches to create unified work sessions
 * Handles sequences like Regular -> Unpaid -> Regular -> Unpaid -> Regular
 * Returns all punches (combined and standalone) in a unified format
 */
export function combineTimePunchesWithUnpaidTimePunches<T extends TimePunchEntryWithPayRate>(
  timePunches: T[]
): T[] {
  // Group punches by person and date
  const groupedPunches = groupBy(timePunches, (punch) => {
    const personId = punch.person?.id || punch.personId || 'unknown';
    const dateStr = punch.date.toISOString().split('T')[0];
    return `${personId}-${dateStr}`;
  });

  const result: T[] = [];

  for (const [key, punches] of Object.entries(groupedPunches)) {
    // Sort punches by timeIn to process them in chronological order
    const sortedPunches = orderBy(punches, (punch) => {
      if (!punch.timeIn || punch.timeIn === "Open Punch") return 0;
      return timeToMinutes(punch.timeIn);
    });

    // Process punches to find combinable sequences
    const processedPunches = processPunchSequences(sortedPunches);
    result.push(...processedPunches);
  }

  return result;
}

/**
 * Process a sequence of time punches for a single person on a single date
 * Combines Regular -> Unpaid -> Regular sequences into unified punches
 */
function processPunchSequences<T extends TimePunchEntryWithPayRate>(
  punches: T[]
): T[] {
  const result: T[] = [];
  let i = 0;

  while (i < punches.length) {
    const currentPunch = punches[i];

    // Skip invalid punches (no timeIn/timeOut or missing payType)
    if (!currentPunch.payType || !currentPunch.timeIn) {
      i++;
      continue;
    }

    // If this is a Regular punch, check if it starts a combinable sequence
    if (currentPunch.payType === 'Regular') {
      const combinedPunch = findAndCombineSequence(punches, i);
      result.push(combinedPunch.punch);
      i = combinedPunch.nextIndex;
    } else {
      // Non-Regular punches (like standalone Unpaid) are ignored
      i++;
    }
  }

  return result;
}

/**
 * Find and combine a sequence starting from a Regular punch
 * Returns the combined punch and the next index to process
 */
function findAndCombineSequence<T extends TimePunchEntryWithPayRate>(
  punches: T[],
  startIndex: number
): { punch: T, nextIndex: number } {
  const firstPunch = punches[startIndex];
  let currentIndex = startIndex;
  let totalUnpaidMinutes = 0;
  let totalRegularHours = 0;
  let lastRegularPunchTimeOut = firstPunch.timeOut;
  let hasOpenPunch = false;

  // Add the first Regular punch hours
  if (firstPunch.totalHours) {
    totalRegularHours += Number(firstPunch.totalHours);
  }

  // Check if first punch has open timeOut
  if (firstPunch.timeOut === "Open Punch") {
    hasOpenPunch = true;
  }

  currentIndex++;

  // Look for alternating Unpaid -> Regular sequences
  while (currentIndex < punches.length) {
    const currentPunch = punches[currentIndex];
    const prevPunch = punches[currentIndex - 1];

    // Skip invalid punches
    if (!currentPunch.payType || !currentPunch.timeIn || !currentPunch.timeOut) {
      currentIndex++;
      continue;
    }

    // Check if this continues the sequence
    if (currentPunch.payType === 'Unpaid' && prevPunch.payType === 'Regular') {
      // Check time gap tolerance (2 minutes)
      if (isWithinTimeGap(prevPunch.timeOut, currentPunch.timeIn)) {
        // Add unpaid time
        if (currentPunch.totalHours) {
          totalUnpaidMinutes += Math.round(Number(currentPunch.totalHours) * 60);
        }
        currentIndex++;
        continue;
      } else {
        // Gap too large, break the sequence
        break;
      }
    } else if (currentPunch.payType === 'Regular' && prevPunch.payType === 'Unpaid') {
      // Check time gap tolerance (2 minutes)
      if (isWithinTimeGap(prevPunch.timeOut, currentPunch.timeIn)) {
        // Add regular time and update last timeOut
        if (currentPunch.totalHours) {
          totalRegularHours += Number(currentPunch.totalHours);
        }
        lastRegularPunchTimeOut = currentPunch.timeOut;

        // Check if this Regular punch has open timeOut
        if (currentPunch.timeOut === "Open Punch") {
          hasOpenPunch = true;
        }

        currentIndex++;
        continue;
      } else {
        // Gap too large, break the sequence
        break;
      }
    } else {
      // Sequence broken (not alternating or different payType)
      break;
    }
  }

  // Create the combined punch
  const combinedPunch: T = {
    ...firstPunch,
    id: genId(), // Generate new ID for combined punch
    timeOut: hasOpenPunch ? "Open Punch" : lastRegularPunchTimeOut,
    totalHours: new Decimal(totalRegularHours),
    totalUnpaidMinutes: totalUnpaidMinutes > 0 ? totalUnpaidMinutes : undefined,
  };

  return {
    punch: combinedPunch,
    nextIndex: currentIndex
  };
}

/**
 * Check if two time strings are within the allowed gap tolerance (2 minutes)
 */
function isWithinTimeGap(timeOut: string | null, timeIn: string | null): boolean {
  if (!timeOut || !timeIn || timeOut === "Open Punch" || timeIn === "Open Punch") {
    return false;
  }

  const timeOutMinutes = timeToMinutes(timeOut);
  const timeInMinutes = timeToMinutes(timeIn);

  // Allow up to 2 minutes gap
  const gap = Math.abs(timeInMinutes - timeOutMinutes);
  return gap <= 2;
}

/**
 * Find the best matching shift for given punch times
 * Returns the shift with the lowest distance score
 */
export function findBestMatchingShift(
  punchTimeIn: string | null,
  punchTimeOut: string | null,
  shifts: ShiftWithDateInfo[],
  options: TimePunchMatchOptions
): TimePunchMatchResult {
  // Return null result if no valid punch times or shifts
  if (!punchTimeIn || !punchTimeOut || shifts.length === 0) {
    return {
      scheduledTimeRange: null,
      scheduledShiftId: null,
      matchDistance: Infinity,
      clockInVariance: null,
      clockOutVariance: null,
      varianceTotal: null,
    };
  }

  // Filter shifts by employee assignment and date
  let filteredShifts = shifts;

  if (options.employeePersonId) {
    filteredShifts = filter(filteredShifts, shiftInfo =>
      shiftInfo.assignedPersonId === options.employeePersonId
    );
  }

  if (options.punchDate) {
    const punchDateStr = options.punchDate.toISOString().split('T')[0];
    filteredShifts = filter(filteredShifts, shiftInfo => {
      if (!shiftInfo.startAbs) return false;
      const shiftDateStr = shiftInfo.startAbs.toISOString().split('T')[0];
      return shiftDateStr === punchDateStr;
    });
  }

  if (filteredShifts.length === 0) {
    return {
      scheduledTimeRange: null,
      scheduledShiftId: null,
      matchDistance: Infinity,
      clockInVariance: null,
      clockOutVariance: null,
      varianceTotal: null,
    };
  }

  const punchInMinutes = timeToMinutes(punchTimeIn);
  const punchOutMinutes = timeToMinutes(punchTimeOut);

  // Skip if punch times are invalid
  if (punchInMinutes <= 0 || punchOutMinutes <= 0) {
    return {
      scheduledTimeRange: null,
      scheduledShiftId: null,
      matchDistance: Infinity,
      clockInVariance: null,
      clockOutVariance: null,
      varianceTotal: null,
    };
  }

  let bestMatch: { shift: ShiftWithDateInfo; distance: number } | null = null;

  for (const shiftInfo of filteredShifts) {
    // Shift times are already 24-hour time strings in store timezone
    const shiftStartMinutes = timeToMinutes(shiftInfo.shift.range.start);
    const shiftEndMinutes = timeToMinutes(shiftInfo.shift.range.end);

    const distance = calculateShiftMatchDistance(
      punchInMinutes,
      punchOutMinutes,
      shiftStartMinutes,
      shiftEndMinutes
    );

    if (!bestMatch || distance < bestMatch.distance) {
      bestMatch = { shift: shiftInfo, distance };
    }
  }

  if (!bestMatch) {
    return {
      scheduledTimeRange: null,
      scheduledShiftId: null,
      matchDistance: Infinity,
      clockInVariance: null,
      clockOutVariance: null,
      varianceTotal: null,
    };
  }

  // Format the time range for display (shift times are already in store timezone)
  const startTime = bestMatch.shift.shift.range.start; // Already 24-hour format in store timezone
  const endTime = bestMatch.shift.shift.range.end;     // Already 24-hour format in store timezone
  const scheduledTimeRange = `${formatTime(startTime)} - ${formatTime(endTime)}`;

  // Calculate variances
  const clockInVariance = calculateTimeVariance(punchTimeIn, startTime, true);
  const clockOutVariance = calculateTimeVariance(punchTimeOut, endTime, false);
  const varianceTotal = calculateTotalVariance(clockInVariance, clockOutVariance);

  return {
    scheduledTimeRange,
    scheduledShiftId: bestMatch.shift.shift.id,
    matchDistance: bestMatch.distance,
    clockInVariance,
    clockOutVariance,
    varianceTotal,
  };
}

/**
 * Results from matching shifts and time punches
 */
export interface MatchingResults {
  matchedPairs: Array<{
    timePunchId: string;
    shiftId: string;
    matchResult: TimePunchMatchResult;
  }>;
  unmatchedTimePunches: Array<{
    timePunchId: string;
  }>;
  unmatchedShifts: Array<{
    shiftId: string;
  }>;
}

/**
 * Optimized matching function using strict constraints instead of scoring
 * Much more efficient - only calculates distances when there are conflicts
 */
export function matchShiftsAndTimePunches<T extends { id: string; person?: { id: string } | null; timeIn: string | null; timeOut: string | null; date: Date }>(
  shifts: ShiftWithDateInfo[],
  timePunches: T[],
  timezone: string
): MatchingResults {
  const matchedPairs: MatchingResults['matchedPairs'] = [];
  const usedTimePunches = new Set<string>();
  const usedShifts = new Set<string>();

  // Group time punches by person and date
  const timePunchesByPersonAndDate = new Map<string, T[]>();
  for (const timePunch of timePunches) {
    if (!timePunch.timeIn || !timePunch.timeOut || !timePunch.person?.id) continue;

    const key = `${timePunch.person.id}-${timePunch.date.toISOString().split('T')[0]}`;
    if (!timePunchesByPersonAndDate.has(key)) {
      timePunchesByPersonAndDate.set(key, []);
    }
    timePunchesByPersonAndDate.get(key)!.push(timePunch);
  }

  // Group shifts by person and date
  const shiftsByPersonAndDate = new Map<string, ShiftWithDateInfo[]>();
  for (const shift of shifts) {
    if (!shift.assignedPersonId || !shift.startAbs) continue;

    const key = `${shift.assignedPersonId}-${shift.startAbs.toISOString().split('T')[0]}`;
    if (!shiftsByPersonAndDate.has(key)) {
      shiftsByPersonAndDate.set(key, []);
    }
    shiftsByPersonAndDate.get(key)!.push(shift);
  }

  // Process each person-date combination
  for (const [key, timePunchesForKey] of timePunchesByPersonAndDate) {
    const shiftsForKey = shiftsByPersonAndDate.get(key) || [];

    // Filter out already used items
    const availableTimePunches = filter(timePunchesForKey, tp => !usedTimePunches.has(tp.id));
    const availableShifts = filter(shiftsForKey, s => !usedShifts.has(getShiftId(s.shift)));

    // Simple 1:1 matching - if exactly one of each, match them automatically
    if (availableTimePunches.length === 1 && availableShifts.length === 1) {
      const timePunch = availableTimePunches[0];
      const shift = availableShifts[0];

      // Calculate variance for this match (no distance calculation needed)
      const matchResult = calculateMatchResult(timePunch, shift, timezone);

      matchedPairs.push({
        timePunchId: timePunch.id,
        shiftId: getShiftId(shift.shift),
        matchResult,
      });

      usedTimePunches.add(timePunch.id);
      usedShifts.add(getShiftId(shift.shift));
    }
    // If multiple options, fall back to distance-based matching for this group only
    else if (availableTimePunches.length > 0 && availableShifts.length > 0) {
      const candidates: Array<{ timePunchId: string; shiftId: string; matchResult: TimePunchMatchResult; distance: number }> = [];

      for (const timePunch of availableTimePunches) {
        for (const shift of availableShifts) {
          const matchResult = calculateMatchResult(timePunch, shift, timezone);
          if (matchResult.matchDistance !== Infinity && matchResult.scheduledShiftId) {
            candidates.push({
              timePunchId: timePunch.id,
              shiftId: getShiftId(shift.shift),
              matchResult,
              distance: matchResult.matchDistance,
            });
          }
        }
      }

      // Sort by distance and greedily assign
      const sortedCandidates = sortBy(candidates, 'distance');
      for (const candidate of sortedCandidates) {
        if (!usedTimePunches.has(candidate.timePunchId) && !usedShifts.has(candidate.shiftId)) {
          matchedPairs.push({
            timePunchId: candidate.timePunchId,
            shiftId: candidate.shiftId,
            matchResult: candidate.matchResult,
          });
          usedTimePunches.add(candidate.timePunchId);
          usedShifts.add(candidate.shiftId);
        }
      }
    }
  }

  // Find unmatched time punches
  const unmatchedTimePunches = map(
    filter(timePunches, tp => !usedTimePunches.has(tp.id)),
    tp => ({ timePunchId: tp.id })
  );

  // Find unmatched shifts
  const unmatchedShifts = map(
    filter(shifts, shift => !usedShifts.has(getShiftId(shift.shift))),
    shift => ({ shiftId: getShiftId(shift.shift) })
  );

  return {
    matchedPairs,
    unmatchedTimePunches,
    unmatchedShifts,
  };
}

/**
 * Calculate match result for a specific time punch and shift pair
 */
function calculateMatchResult<T extends { timeIn: string | null; timeOut: string | null; date: Date; person?: { id: string } | null }>(
  timePunch: T,
  shift: ShiftWithDateInfo,
  timezone: string
): TimePunchMatchResult {
  return findBestMatchingShift(
    timePunch.timeIn,
    timePunch.timeOut,
    [shift],
    { timezone, punchDate: timePunch.date, employeePersonId: timePunch.person?.id }
  );
}

/**
 * Legacy function for backward compatibility
 * Simplified one-to-one matching that works directly with database entries
 * Ensures each shift can only be matched to at most one time punch entry
 */
export function matchTimePunchesToShifts<T extends { id: string; person?: { id: string } | null; timeIn: string | null; timeOut: string | null; date: Date }>(
  entries: T[],
  shifts: ShiftWithDateInfo[],
  timezone: string
): Map<string, TimePunchMatchResult> {
  const matchingResults = matchShiftsAndTimePunches(shifts, entries, timezone);

  // Convert to legacy format
  const assignments = new Map<string, TimePunchMatchResult>();
  for (const pair of matchingResults.matchedPairs) {
    assignments.set(pair.timePunchId, pair.matchResult);
  }

  return assignments;
}

/**
 * Calculate total variance cost from unified variance rows (includes matched, unmatched time punches, and unmatched shifts)
 *
 * @param varianceRows - Array of unified variance rows
 * @returns Total variance cost in cents (positive = cost/bad, negative = savings/good, 0 = even)
 */
export function calculateTotalVarianceCost(varianceRows: Array<{
  varianceTotal: number | null;
  payRate: number | null;
}>): number {
  let totalCents = 0;

  for (const row of varianceRows) {
    const varianceMinutes = row.varianceTotal;
    const payRateCents = row.payRate;

    if (varianceMinutes === null || varianceMinutes === undefined || !payRateCents || varianceMinutes === 0) continue;

    // Convert minutes to hours and calculate cost
    const costCents = Math.round((varianceMinutes / 60) * payRateCents);

    // Negative variance minutes = early/no-shows (savings), positive = late/unscheduled (cost)
    // Invert the sign so that savings are positive (good) and costs are negative (bad)
    totalCents -= costCents;
  }

  return totalCents;
}

/**
 * Team totals summary statistics calculated from all filtered data
 */
export interface TeamTotalsSummary {
  totalScheduled: number;      // Total scheduled minutes across all filtered rows
  totalActual: number;         // Total actual minutes across all filtered rows
  totalVarianceTime: number;   // Total variance minutes across all filtered rows
  totalVarianceCost: number;   // Total variance cost in cents across all filtered rows
  totalWages: number;          // Total wages in cents across all filtered rows
}

/**
 * Calculate summary statistics for team totals from filtered data
 * This provides totals across ALL filtered data, not just the paginated subset
 *
 * @param filteredTeamTotals - Array of filtered team totals rows
 * @returns Summary statistics object
 */
export function calculateTeamTotalsSummary(filteredTeamTotals: TeamTotalsRow[]): TeamTotalsSummary {
  let totalScheduled = 0;
  let totalActual = 0;
  let totalVarianceTime = 0;
  let totalWages = 0;

  for (const row of filteredTeamTotals) {
    // Sum scheduled and actual time
    totalScheduled += row.scheduledTime || 0;
    totalActual += row.actualTime || 0;

    // Sum variance time
    totalVarianceTime += row.varianceTotal || 0;

    // Sum total wages
    totalWages += row.totalWages || 0;
  }

  // Use existing function to calculate total variance cost
  // Note: calculateTotalVarianceCost inverts the sign, so we invert it back for display
  // We want: positive = cost (red), negative = savings (green)
  const totalVarianceCost = -calculateTotalVarianceCost(filteredTeamTotals);

  return {
    totalScheduled,
    totalActual,
    totalVarianceTime,
    totalVarianceCost,
    totalWages
  };
}

/**
 * Team totals aggregated row structure
 * Represents aggregated variance data for a single team member across a date range
 */
export interface TeamTotalsRow {
  id: string; // assignedPersonId
  assignedPersonId: string;
  employeeName: string;
  scheduledTime: number | null; // Total scheduled minutes across all shifts (null = no data)
  actualTime: number | null;    // Total actual punch minutes across all punches (null = no data)
  clockInVariance: number | null;  // Sum of clockInVariance (only from matched pairs)
  clockOutVariance: number | null; // Sum of clockOutVariance (only from matched pairs)
  varianceTotal: number | null;   // Sum of ALL variance totals (matched + unmatched)
  breaks: number | null; // Total break minutes for this person in the date range
  payRate: number | null; // Pay rate (should be consistent across all rows for same person)
  totalWages: number | null; // Total wages in cents (actualTime * payRate)
  date: Date; // Most recent date for sorting purposes
  profileImageUrl: string | null;    // Profile image URL for PersonAvatar
  age: number | null;                // Age for LaborStatusIcon calculation
  proficiencyRanking: number | null; // Proficiency ranking for ProficiencyRating stars
}

/**
 * Aggregate variance rows by team member (assignedPersonId)
 * Groups all variance data for each person and calculates totals
 */
export function aggregateVarianceRowsByTeamMember(
  varianceRows: VarianceRow[],
  allShiftRows: ShiftWithPersonInfo[],
  allTimePunches: TimePunchEntryWithPayRate[],
  allBreakPunches: TimePunchEntryWithPayRate[],
  allStoreEmployees: Array<{
    id: string;
    firstName: string | null;
    lastName: string | null;
    profileImage?: { id: string } | null;
    metadata?: any;
    employments?: Array<{
      stores: Array<{
        proficiencyRanking: number | null;
        storeId: string;
      }>;
    }>;
  }>,
  ctx: { getImageUrl: (prefix: string, imageId: string) => string },
  storeId: string | { toString(): string }
): TeamTotalsRow[] {
  // Create a map to group rows by assignedPersonId
  const personGroups = new Map<string, VarianceRow[]>();

  // Create a map for person info lookup with enhanced data
  const personInfoMap = new Map<string, {
    profileImageUrl: string | null;
    age: number | null;
    proficiencyRanking: number | null;
  }>();

  // Create a map for break totals by person ID
  const breakTotalsMap = new Map<string, number>();

  // Populate person info from shifts (basic data only)
  for (const shift of allShiftRows) {
    if (shift.assignedPersonId && shift.assignedPerson) {
      personInfoMap.set(shift.assignedPersonId, {
        profileImageUrl: null, // Will be populated from allStoreEmployees
        age: null, // Will be populated from allStoreEmployees
        proficiencyRanking: null // Will be populated from allStoreEmployees
      });
    }
  }

  // Populate person info from time punches
  for (const punch of allTimePunches) {
    if (punch.person?.id) {
      // Note: We don't have firstName/lastName in time punch person data
      // This will be handled by getting the name from variance rows
    }
  }

  // Calculate break totals by person ID from break punches
  for (const breakPunch of allBreakPunches) {
    if (breakPunch.person?.id && breakPunch.totalHours) {
      const personId = breakPunch.person.id;
      const breakMinutes = Math.round(Number(breakPunch.totalHours) * 60); // Convert hours to minutes

      if (breakTotalsMap.has(personId)) {
        breakTotalsMap.set(personId, breakTotalsMap.get(personId)! + breakMinutes);
      } else {
        breakTotalsMap.set(personId, breakMinutes);
      }
    }
  }

  // Populate person data from allStoreEmployees
  for (const employee of allStoreEmployees) {
    // Calculate age from metadata birthday (only if metadata is available)
    let age: number | null = null;
    if (employee.metadata?.birthday) {
      const birthDate = new Date(employee.metadata.birthday);
      const today = new Date();
      age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
    }

    // Get proficiency ranking for this store (only if employments is available)
    let proficiencyRanking: number | null = null;
    if (employee.employments) {
      const storeEmployment = employee.employments
        .flatMap(emp => emp.stores)
        .find(store => store.storeId === storeId.toString());
      proficiencyRanking = storeEmployment?.proficiencyRanking || null;
    }

    // Get profile image URL (only if profileImage is available)
    const profileImageUrl = employee.profileImage
      ? ctx.getImageUrl(employee.id, employee.profileImage.id)
      : null;

    // Update or create person info with enhanced data
    personInfoMap.set(employee.id, {
      profileImageUrl,
      age,
      proficiencyRanking
    });
  }

  // Group variance rows by person
  for (const row of varianceRows) {
    let personId: string;

    // Determine the assignedPersonId based on row type
    if (row.type === 'matched' || row.type === 'unmatchedTimePunch') {
      // For matched and unmatched time punches, we need to find the person ID
      // This should be available in the original time punch data
      const timePunch = allTimePunches.find(tp =>
        row.timePunchId && tp.id === row.timePunchId
      );
      if (timePunch?.person?.id) {
        personId = timePunch.person.id;
      } else {
        // Fallback: use employeeName as identifier if no person ID
        personId = `name:${row.employeeName}`;
      }
    } else if (row.type === 'unmatchedShift') {
      // For unmatched shifts, find the assignedPersonId from shift data
      const shift = allShiftRows.find(s =>
        row.scheduledShiftId && s.id === row.scheduledShiftId
      );
      if (shift?.assignedPersonId) {
        personId = shift.assignedPersonId;
      } else {
        // Fallback: use employeeName as identifier
        personId = `name:${row.employeeName}`;
      }
    } else {
      // Fallback: use employeeName as identifier
      personId = `name:${row.employeeName}`;
    }

    if (!personGroups.has(personId)) {
      personGroups.set(personId, []);
    }
    personGroups.get(personId)!.push(row);

    // Add unpaid break minutes from this variance row to the break totals
    if (row.totalUnpaidMinutes && row.totalUnpaidMinutes > 0) {
      const actualPersonId = personId.startsWith('name:') ? personId.substring(5) : personId;

      if (breakTotalsMap.has(actualPersonId)) {
        breakTotalsMap.set(actualPersonId, breakTotalsMap.get(actualPersonId)! + row.totalUnpaidMinutes);
      } else {
        breakTotalsMap.set(actualPersonId, row.totalUnpaidMinutes);
      }
    }
  }

  // Aggregate data for each person
  const teamTotals: TeamTotalsRow[] = [];

  for (const [personId, rows] of personGroups) {
    let scheduledTime = 0;
    let actualTime = 0;
    let clockInVarianceSum = 0;
    let clockOutVarianceSum = 0;
    let varianceTotalSum: number | null = null;
    let payRate: number | null = null;
    let hasClockInVariance = false;
    let hasClockOutVariance = false;
    let mostRecentDate = new Date(0); // Start with epoch

    // Get employee name (use the first row's name)
    const employeeName = rows[0]?.employeeName || 'Unknown Employee';

    for (const row of rows) {
      // Track most recent date
      if (row.date > mostRecentDate) {
        mostRecentDate = row.date;
      }

      // Set pay rate (should be consistent across all rows for same person)
      if (row.payRate && !payRate) {
        payRate = row.payRate;
      }

      // Aggregate scheduled time (from shifts) - use pre-calculated duration
      if (row.scheduledTimeDuration && row.type !== 'unmatchedTimePunch') {
        scheduledTime += row.scheduledTimeDuration;
      }

      // Aggregate actual time (from time punches) - use pre-calculated duration
      if (row.punchTimeDuration && row.type !== 'unmatchedShift') {
        actualTime += row.punchTimeDuration;
      }

      // Aggregate clock-in variance (only from matched pairs)
      if (row.clockInVariance !== null && row.type === 'matched') {
        clockInVarianceSum += row.clockInVariance;
        hasClockInVariance = true;
      }

      // Aggregate clock-out variance (only from matched pairs)
      if (row.clockOutVariance !== null && row.type === 'matched') {
        clockOutVarianceSum += row.clockOutVariance;
        hasClockOutVariance = true;
      }

      // Aggregate total variance (from all row types)
      if (row.varianceTotal !== null) {
        if (varianceTotalSum === null) {
          varianceTotalSum = row.varianceTotal;
        } else {
          varianceTotalSum += row.varianceTotal;
        }
      }
    }

    // Get break total for this person
    const actualPersonId = personId.startsWith('name:') ? personId.substring(5) : personId;
    const breakTotal = breakTotalsMap.get(actualPersonId) || null;

    // Get enhanced person data
    const personInfo = personInfoMap.get(actualPersonId) || {
      profileImageUrl: null,
      age: null,
      proficiencyRanking: null
    };

    // Calculate total wages (actualTime in minutes * payRate in cents per hour)
    let totalWages: number | null = null;
    if (actualTime !== null && payRate !== null && actualTime > 0) {
      // Convert minutes to hours and multiply by pay rate (cents per hour)
      totalWages = Math.round((actualTime / 60) * payRate);
    }

    teamTotals.push({
      id: personId,
      assignedPersonId: actualPersonId,
      employeeName: employeeName,
      scheduledTime: scheduledTime, // Keep in minutes
      actualTime: actualTime, // Keep in minutes
      clockInVariance: hasClockInVariance ? clockInVarianceSum : null,
      clockOutVariance: hasClockOutVariance ? clockOutVarianceSum : null,
      varianceTotal: varianceTotalSum,
      breaks: breakTotal, // Total break minutes for this person
      payRate,
      totalWages, // Total wages in cents
      date: mostRecentDate,
      // Enhanced person data
      profileImageUrl: personInfo.profileImageUrl,
      age: personInfo.age,
      proficiencyRanking: personInfo.proficiencyRanking,
    });
  }

  // Ensure all store employees have rows, even if they have no variance data
  const existingPersonIds = new Set(teamTotals.map(row => row.assignedPersonId));

  for (const employee of allStoreEmployees) {
    if (!existingPersonIds.has(employee.id)) {
      // Create a row with zero values for employees with no data
      const employeeName = `${employee.firstName} ${employee.lastName}`.trim();
      const breakTotal = breakTotalsMap.get(employee.id) || null;

      const personInfo = personInfoMap.get(employee.id) || {
        profileImageUrl: null,
        age: null,
        proficiencyRanking: null
      };

      teamTotals.push({
        id: employee.id,
        assignedPersonId: employee.id,
        employeeName: employeeName,
        scheduledTime: null, // No scheduled time (will display as dash)
        actualTime: null, // No actual time (will display as dash)
        clockInVariance: null, // No variance data
        clockOutVariance: null, // No variance data
        varianceTotal: null, // No variance
        breaks: breakTotal, // May have breaks even without other data
        payRate: null, // No pay rate data
        totalWages: null, // No wages (no actual time or pay rate)
        date: new Date(), // Use current date as placeholder
        profileImageUrl: personInfo.profileImageUrl,
        age: personInfo.age,
        proficiencyRanking: personInfo.proficiencyRanking,
      });
    }
  }

  return teamTotals;
}

/**
 * Parse time range string (e.g., "9:00 AM - 5:00 PM") and return duration in minutes
 * Simple calculation between two times
 */
function parseTimeRangeDuration(timeRange: string): number {
  try {
    const parts = timeRange.split(' - ');
    if (parts.length !== 2) return 0;

    const [startTime, endTime] = parts;

    // Use existing timeToMinutes function for both times
    const startMinutes = timeToMinutes(startTime);
    const endMinutes = timeToMinutes(endTime);

    if (startMinutes <= 0 || endMinutes <= 0) return 0;

    let duration = endMinutes - startMinutes;

    // Handle overnight shifts
    if (duration < 0) {
      duration += 24 * 60;
    }

    return duration;
  } catch (error) {
    return 0;
  }
}

/**
 * Filter team totals rows based on time criteria (applied after aggregation)
 */
export function filterTeamTotalsRows(
  teamTotals: TeamTotalsRow[],
  filters: {
    personId?: string;
    timeType?: string; // 'all' | 'scheduled' | 'actual'
    timeCondition?: string; // 'all' | 'over' | 'under'
    timeThreshold?: number; // hours
  }
): TeamTotalsRow[] {
  let filtered = teamTotals;

  // Apply person ID filter
  if (filters.personId && filters.personId.trim() !== '') {
    filtered = filtered.filter(row =>
      row.assignedPersonId === filters.personId
    );
  }

  // Apply time-based filter
  if (filters.timeType && filters.timeType !== 'all' &&
      filters.timeCondition && filters.timeCondition !== 'all' &&
      filters.timeThreshold !== undefined) {

    const thresholdMinutes = filters.timeThreshold * 60; // Convert hours to minutes

    filtered = filtered.filter(row => {
      let timeValue: number | null;

      if (filters.timeType === 'scheduled') {
        timeValue = row.scheduledTime;
      } else if (filters.timeType === 'actual') {
        timeValue = row.actualTime;
      } else {
        return true; // 'all' - no filtering
      }

      // Filter out null/undefined values - they should not match any time-based criteria
      if (timeValue === null || timeValue === undefined) {
        return false;
      }

      if (filters.timeCondition === 'over') {
        return timeValue > thresholdMinutes;
      } else if (filters.timeCondition === 'under') {
        return timeValue < thresholdMinutes;
      }

      return true;
    });
  }

  return filtered;
}

/**
 * Sort team totals rows
 * Matches variance table sorting logic where null values (dashes) appear at the bottom
 */
export function sortTeamTotalsRows(
  teamTotals: TeamTotalsRow[],
  sortBy?: string,
  sortOrder?: string
): TeamTotalsRow[] {
  if (!sortBy) {
    // Default sort by employee name
    return orderBy(teamTotals, ['employeeName'], ['asc']);
  }

  const direction = sortOrder === 'desc' ? 'desc' : 'asc';

  // Custom sorting with null handling (matches variance table logic)
  return [...teamTotals].sort((a, b) => {
    let compareValue: number;

    switch (sortBy) {
      case 'employeeName':
        compareValue = a.employeeName.localeCompare(b.employeeName);
        break;
      case 'scheduledTime':
        // Handle null values (dashes) - they should appear at the end regardless of sort order
        if (a.scheduledTime === null && b.scheduledTime === null) {
          compareValue = 0; // Both null, equal
        } else if (a.scheduledTime === null) {
          compareValue = direction === 'desc' ? -1 : 1; // a is null, put it at the end
        } else if (b.scheduledTime === null) {
          compareValue = direction === 'desc' ? 1 : -1; // b is null, put it at the end
        } else {
          compareValue = a.scheduledTime - b.scheduledTime;
        }
        break;
      case 'actualTime':
        // Handle null values (dashes) - they should appear at the end regardless of sort order
        if (a.actualTime === null && b.actualTime === null) {
          compareValue = 0; // Both null, equal
        } else if (a.actualTime === null) {
          compareValue = direction === 'desc' ? -1 : 1; // a is null, put it at the end
        } else if (b.actualTime === null) {
          compareValue = direction === 'desc' ? 1 : -1; // b is null, put it at the end
        } else {
          compareValue = a.actualTime - b.actualTime;
        }
        break;
      case 'clockInVariance':
        // Handle null values (dashes) - they should appear at the end regardless of sort order
        if (a.clockInVariance === null && b.clockInVariance === null) {
          compareValue = 0; // Both null, equal
        } else if (a.clockInVariance === null) {
          compareValue = direction === 'desc' ? -1 : 1; // a is null, put it at the end
        } else if (b.clockInVariance === null) {
          compareValue = direction === 'desc' ? 1 : -1; // b is null, put it at the end
        } else {
          compareValue = a.clockInVariance - b.clockInVariance;
        }
        break;
      case 'clockOutVariance':
        // Handle null values (dashes) - they should appear at the end regardless of sort order
        if (a.clockOutVariance === null && b.clockOutVariance === null) {
          compareValue = 0; // Both null, equal
        } else if (a.clockOutVariance === null) {
          compareValue = direction === 'desc' ? -1 : 1; // a is null, put it at the end
        } else if (b.clockOutVariance === null) {
          compareValue = direction === 'desc' ? 1 : -1; // b is null, put it at the end
        } else {
          compareValue = a.clockOutVariance - b.clockOutVariance;
        }
        break;
      case 'varianceTotal':
        // Handle null values (dashes) - they should appear at the end regardless of sort order
        if (a.varianceTotal === null && b.varianceTotal === null) {
          compareValue = 0; // Both null, equal
        } else if (a.varianceTotal === null) {
          compareValue = direction === 'desc' ? -1 : 1; // a is null, put it at the end
        } else if (b.varianceTotal === null) {
          compareValue = direction === 'desc' ? 1 : -1; // b is null, put it at the end
        } else {
          compareValue = a.varianceTotal - b.varianceTotal;
        }
        break;
      case 'breaks':
        // Handle null values (dashes) - they should appear at the end regardless of sort order
        if (a.breaks === null && b.breaks === null) {
          compareValue = 0; // Both null, equal
        } else if (a.breaks === null) {
          compareValue = direction === 'desc' ? -1 : 1; // a is null, put it at the end
        } else if (b.breaks === null) {
          compareValue = direction === 'desc' ? 1 : -1; // b is null, put it at the end
        } else {
          compareValue = a.breaks - b.breaks;
        }
        break;
      case 'date':
        compareValue = a.date.getTime() - b.date.getTime();
        break;
      default:
        // Fallback to employee name
        compareValue = a.employeeName.localeCompare(b.employeeName);
    }

    return direction === 'desc' ? -compareValue : compareValue;
  });
}

/**
 * Unified row structure for variance analysis
 * Represents either a matched pair, unmatched time punch, or unmatched shift
 */
export interface VarianceRow {
  id: string; // Unique identifier for the row
  type: 'matched' | 'unmatchedTimePunch' | 'unmatchedShift';

  // Employee info (always present)
  employeeName: string;
  date: Date; // Either punch date or shift date
  payRate: number | null;

  // Time punch fields (null for unmatched shifts)
  timePunchId: string | null;
  dayOfWeek: string | null;
  timeIn: string | null;
  timeOut: string | null;
  totalHours: number | null;
  payType: string | null;
  totalUnpaidMinutes: number | null; // Total unpaid break time for combined punches

  // Shift fields (null for unmatched time punches)
  scheduledShiftId: string | null;
  scheduledTimeRange: string | null;
  scheduledShiftDate: string | null;

  // Pre-calculated duration fields (in minutes)
  scheduledTimeDuration: number | null; // Duration of scheduled shift in minutes
  punchTimeDuration: number | null;     // Duration of actual time punch in minutes

  // Variance fields (null for unmatched shifts)
  clockInVariance: number | null;
  clockOutVariance: number | null;
  varianceTotal: number | null;
}

/**
 * Time punch entry with pay rate information (matches database structure)
 */
export interface TimePunchEntryWithPayRate {
  id: string;
  storeId: string;
  businessId: string;
  uploadedFileId: string;
  employeeName: string;
  personId: string | null;
  dayOfWeek: string | null;
  date: Date;
  timeIn: string | null;
  timeOut: string | null;
  totalHours: Decimal | null;
  payType: string | null;
  totalUnpaidMinutes?: number; // Added for combined time punches with unpaid breaks
  person?: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    employments?: Array<{
      stores?: Array<{
        payRate: number | null;
      }>;
    }>;
  } | null;
}

/**
 * Shift data with person information for row construction (matches Prisma result)
 */
export interface ShiftWithPersonInfo {
  id: string;
  storeId: string | null;
  shiftAreaId: string | null;
  storePositionId: string | null;
  assignedPersonId: string | null;
  title: string | null;
  description: string | null;
  start: Date;
  end: Date;
  startAbs: Date | null;
  endAbs: Date | null;
  year: number | null;
  week: number | null;
  day: number | null;
  isShiftLead: boolean;
  order: number;
  storeAreaId: string | null;
  assignedPerson?: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    employments?: Array<{
      stores?: Array<{
        payRate: number | null;
      }>;
    }>;
  } | null;
  shiftActivities: Array<{
    id: string;
    shiftId: string;
    title: string;
    activityType: string;
    description: string | null;
    start: Date;
    end: Date;
    countsTowardsLabor: boolean;
  }>;
}

/**
 * Construct unified variance rows from matching results
 */
export function constructVarianceRows(
  matchingResults: MatchingResults,
  timePunches: TimePunchEntryWithPayRate[],
  shifts: ShiftWithPersonInfo[]
): VarianceRow[] {
  const rows: VarianceRow[] = [];

  // Create a lookup map for time punches
  const timePunchMap = new Map<string, TimePunchEntryWithPayRate>();
  for (const tp of timePunches) {
    timePunchMap.set(tp.id, tp);
  }

  // Create a lookup map for shifts
  const shiftMap = new Map<string, ShiftWithPersonInfo>();
  for (const shift of shifts) {
    shiftMap.set(shift.id, shift);
  }

  // 1. Create rows for matched pairs
  for (const pair of matchingResults.matchedPairs) {
    const timePunch = timePunchMap.get(pair.timePunchId);
    const shift = shiftMap.get(pair.shiftId);

    if (timePunch && shift) {
      rows.push(createMatchedRow(timePunch, shift, pair.matchResult));
    }
  }

  // 2. Create rows for unmatched time punches
  for (const unmatched of matchingResults.unmatchedTimePunches) {
    const timePunch = timePunchMap.get(unmatched.timePunchId);
    if (timePunch) {
      rows.push(createUnmatchedTimePunchRow(timePunch));
    }
  }

  // 3. Create rows for unmatched shifts
  for (const unmatched of matchingResults.unmatchedShifts) {
    const shift = shiftMap.get(unmatched.shiftId);
    if (shift) {
      rows.push(createUnmatchedShiftRow(shift));
    }
  }

  return rows;
}

/**
 * Create a row for a matched time punch and shift pair
 */
function createMatchedRow(
  timePunch: TimePunchEntryWithPayRate,
  shift: ShiftWithPersonInfo,
  matchResult: TimePunchMatchResult
): VarianceRow {
  const payRate = timePunch.person?.employments?.[0]?.stores?.[0]?.payRate || null;
  const scheduledShiftDate = shift.startAbs?.toISOString().split('T')[0] || null;

  // Calculate durations
  const scheduledTimeDuration = matchResult.scheduledTimeRange
    ? parseTimeRangeDuration(matchResult.scheduledTimeRange)
    : null;

  const punchTimeDuration = timePunch.totalHours
    ? Math.round(Number(timePunch.totalHours) * 60) // Convert hours to minutes
    : null;

  return {
    id: `matched-${timePunch.id}-${shift.id}`,
    type: 'matched',

    // Employee info
    employeeName: timePunch.person
      ? `${timePunch.person.firstName} ${timePunch.person.lastName}`.trim()
      : timePunch.employeeName,
    date: timePunch.date,
    payRate,

    // Time punch fields
    timePunchId: timePunch.id,
    dayOfWeek: timePunch.dayOfWeek,
    timeIn: timePunch.timeIn,
    timeOut: timePunch.timeOut,
    totalHours: timePunch.totalHours ? Number(timePunch.totalHours) : null,
    payType: timePunch.payType,
    totalUnpaidMinutes: timePunch.totalUnpaidMinutes || null,

    // Shift fields
    scheduledShiftId: shift.id,
    scheduledTimeRange: matchResult.scheduledTimeRange,
    scheduledShiftDate,

    // Pre-calculated duration fields
    scheduledTimeDuration,
    punchTimeDuration,

    // Variance fields
    clockInVariance: matchResult.clockInVariance,
    clockOutVariance: matchResult.clockOutVariance,
    varianceTotal: matchResult.varianceTotal,
  };
}

/**
 * Create a row for an unmatched time punch (no corresponding shift found)
 * Variance represents the full cost of unscheduled work
 */
function createUnmatchedTimePunchRow(timePunch: TimePunchEntryWithPayRate): VarianceRow {
  const payRate = timePunch.person?.employments?.[0]?.stores?.[0]?.payRate || null;

  // Calculate variance as the full duration worked (since it was unscheduled)
  let varianceTotal: number | null = null;
  let punchTimeDuration: number | null = null;

  if (timePunch.totalHours) {
    // Convert total hours to minutes (positive = cost since this is unscheduled work)
    punchTimeDuration = Math.round(Number(timePunch.totalHours) * 60);
    varianceTotal = punchTimeDuration;
  }

  return {
    id: `unmatched-timepunch-${timePunch.id}`,
    type: 'unmatchedTimePunch',

    // Employee info
    employeeName: timePunch.person
      ? `${timePunch.person.firstName} ${timePunch.person.lastName}`.trim()
      : timePunch.employeeName,
    date: timePunch.date,
    payRate,

    // Time punch fields
    timePunchId: timePunch.id,
    dayOfWeek: timePunch.dayOfWeek,
    timeIn: timePunch.timeIn,
    timeOut: timePunch.timeOut,
    totalHours: timePunch.totalHours ? Number(timePunch.totalHours) : null,
    payType: timePunch.payType,
    totalUnpaidMinutes: timePunch.totalUnpaidMinutes || null,

    // Shift fields (null for unmatched time punches)
    scheduledShiftId: null,
    scheduledTimeRange: null,
    scheduledShiftDate: null,

    // Pre-calculated duration fields
    scheduledTimeDuration: null, // No scheduled time for unmatched punches
    punchTimeDuration,

    // Variance fields - show full duration as variance since work was unscheduled
    clockInVariance: null,
    clockOutVariance: null,
    varianceTotal, // Full hours worked as positive variance (cost)
  };
}

/**
 * Create a row for an unmatched shift (no corresponding time punch found)
 * Variance represents the cost of scheduled but unworked hours
 */
function createUnmatchedShiftRow(shift: ShiftWithPersonInfo): VarianceRow {
  const payRate = shift.assignedPerson?.employments?.[0]?.stores?.[0]?.payRate || null;
  const employeeName = shift.assignedPerson
    ? `${shift.assignedPerson.firstName} ${shift.assignedPerson.lastName}`.trim()
    : 'Unknown Employee';

  // Use shift date (startAbs is in UTC, but we'll use it as the date)
  const shiftDate = shift.startAbs || new Date();
  const scheduledShiftDate = shift.startAbs?.toISOString().split('T')[0] || null;

  // Format the scheduled time range from shift start/end times
  const startTime24 = dateTo24HrTime(shift.start); // Convert to HH:MM format
  const endTime24 = dateTo24HrTime(shift.end);
  const startTime12 = convertTo12HourFormat(startTime24); // Convert to 12-hour format
  const endTime12 = convertTo12HourFormat(endTime24);
  const scheduledTimeRange = `${startTime12} - ${endTime12}`;

  // Calculate variance as the full scheduled duration (since no work was done)
  let varianceTotal: number | null = null;
  // Calculate scheduled duration in minutes regardless of pay rate
  const startMinutes = timeToMinutes(startTime24);
  const endMinutes = timeToMinutes(endTime24);
  const scheduledDurationMinutes = endMinutes - startMinutes;

  if (scheduledDurationMinutes > 0) {
    // Negative variance = scheduled but not worked (savings for business)
    varianceTotal = -scheduledDurationMinutes;
  }

  return {
    id: `unmatched-shift-${shift.id}`,
    type: 'unmatchedShift',

    // Employee info
    employeeName,
    date: shiftDate,
    payRate,

    // Time punch fields (null for unmatched shifts)
    timePunchId: null,
    dayOfWeek: null,
    timeIn: null,
    timeOut: null,
    totalHours: null,
    payType: null,
    totalUnpaidMinutes: null,

    // Shift fields
    scheduledShiftId: shift.id,
    scheduledTimeRange,
    scheduledShiftDate,

    // Pre-calculated duration fields
    scheduledTimeDuration: scheduledDurationMinutes,
    punchTimeDuration: null, // No punch time for unmatched shifts

    // Variance fields - show full scheduled duration as negative variance (savings)
    clockInVariance: null,
    clockOutVariance: null,
    varianceTotal, // Full scheduled hours as negative variance (savings)
  };
}

/**
 * Sort variance rows by date (desc) and employee name (asc)
 * Maintains the same default sorting as the original time punch queries
 */
export function sortVarianceRows(
  rows: VarianceRow[],
  sortBy?: string,
  sortOrder?: 'asc' | 'desc'
): VarianceRow[] {
  if (!sortBy || !sortOrder) {
    // Default sorting: date desc, then employee name asc
    return [...rows].sort((a, b) => {
      // Primary sort: date (descending)
      const dateCompare = b.date.getTime() - a.date.getTime();
      if (dateCompare !== 0) return dateCompare;

      // Secondary sort: employee name (ascending)
      return a.employeeName.localeCompare(b.employeeName);
    });
  }

  // Custom sorting based on sortBy parameter
  return [...rows].sort((a, b) => {
    let compareValue: number;

    switch (sortBy) {
      case 'employeeName':
        compareValue = a.employeeName.localeCompare(b.employeeName);
        break;
      case 'date':
        compareValue = a.date.getTime() - b.date.getTime();
        break;
      case 'timeIn':
        const aTimeIn = a.timeIn || '';
        const bTimeIn = b.timeIn || '';
        compareValue = aTimeIn.localeCompare(bTimeIn);
        break;
      case 'timeOut':
        const aTimeOut = a.timeOut || '';
        const bTimeOut = b.timeOut || '';
        compareValue = aTimeOut.localeCompare(bTimeOut);
        break;
      case 'totalHours':
        const aTotalHours = a.totalHours || 0;
        const bTotalHours = b.totalHours || 0;
        compareValue = aTotalHours - bTotalHours;
        break;
      case 'scheduledTimeRange':
        const aScheduled = a.scheduledTimeRange || '';
        const bScheduled = b.scheduledTimeRange || '';
        compareValue = aScheduled.localeCompare(bScheduled);
        break;
      case 'varianceTotal':
        const aVariance = a.varianceTotal || 0;
        const bVariance = b.varianceTotal || 0;
        compareValue = aVariance - bVariance;
        break;
      case 'clockInVariance':
        // Handle null values (dashes) - they should appear at the end regardless of sort order
        if (a.clockInVariance === null && b.clockInVariance === null) {
          compareValue = 0; // Both null, equal
        } else if (a.clockInVariance === null) {
          compareValue = sortOrder === 'desc' ? -1 : 1; // a is null, put it at the end
        } else if (b.clockInVariance === null) {
          compareValue = sortOrder === 'desc' ? 1 : -1; // b is null, put it at the end
        } else {
          compareValue = a.clockInVariance - b.clockInVariance;
        }
        break;
      case 'clockOutVariance':
        // Handle null values (dashes) - they should appear at the end regardless of sort order
        if (a.clockOutVariance === null && b.clockOutVariance === null) {
          compareValue = 0; // Both null, equal
        } else if (a.clockOutVariance === null) {
          compareValue = sortOrder === 'desc' ? -1 : 1; // a is null, put it at the end
        } else if (b.clockOutVariance === null) {
          compareValue = sortOrder === 'desc' ? 1 : -1; // b is null, put it at the end
        } else {
          compareValue = a.clockOutVariance - b.clockOutVariance;
        }
        break;
      default:
        // Fallback to employee name
        compareValue = a.employeeName.localeCompare(b.employeeName);
    }

    return sortOrder === 'desc' ? -compareValue : compareValue;
  });
}

/**
 * Filter variance rows based on variance criteria
 */
export function filterVarianceRows(
  rows: VarianceRow[],
  filters: {
    showOpenPunchesOnly?: boolean;
    punchType?: 'all' | 'clockIn' | 'clockOut';
    punchTiming?: 'all' | 'early' | 'late';
    timeCondition?: 'all' | 'lessThan' | 'greaterThan';
    timeThreshold?: number; // minutes
  }
): VarianceRow[] {
  const {
    showOpenPunchesOnly = false,
    punchType = 'all',
    punchTiming = 'all',
    timeCondition = 'all',
    timeThreshold = 5
  } = filters;

  // If no filters are active, return all rows
  if (punchType === 'all' && punchTiming === 'all' && timeCondition === 'all' && !showOpenPunchesOnly) {
    return rows;
  }

  return rows.filter(row => {
    // Apply open punches filter if specified
    if (showOpenPunchesOnly && row.timeOut !== "Open Punch") {
      return false;
    }

    // Get the relevant variance value based on punch type
    let varianceValue: number | null = null;

    if (punchType === 'clockIn') {
      varianceValue = row.clockInVariance;
    } else if (punchType === 'clockOut') {
      varianceValue = row.clockOutVariance;
    } else {
      // For "all", check if either clock-in or clock-out matches the criteria
      const clockInVariance = row.clockInVariance;
      const clockOutVariance = row.clockOutVariance;

      // Apply timing filter (different logic for clock-in vs clock-out)
      const matchesClockInTiming = (variance: number | null) => {
        if (variance === null && punchTiming !== 'all') return false; // Only exclude null if timing filter is active
        if (variance === null) return true; // If variance is null but no timing filter, pass through
        if (punchTiming === 'early') return variance > 0; // Early clock-in = positive variance (more work)
        if (punchTiming === 'late') return variance < 0;  // Late clock-in = negative variance (less work)
        return true; // "all"
      };

      const matchesClockOutTiming = (variance: number | null) => {
        if (variance === null && punchTiming !== 'all') return false; // Only exclude null if timing filter is active
        if (variance === null) return true; // If variance is null but no timing filter, pass through
        if (punchTiming === 'early') return variance < 0; // Early clock-out = negative variance (less work)
        if (punchTiming === 'late') return variance > 0;  // Late clock-out = positive variance (more work)
        return true; // "all"
      };

      // Apply time condition filter
      const matchesCondition = (variance: number | null) => {
        if (variance === null && timeCondition !== 'all') return false; // Only exclude null if condition filter is active
        if (variance === null) return true; // If variance is null but no condition filter, pass through
        const absVariance = Math.abs(variance);
        if (timeCondition === 'lessThan') return absVariance < timeThreshold;
        if (timeCondition === 'greaterThan') return absVariance > timeThreshold;
        return true; // "all"
      };

      return (matchesClockInTiming(clockInVariance) && matchesCondition(clockInVariance)) ||
             (matchesClockOutTiming(clockOutVariance) && matchesCondition(clockOutVariance));
    }

    // For specific punch types, apply filters to that variance
    // Only exclude null variance if we're actually applying variance filters
    if (varianceValue === null) return false;

    // Apply timing filter (different logic for clock-in vs clock-out)
    if (punchTiming !== 'all') {
      if (punchType === 'clockIn') {
        if (punchTiming === 'early' && varianceValue <= 0) return false; // Early clock-in should be positive
        if (punchTiming === 'late' && varianceValue >= 0) return false;  // Late clock-in should be negative
      } else if (punchType === 'clockOut') {
        if (punchTiming === 'early' && varianceValue >= 0) return false; // Early clock-out should be negative
        if (punchTiming === 'late' && varianceValue <= 0) return false;  // Late clock-out should be positive
      }
    }

    // Apply time condition filter
    if (timeCondition !== 'all') {
      const absVariance = Math.abs(varianceValue);
      if (timeCondition === 'lessThan' && absVariance >= timeThreshold) return false;
      if (timeCondition === 'greaterThan' && absVariance <= timeThreshold) return false;
    }

    return true;
  });
}
