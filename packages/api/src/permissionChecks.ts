import {
  CheckAllowed,
  isAllowedToMakeChanges,
  isPersonShiftLeadOnDayOfShift,
  isPersonShiftLeadOnDayOfWeek,
  UserEmploymentContext
} from "./authorization.util";
import {SecureBusinessId, SecurePersonId, SecureStoreId, SecureUserId} from "./database.types";
import * as P from "@prisma/client";
import {TRPCError} from "@trpc/server";
import {
  EmploymentWithJobAndStores,
  noteHighSensitivityLevel,
  noteLowSensitivityLevel,
  noteMediumSensitivityLevel
} from "./schema.converters";
import {DraftSchedule, PublishedSchedule, ScheduleRevision} from "./scheduleSchemas";
import {UserDto} from "./schemas";
import {Context} from "./types";
import {DateTimeRange} from "./timeSchemas";
import {map} from "lodash";
import {DateTime} from "luxon";

export function canCreateCorrectiveActions(checkAllowed: CheckAllowed, {
  businessId,
  storeEmploymentId,
  storeId
}: {
  businessId: SecureBusinessId;
  storeEmploymentId: string;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "storeEmployment/createCorrectiveAction",
    resource: `business/${businessId}/storeEmployment/${storeEmploymentId}`,
    resourceEntity: {
      storeId: storeId
    }
  }).isAllowed;
}

export function canGetCorrectiveAction(checkAllowed: CheckAllowed, {
  businessId,
  recipientStoreEmployment,
  recipientUserId,
}: {
  businessId: SecureBusinessId;
  recipientStoreEmployment: P.StoreEmployment;
  recipientUserId: string;
}): boolean {
  return checkAllowed({
    action: "storeEmployment/getCorrectiveAction",
    resource: `business/${businessId}/storeEmployment/${recipientStoreEmployment.id}`,
    resourceEntity: {
      id: recipientStoreEmployment.id,
      ownerId: recipientUserId,
      storeId: recipientStoreEmployment.storeId
    }
  }).isAllowed;
}


export function canGetActionableItem(checkAllowed: CheckAllowed, {
  businessId,
  recipientStoreEmployment,
  recipientUserId,
}: {
  businessId: SecureBusinessId;
  recipientStoreEmployment: P.StoreEmployment;
  recipientUserId: string;
}): boolean {
  return checkAllowed({
    action: "storeEmployment/getActionableItem",
    resource: `business/${businessId}/storeEmployment/${recipientStoreEmployment.id}`,
    resourceEntity: {
      id: recipientStoreEmployment.id,
      ownerId: recipientUserId,
      storeId: recipientStoreEmployment.storeId
    }
  }).isAllowed;
}

export function throwAuthErrorIfFalse(condition: boolean, message: string = "You are not authorized to perform this action") {
  if (!condition) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: message,
    });
  }
}

export function canViewOthersNotes(checkAllowed: CheckAllowed, {businessId, personId, storeId}: {
  businessId: SecureBusinessId;
  personId: string;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/viewOthersNotes",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      id: storeId,
      storeId: storeId,
    }
  }).isAllowed;
}

type ListNotesParams = {
  businessId: SecureBusinessId;
  personId: string;
  requestingPersonId: string;
  storeId: SecureStoreId;
};

function internalCanListNotesImpl(checkAllowed: CheckAllowed, canListNotesAction: string, {
  businessId,
  personId,
  requestingPersonId,
  storeId
}: ListNotesParams): boolean {
  const canList = checkAllowed({
    action: canListNotesAction,
    resource: `business/${businessId}/person/${personId}`,
    resourceEntity: {}
  }).isAllowed;

  // if the requesting person is viewing a different person's notes, they need to have view other notes permission
  if (requestingPersonId !== personId) {
    const canViewOthers = canViewOthersNotes(checkAllowed, {
      businessId,
      personId,
      storeId: storeId,
    });

    return canList && canViewOthers;
  }

  return canList;
}

export function canListLowSensitivityNotes(checkAllowed: CheckAllowed, params: ListNotesParams): boolean {
  return internalCanListNotesImpl(checkAllowed, "person/listNotesLow", params);
}

export function canListMediumSensitivityNotes(checkAllowed: CheckAllowed, params: ListNotesParams): boolean {
  return internalCanListNotesImpl(checkAllowed, "person/listNotesMedium", params);
}

export function canListHighSensitivityNotes(checkAllowed: CheckAllowed, params: ListNotesParams): boolean {
  return internalCanListNotesImpl(checkAllowed, "person/listNotesHigh", params)
}

export function getMaxAllowedPersonNoteSensitivityLevel(checkAllowed: CheckAllowed, params: ListNotesParams): number {
  const canLow = canListLowSensitivityNotes(checkAllowed, params)
  const canMedium = canListMediumSensitivityNotes(checkAllowed, params)
  const canHigh = canListHighSensitivityNotes(checkAllowed, params)

  return canHigh
    ? noteHighSensitivityLevel
    : canMedium
      ? noteMediumSensitivityLevel
      : canLow
        ? noteLowSensitivityLevel
        : 0;
}

export function canListPersonNote(note: { sensitivityLevel: number }, {
  checkAllowed,
  businessId,
  personId,
  storeId,
  requestingPersonId
}: {
  checkAllowed: CheckAllowed;
  businessId: SecureBusinessId;
  personId: string;
  requestingPersonId: string;
  storeId: SecureStoreId;
}): boolean {
  const maxSensitivityLevel = getMaxAllowedPersonNoteSensitivityLevel(checkAllowed, {
    businessId,
    personId: personId,
    requestingPersonId,
    storeId,
  });

  return note.sensitivityLevel <= maxSensitivityLevel;
}

export function canGetPerson(checkAllowed: CheckAllowed, {businessId, storeId, personId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
  personId: string;
}): boolean {
  return checkAllowed({
    action: "person/get",
    resource: `business/${businessId}/person/${personId}`,
    resourceEntity: {
      businessId: businessId,
      storeId: storeId,
    }
  }).isAllowed
}

export function canListCoreValueScores(checkAllowed: CheckAllowed, {
  businessId,
  personId,
  userId,
  storeId,
}: {
  businessId: SecureBusinessId;
  personId: string;
  userId: SecureUserId | undefined;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "person/listCoreValueScores",
    resource: `business/${businessId}/person/${personId}`,
    resourceEntity: {
      ownerId: userId,
      storeId: storeId,
    }
  }).isAllowed;
}

export function canListCorrectiveActions(checkAllowed: CheckAllowed, {
  businessId,
  storeEmploymentId,
  userId,
  storeId,
}: {
  businessId: SecureBusinessId;
  storeEmploymentId: string | undefined;
  userId: SecureUserId | undefined;
  storeId: SecureStoreId;
}): boolean {
  if (!storeEmploymentId) return false;

  return checkAllowed({
    action: "storeEmployment/listCorrectiveActions",
    resource: `business/${businessId}/storeEmployment/${storeEmploymentId}`,
    resourceEntity: {
      ownerId: userId,
      storeId: storeId,
    }
  }).isAllowed;
}

export function canListActionableItems(checkAllowed: CheckAllowed, {
  businessId,
  storeEmploymentId,
  userId,
  storeId,
}: {
  businessId: SecureBusinessId;
  storeEmploymentId: string | undefined;
  userId: SecureUserId | undefined;
  storeId: SecureStoreId;
}): boolean {
  if (!storeEmploymentId) return false;

  return checkAllowed({
    action: "storeEmployment/listActionableItems",
    resource: `business/${businessId}/storeEmployment/${storeEmploymentId}`,
    resourceEntity: {
      ownerId: userId,
      storeId: storeId,
    }
  }).isAllowed;
}

export function canGetEmergencyContact(checkAllowed: CheckAllowed, {
  businessId,
  personId,
  userId,
}: {
  businessId: SecureBusinessId;
  personId: string;
  userId: SecureUserId | undefined;
}): boolean {
  return checkAllowed({
    action: "person/getEmergencyContact",
    resource: `business/${businessId}/person/${personId}`,
    resourceEntity: {
      businessId,
      ownerId: userId,
    }
  }).isAllowed;
}

export function canViewSchedules(checkAllowed: CheckAllowed, {
  businessId,
  storeId,
}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/getPublishedSchedule",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      id: storeId,
      storeId: storeId,
    }
  }).isAllowed;
}

export function canViewShiftDetails(checkAllowed: CheckAllowed, {
  businessId,
  storeId,
  shiftAssignedPersonId,
  currentPersonId,
}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
  shiftAssignedPersonId: string | undefined;
  currentPersonId: SecurePersonId;
}): boolean {
  const canViewScheds = canViewSchedules(checkAllowed, {
    businessId,
    storeId,
  });

  // user can view their own shifts or view other people's shifts if they have permission to view schedules
  return shiftAssignedPersonId === currentPersonId || canViewScheds
}

export function canViewAvailability(checkAllowed: CheckAllowed, {
  businessId,
  storeId,
}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/listAvailabilityRequests",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canApproveAvailability(checkAllowed: CheckAllowed, {
  businessId,
  storeId,
}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/approveAvailabilityRequest",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canEditPhoto(checkAllowed: CheckAllowed, {
  businessId,
  personId,
  userId,
}: {
  businessId: SecureBusinessId;
  personId: SecurePersonId;
  userId: SecureUserId | undefined;
}): boolean {
  return checkAllowed({
    action: "person/uploadImage",
    resource: `business/${businessId}/person/${personId}`,
    resourceEntity: {
      id: personId,
      businessId: businessId,
      ownerId: userId,
    }
  }).isAllowed;
}

export function canEditPerson(checkAllowed: CheckAllowed, {
  businessId,
  personId,
  userId,
}: {
  businessId: SecureBusinessId;
  personId: SecurePersonId;
  userId: SecureUserId | undefined;
}): boolean {
  return checkAllowed({
    action: "person/update",
    resource: `business/${businessId}/person/${personId}`,
    resourceEntity: {
      id: personId,
      businessId: businessId,
      ownerId: userId,
    }
  }).isAllowed;
}

export function canEditCoreValues(checkAllowed: CheckAllowed, {
  businessId,
  personId,
  userId,
}: {
  businessId: SecureBusinessId;
  personId: string;
  userId: SecureUserId | undefined;
}): boolean {
  return checkAllowed({
    action: "person/createCoreValueScore",
    resource: `business/${businessId}/person/${personId}`,
    resourceEntity: {
      ownerId: userId,
    }
  }).isAllowed;
}

export function canEditPermissions(checkAllowed: CheckAllowed, {
  businessId,
  employmentOrEmploymentRequestId,
  userId,
}: {
  businessId: SecureBusinessId;
  employmentOrEmploymentRequestId: string;
  userId: SecureUserId | undefined;
}): boolean {
  return checkAllowed({
    action: "employment/updatePermissionPolicy",
    resource: `business/${businessId}/employment/${employmentOrEmploymentRequestId}`,
    resourceEntity: {
      id: employmentOrEmploymentRequestId,
      ownerId: userId,
    }
  }).isAllowed;
}

export function canEditPositionScores(checkAllowed: CheckAllowed, {
  businessId,
  storeEmploymentId,
  storeId,
}: {
  businessId: SecureBusinessId;
  storeEmploymentId: string;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "storeEmployment/createPositionScore",
    resource: `business/${businessId}/storeEmployment/${storeEmploymentId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canEditPositionTraining(checkAllowed: CheckAllowed, {
  businessId,
  storeEmploymentId,
  storeId,
  userId,
}: {
  businessId: SecureBusinessId;
  storeEmploymentId: string;
  storeId: SecureStoreId;
  userId: SecureUserId;
}): boolean {
  return checkAllowed({
    action: "storeEmployment/putPositionTraining",
    resource: `business/${businessId}/storeEmployment/${storeEmploymentId}`,
    resourceEntity: {
      storeId: storeId,
      ownerId: userId,
    }
  }).isAllowed;
}

export function canEditProficiencyRanking(checkAllowed: CheckAllowed, {
  businessId,
  storeEmploymentId,
  storeId,
  userId,
}: {
  businessId: SecureBusinessId;
  storeEmploymentId: string;
  storeId: SecureStoreId;
  userId: SecureUserId | undefined;
}): boolean {
  return checkAllowed({
    action: "storeEmployment/putProficiencyScore",
    resource: `business/${businessId}/storeEmployment/${storeEmploymentId}`,
    resourceEntity: {
      id: storeEmploymentId,
      storeId: storeId,
      ownerId: userId,
    }
  }).isAllowed;
}

export function canEditTraining(checkAllowed: CheckAllowed, {
  businessId,
  storeEmploymentId,
  storeId,
  userId,
}: {
  businessId: SecureBusinessId;
  storeEmploymentId: string;
  storeId: SecureStoreId;
  userId: SecureUserId | undefined;
}): boolean {
  return checkAllowed({
    action: "storeEmployment/putPositionTraining",
    resource: `business/${businessId}/storeEmployment/${storeEmploymentId}`,
    resourceEntity: {
      storeId: storeId,
      ownerId: userId,
    }
  }).isAllowed;
}

export function canViewPositionScores(checkAllowed: CheckAllowed, {
  businessId,
  storeEmploymentId,
  userId,
  storeId,
}: {
  businessId: SecureBusinessId;
  storeEmploymentId: string;
  userId: SecureUserId | undefined;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "storeEmployment/getPositionScores",
    resource: `business/${businessId}/storeEmployment/${storeEmploymentId}`,
    resourceEntity: {
      ownerId: userId,
      storeId: storeId,
    }
  }).isAllowed;
}

export function canCreateActionableItems(checkAllowed: CheckAllowed, {
  businessId,
  storeEmploymentId,
  storeId,
}: {
  businessId: SecureBusinessId;
  storeEmploymentId: string;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "storeEmployment/createActionableItem",
    resource: `business/${businessId}/storeEmployment/${storeEmploymentId}`,
    resourceEntity: {
      storeId: storeId
    }
  }).isAllowed;
}

export function canCreateGeneralNotes(checkAllowed: CheckAllowed, {
  businessId,
  personId,
  userId,
}: {
  businessId: SecureBusinessId;
  personId: SecurePersonId;
  userId: SecureUserId | undefined;
}): boolean {
  return checkAllowed({
    action: "person/createNote",
    resource: `business/${businessId}/person/${personId}`,
    resourceEntity: {
      id: personId,
      businessId: businessId,
      ownerId: userId,
    }
  }).isAllowed;
}

export function canApprovePeople(checkAllowed: CheckAllowed, {
  businessId,
  storeId,
}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "employmentRequest/approve",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      id: storeId,
      storeId: storeId,
    }
  }).isAllowed;
}

export function canGetPositionTraining(checkAllowed: CheckAllowed, {
  businessId,
  storeEmploymentId,
  userId,
  storeId,
}: {
  businessId: SecureBusinessId;
  storeEmploymentId: string | undefined;
  userId: SecureUserId | undefined;
  storeId: SecureStoreId;
}): boolean {
  if (!storeEmploymentId) return false;
  return checkAllowed({
    action: "storeEmployment/getPositionTraining",
    resource: `business/${businessId}/storeEmployment/${storeEmploymentId}`,
    resourceEntity: {
      ownerId: userId,
      storeId: storeId,
    }
  }).isAllowed;
}

export function canGetProficiency(checkAllowed: CheckAllowed, {
  businessId,
  storeEmploymentId,
  userId,
  storeId,
}: {
  businessId: SecureBusinessId;
  storeEmploymentId: string | undefined;
  userId: SecureUserId | undefined;
  storeId: SecureStoreId;
}): boolean {
  if (!storeEmploymentId) return false;
  return checkAllowed({
    action: "storeEmployment/getProficiency",
    resource: `business/${businessId}/storeEmployment/${storeEmploymentId}`,
    resourceEntity: {
      ownerId: userId,
      storeId: storeId,
    }
  }).isAllowed;
}

export function canCreateShiftLeaderFeedback(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/createShiftLeadFeedback",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canViewShiftLeaderFeedback(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/getShiftLeadFeedback",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function hasPermissionToGetShiftLeadNotes(checkAllowed: CheckAllowed, {
  businessId,
  storeId,
}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/getShiftLeadNotes",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canGetShiftLeadNotes(checkAllowed: CheckAllowed, {
  businessId,
  storeId,
  schedule,
  currentPersonId,
  dayOfWeek
}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
  schedule: PublishedSchedule | undefined;
  currentPersonId: SecurePersonId;
  dayOfWeek: number;
}): boolean {
  const hasPermission = hasPermissionToGetShiftLeadNotes(checkAllowed, {
    businessId,
    storeId
  });

  const isShiftLeadOnDay = schedule ? isPersonShiftLeadOnDayOfWeek({
    schedule: schedule,
    personId: currentPersonId,
    weekday: dayOfWeek
  }) : false;

  return hasPermission || isShiftLeadOnDay;
}

export function canGetScheduleNotes(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/getScheduleNotes",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canCreateShiftLeadNote(checkAllowed: CheckAllowed, {
  businessId,
  storeId,
  schedule,
  currentPersonId,
  dayOfWeek
}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
  schedule: PublishedSchedule | undefined;
  currentPersonId: SecurePersonId;
  dayOfWeek: number;
}): boolean {
  const isShiftLeadOnDay = schedule ? isPersonShiftLeadOnDayOfWeek({
    schedule: schedule,
    personId: currentPersonId,
    weekday: dayOfWeek
  }) : false;

  const hasPermission = checkAllowed({
    action: "store/createShiftLeadNotes",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;

  return hasPermission || isShiftLeadOnDay;
}

export function canApproveShiftSwap(checkAllowed: CheckAllowed, {
  businessId,
  offerorShiftId,
  offereeShiftId,
  storeId,
  schedule,
  personId,
}: {
  businessId: SecureBusinessId;
  offerorShiftId: string;
  offereeShiftId: string;
  storeId: SecureStoreId;
  schedule: PublishedSchedule | undefined;
  personId: SecurePersonId;
}): boolean {
  if (!schedule) {
    return false;
  }

  const isShiftLeadDayOfOfferorShift = isPersonShiftLeadOnDayOfShift({
    schedule,
    personId: personId,
    shiftId: offerorShiftId,
  })
  const isShiftLeadDayOfOffereeShift = isPersonShiftLeadOnDayOfShift({
    schedule,
    personId,
    shiftId: offereeShiftId,
  })

  const hasPermission = checkAllowed({
    action: "store/approveShiftSwap",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;

  return hasPermission || isShiftLeadDayOfOfferorShift || isShiftLeadDayOfOffereeShift;
}

export function canApproveShiftOffer(checkAllowed: CheckAllowed, {
  businessId,
  shiftId,
  storeId,
  schedule,
  personId,
}: {
  businessId: SecureBusinessId;
  shiftId: string;
  storeId: SecureStoreId;
  schedule: PublishedSchedule | undefined;
  personId: SecurePersonId;
}): boolean {
  if (!schedule) {
    return false;
  }

  // check if the person is a shift lead for that day
  const isShiftLeadDayOfShift = isPersonShiftLeadOnDayOfShift({
    schedule: schedule,
    personId: personId,
    shiftId: shiftId,
  })

  const hasPermission = checkAllowed({
    action: "store/approveShiftOffer",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;

  // They can approve the shift if they have permission to approve shifts, or if they are a shift lead for that day
  return hasPermission || isShiftLeadDayOfShift;
}

export function canCreateStoreResource(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/createResource",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canUploadTimePunchFile(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/uploadTimePunchFile",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canCreateDataFiles(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/createDataFile",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      businessId: businessId,
      storeId: storeId,
    }
  }).isAllowed;
}

export function canListDataFiles(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/listDataFiles",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      businessId: businessId,
      storeId: storeId
    }
  }).isAllowed;
}

export function canPublishSchedule(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/publishSchedule",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canPublishScheduleAsShiftLead(checkAllowed: CheckAllowed, {
  businessId,
  storeId,
  schedule,
  draft,
  user
}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
  schedule: ScheduleRevision;
  draft: DraftSchedule;
  user: UserDto;
}): boolean {
  const isAllowedToEditAffectedDays = isAllowedToMakeChanges({
    scheduleRevision: schedule,
    original: schedule.published,
    draft: draft,
    user
  });
  const hasPublishSchedulePermission = canPublishSchedule(checkAllowed, {businessId, storeId});

  return isAllowedToEditAffectedDays || hasPublishSchedulePermission;
}

export function canViewPayRates(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/getPayRates",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canViewInsights(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/viewInsightsStreet",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canViewSchedulingInsights(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/viewSchedulingInsights",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canViewTraining(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "storeEmployment/getPositionTraining",
    resource: `business/${businessId}/storeEmployment/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canUpdatePayRates(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/updatePayRate",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canDeleteChecklists(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "checklist/delete",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId
    }
  }).isAllowed
}

export function canUpdateChecklistSeries(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "checklist/updateSeries",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId
    }
  }).isAllowed
}

export function canUpdateChecklist(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "checklist/update",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;
}

export function canAssignChecklist(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "checklist/assign",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    },
  }).isAllowed;
}

export function canArchiveChecklist(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "checklist/archive",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    },
  }).isAllowed;
}

export function canListTimeOffRequests(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/listTimeOffRequests",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed
}

export function canAddStoreDevice(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/addDevice",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed
}

export function canRemoveStoreDevice(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/removeDevice",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed
}

export function canListStoreDevices(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/listDevices",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed
}

export function canUpdateStoreDevice(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/updateDevice",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed
}

export function canManageStoreSubscription(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "store/manageSubscription",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed
}

export function canArchiveChatChannel(checkAllowed: CheckAllowed, {businessId}: {
  businessId: SecureBusinessId;
}): boolean {
  return checkAllowed({
    action: "chat/archiveChannel",
    resource: `business/${businessId}`,
  }).isAllowed
}

export function isAnOperator(principalEmployment: EmploymentWithJobAndStores): boolean {
  return principalEmployment.job.createdFromTemplateId === "operator";
}

export function canCreateChatChannel(checkAllowed: CheckAllowed, {businessId}: {
  businessId: SecureBusinessId;
}): boolean {
  return checkAllowed({
    action: "chat/createChannel",
    resource: `business/${businessId}`,
  }).isAllowed
}

export function canCreateChecklistTemplate(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "checklist/create",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed
}

export function canGetChecklistTemplates(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "checklist/getTemplates",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed
}

export function canEditChecklistTemplates(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "checklist/update",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed
}

export function canActivateChecklistTemplates(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "checklist/activate",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed
}

/**
 * Can only edit recurring checklists if a checklist manager
 * @param checkAllowed
 * @param businessId
 * @param storeId
 */
export function canEditRecurringChecklist(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {

  return checkAllowed({
    action: "checklist/update",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed
}

export function canGetLiveManagerChecklists(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "checklist/getActive",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed
}

export function canGetUpcomingChecklists(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "checklist/getUpcoming",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed
}

export function canGetRecordedChecklists(checkAllowed: CheckAllowed, {businessId, storeId}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
}): boolean {
  return checkAllowed({
    action: "checklist/getRecorded",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed
}

/**
 * Can edit if a shift leader on that day or is a checklist manager
 * @param checkAllowed
 * @param businessId
 * @param storeId
 * @param currentPersonId
 * @param schedule
 * @param dayOfWeek
 */
export function canEditChecklist(checkAllowed: CheckAllowed, {businessId, storeId, currentPersonId, schedule, dayOfWeek}: {
  businessId: SecureBusinessId;
  storeId: SecureStoreId;
  schedule: PublishedSchedule | undefined;
  currentPersonId: SecurePersonId;
  dayOfWeek: number;
}): boolean {
  const isShiftLeadOnDay = schedule ? isPersonShiftLeadOnDayOfWeek({
    schedule: schedule,
    personId: currentPersonId,
    weekday: dayOfWeek
  }) : false;

  const isChecklistManager = checkAllowed({
    action: "checklist/update",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed

  return isShiftLeadOnDay || isChecklistManager;
}


export async function canGetChecklistsForShiftLeader(ctx: Context & UserEmploymentContext, {timezone, storeId, range}: {
  timezone: string;
  storeId: SecureStoreId;
  range: DateTimeRange;
}) {
  const businessId = ctx.businessId;

  // if user has shift leader console permsissions, then they can see all events
  const hasShiftLeaderConsolePermissions = ctx.checkAllowed({
    action: "store/viewShiftLeaderConsoleStreet",
    resource: `business/${businessId}/store/${storeId}`,
    resourceEntity: {
      storeId: storeId,
    }
  }).isAllowed;

  // if user doesn't have permission but is a shift leader, they can see events for the day of their shift leader shift
  const shiftLeaderShifts = await ctx.db.scheduling.getShiftLeaderShiftsForPerson(storeId, ctx.currentPersonId, range)
  const shiftLeaderDayRanges = map(shiftLeaderShifts, s => {
    const start = DateTime.fromJSDate(s.startAbs!, {zone: timezone}).startOf("day");
    const end = DateTime.fromJSDate(s.endAbs!, {zone: timezone}).endOf("day");
    return {
      start: start.toJSDate(),
      end: end.toJSDate()
    }
  });

  return {
    hasShiftLeaderConsolePermissions,
    shiftLeaderDayRanges
  }
}
