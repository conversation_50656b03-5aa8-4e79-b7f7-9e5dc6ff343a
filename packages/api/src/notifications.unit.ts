import {beforeEach, describe, expect, it, vi} from 'vitest';
import {Context} from './types';
import {notify<PERSON>erson} from "./notifications";
import {NotificationCode} from "./notifications.types";
import {MockTelnyx} from "./testUtil";

const defaultNotificationSettings = {
  smsEnabled: false,
  pushEnabled: false,
  receiveScheduleUpdates: false,
  receiveShiftOfferUpdates: false,
  receiveActionableItemUpdates: false,
  receiveChecklistUpdates: false,
  receiveAdminShiftAndScheduleUpdates: false,
  receiveFeedbackUpdates: false,
  receiveScheduleUpdatesSMS: false,
  receiveShiftOfferUpdatesSMS: false,
  receiveActionableItemUpdatesSMS: false,
  receiveChecklistUpdatesSMS: false,
  receiveFeedbackUpdatesSMS: false,
  receiveAdminShiftAndScheduleUpdatesSMS: false
};

describe('notifyPerson', () => {
  let mockCtx: Pick<Context, 'courier' | 'sendEmail' | 'prisma' | 'pushNotificationService' | 'telnyx' | 'telnyxFromNumber'>;
  let mockOnError: (error: string) => void;
  let mockPrismaCreate: any;
  let mockPrismaUpdate: any;

  beforeEach(() => {
    mockPrismaCreate = vi.fn().mockResolvedValue({});
    mockPrismaUpdate = vi.fn().mockResolvedValue({});

    mockCtx = {
      courier: {
        send: vi.fn().mockResolvedValue(undefined),
      } as any,
      sendEmail: vi.fn().mockResolvedValue(undefined),
      prisma: {
        notificationSend: {
          create: mockPrismaCreate,
          update: mockPrismaUpdate,
        },
      } as any,
      pushNotificationService: {
        sendNotification: vi.fn().mockResolvedValue(undefined),
      },
      telnyx: MockTelnyx(),
      telnyxFromNumber: "+11231231234"
    };
    mockOnError = vi.fn();
  });

  describe('notification records', () => {
    it('should create shift notification with correct code and ID', async () => {
      const storeId = "s_abcdef";
      const shiftId = "shift123";
      const isoDate = "2023-01";
      await notifyPerson({
        ctx: mockCtx,
        person: {id: "person1", phoneNumber: '1234567890', user: {devicePushTokens: []}, notificationSettings: []},
        message: 'Your shift has been assigned',
        subject: 'New Shift Assignment',
        deduplicationId: "dedup123",
        code: NotificationCode.shift.assigned,
        entityIds: {storeId, shiftId, isoDate}
      });

      expect(mockPrismaCreate).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          code: "NC_shiftAssigned",
          shiftId,
          storeId,
          isoDate,
          isRead: false,
          isArchived: false
        })
      }));
    });

    it('should create shift offer notification with all related data', async () => {
      const shiftOfferId = "shiftOffer123";

      await notifyPerson({
        ctx: mockCtx,
        person: {id: "person2", email: '<EMAIL>', user: {devicePushTokens: []}, notificationSettings: []},
        message: 'New shift offer available',
        subject: 'Shift Offer',
        deduplicationId: "dedup124",
        code: NotificationCode.shiftOffer.available,
        entityIds: {
          shiftOfferId,
          storeId: "store123" // optional field
        }
      });

      expect(mockPrismaCreate).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          code: "NC_shiftOfferAvailable",
          shiftOfferId,
          storeId: "store123",
          isRead: false,
          isArchived: false
        })
      }));
    });

    it('should create checklist notification with event ID', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {id: "person4", phoneNumber: '1234567890', user: {devicePushTokens: []}, notificationSettings: []},
        message: 'Checklist overdue',
        subject: 'Overdue Checklist',
        deduplicationId: "dedup126",
        code: NotificationCode.checklist.overdue,
        entityIds: {
          storeId: "store123",
          checklistEventId: "checklist123"
        }
      });

      expect(mockPrismaCreate).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          code: "NC_checkListOverdue",
          checklistEventId: "checklist123",
          isRead: false,
          isArchived: false
        })
      }));
    });

    it('should create availability notification with correct data', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {id: "person5", email: '<EMAIL>', user: {devicePushTokens: []}, notificationSettings: []},
        message: 'Availability approved',
        subject: 'Availability Update',
        deduplicationId: "dedup127",
        code: NotificationCode.availability.approved,
        entityIds: {
          storeId: "store123",
          availabilityId: "avail123"
        }
      });

      expect(mockPrismaCreate).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          code: "NC_availabilityApproved",
          availabilityId: "avail123",
          isRead: false,
          isArchived: false
        })
      }));
    });

    it('should create uncategorized notification without entityIds', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {id: "person6", phoneNumber: '1234567890', user: {devicePushTokens: []}, notificationSettings: []},
        message: 'Generic notification',
        subject: 'Notice',
        deduplicationId: "dedup128",
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"}
      });

      expect(mockPrismaCreate).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          code: "NC_uncategorized",
          isRead: false,
          isArchived: false
        })
      }));
    });
  });

  // Transport Tests
  describe('transport functionality', () => {
    it('should send sms when transport is sms and phone number is available', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {id: "person1", phoneNumber: '1234567890', user: {devicePushTokens: []}, notificationSettings: []},
        message: 'Test message',
        overrideTransports: new Set(['sms']),
        subject: 'Test subject',
        onError: mockOnError,
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"},
      });

      expect(mockCtx.telnyx.messages.create).toHaveBeenCalledWith({
        from: mockCtx.telnyxFromNumber,
        to: '1234567890',
        subject: 'Test subject',
        text: 'Test message',
        auto_detect: true,
        use_profile_webhooks: false,
        type: 'SMS',
      });
      expect(mockCtx.sendEmail).not.toHaveBeenCalled();
      expect(mockCtx.sendEmail).not.toHaveBeenCalled();
      expect(mockOnError).not.toHaveBeenCalled();
    });

    it('should send email when transport is email and email is available', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {id: "person2", email: '<EMAIL>', user: {devicePushTokens: []}, notificationSettings: []},
        message: 'Test message',
        subject: 'Test subject',
        overrideTransports: new Set(['email']),
        onError: mockOnError,
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"},
      });

      expect(mockCtx.sendEmail).toHaveBeenCalledWith({
        to: ['<EMAIL>'],
        subject: 'Test subject',
        bodyHtml: 'Test message',
      });
      expect(mockCtx.telnyx.messages.create).not.toHaveBeenCalled();
      expect(mockOnError).not.toHaveBeenCalled();
    });

    it('should use messageHtml for email if provided', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {id: "person3", email: '<EMAIL>', user: {devicePushTokens: []}, notificationSettings: []},
        message: 'Test message',
        messageHtml: '<p>Test HTML message</p>',
        subject: 'Test subject',
        overrideTransports: new Set(['email']),
        onError: mockOnError,
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"},
      });

      expect(mockCtx.sendEmail).toHaveBeenCalledWith({
        to: ['<EMAIL>'],
        subject: 'Test subject',
        bodyHtml: '<p>Test HTML message</p>',
      });
    });

    it('should send both phone and email when both transports are specified', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person4",
          phoneNumber: '1234567890',
          email: '<EMAIL>',
          user: {devicePushTokens: []},
          notificationSettings: []
        },
        message: 'Test message',
        subject: 'Test subject',
        overrideTransports: new Set(['push', 'sms', 'email']),
        onError: mockOnError,
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"},
      });

      expect(mockCtx.telnyx.messages.create).toHaveBeenCalled();
      expect(mockCtx.sendEmail).toHaveBeenCalled();
      expect(mockOnError).not.toHaveBeenCalled();
    });

    it('should not send phone when transport is phone but no phone number', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {id: "person5", email: '<EMAIL>', user: {devicePushTokens: []}, notificationSettings: []},
        message: 'Test message',
        subject: 'Test subject',
        overrideTransports: new Set(['sms']),
        onError: mockOnError,
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"},
      });

      expect(mockCtx.telnyx.messages.create).not.toHaveBeenCalled();
      expect(mockCtx.sendEmail).not.toHaveBeenCalled();
      expect(mockOnError).not.toHaveBeenCalled();
    });

    it('should not send email when transport is email but no email', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {id: "person6", phoneNumber: '1234567890', user: {devicePushTokens: []}, notificationSettings: []},
        message: 'Test message',
        subject: 'Test subject',
        overrideTransports: new Set(['email']),
        onError: mockOnError,
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"},
      });

      expect(mockCtx.telnyx.messages.create).not.toHaveBeenCalled();
      expect(mockCtx.sendEmail).not.toHaveBeenCalled();
      expect(mockOnError).not.toHaveBeenCalled();
    });

    it('should only send push notification when no transports or preferences specified', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person384",
          email: '<EMAIL>',
          phoneNumber: '1234567890',
          user: {devicePushTokens: [{deviceToken: 'token1'}]},
          notificationSettings: []
        },
        message: 'Test message',
        subject: 'Test subject',
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"}
      });

      expect(mockCtx.pushNotificationService.sendNotification).toHaveBeenCalled();
      expect(mockCtx.telnyx.messages.create).not.toHaveBeenCalled();
      expect(mockCtx.sendEmail).not.toHaveBeenCalled();
    });

    it('should handle empty transports set', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person383",
          email: '<EMAIL>',
          phoneNumber: '1234567890',
          user: {devicePushTokens: [{deviceToken: 'token1'}]},
          notificationSettings: []
        },
        message: 'Test message',
        subject: 'Test subject',
        overrideTransports: new Set(),
        onError: mockOnError,
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"},
      });

      expect(mockCtx.pushNotificationService.sendNotification).toHaveBeenCalled();
      expect(mockCtx.sendEmail).not.toHaveBeenCalled();
      expect(mockOnError).not.toHaveBeenCalled();
    });
  });

  // Push Notification Tests
  describe('push notifications', () => {
    it('should send both SMS and push when overrideTransport is [\'push\', \'sms\']', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person9",
          phoneNumber: '1234567890',
          user: {
            devicePushTokens: [{deviceToken: 'token1'}]
          },
          notificationSettings: [defaultNotificationSettings]
        },
        message: 'Test message',
        subject: 'Test subject',
        overrideTransports: new Set(['push', 'sms']),
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"},
      });

      expect(mockCtx.telnyx.messages.create).toHaveBeenCalled();
      expect(mockCtx.pushNotificationService.sendNotification).toHaveBeenCalledWith(
        ['token1'],
        {title: 'Test subject', body: 'Test message'}
      );
    });

    it('should only send SMS when only SMS is defined in overrideTransport', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person10",
          phoneNumber: '1234567890',
          user: {
            devicePushTokens: [{deviceToken: 'token1'}]
          },
          notificationSettings: [defaultNotificationSettings]
        },
        message: 'Test message',
        subject: 'Test subject',
        overrideTransports: new Set(['sms']),
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"},
      });

      expect(mockCtx.telnyx.messages.create).toHaveBeenCalled();
      expect(mockCtx.pushNotificationService.sendNotification).not.toHaveBeenCalled();
    });

    it('should only send push when overrideTransports contains only push', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person11",
          phoneNumber: '1234567890',
          user: {
            devicePushTokens: [{deviceToken: 'token1'}]
          },
          notificationSettings: [defaultNotificationSettings]
        },
        message: 'Test message',
        subject: 'Test subject',
        overrideTransports: new Set(['push']),
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"},
      });

      expect(mockCtx.telnyx.messages.create).not.toHaveBeenCalled();
      expect(mockCtx.pushNotificationService.sendNotification).toHaveBeenCalled();
    });

    it('should only send to one transport with no notification settings', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person12",
          phoneNumber: '1234567890',
          user: {
            devicePushTokens: [{deviceToken: 'token1'}]
          },
          notificationSettings: []
        },
        message: 'Test message',
        subject: 'Test subject',
        overrideTransports: new Set(['push']),
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"},
      });

      expect(mockCtx.telnyx.messages.create).not.toHaveBeenCalled();
      expect(mockCtx.pushNotificationService.sendNotification).toHaveBeenCalled();
    });

    it('should not send push notifications when no device tokens exist', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person13",
          phoneNumber: '1234567890',
          user: {
            devicePushTokens: []
          },
          notificationSettings: [defaultNotificationSettings]
        },
        code: NotificationCode.uncategorized,
        message: 'Test message',
        subject: 'Test subject',
        overrideTransports: new Set(['push']),
        deduplicationId: null,
        entityIds: {storeId: "s_123abc"},
      });

      //expect(mockCtx.telnyx.messages.create).toHaveBeenCalled();
      expect(mockCtx.pushNotificationService.sendNotification).not.toHaveBeenCalled();
    });
  });

  describe('preference-based notifications', () => {
    it('no notification should be sent if both sms and push preference columns read false', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person1",
          phoneNumber: '1234567890',
          user: {devicePushTokens: [{deviceToken: 'token1'}]},
          notificationSettings: [{
            ...defaultNotificationSettings,
            pushEnabled: false,
            smsEnabled: false,
          }]
        },
        message: 'Test message',
        subject: 'Test subject',
        preferences: {
          pushColumn: 'pushEnabled',
          smsColumn: 'smsEnabled',
        },
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"}
      });

      expect(mockCtx.pushNotificationService.sendNotification).not.toHaveBeenCalled();
      expect(mockCtx.telnyx.messages.create).not.toHaveBeenCalled();
    });

    it('should not include email by default in preferences', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person2",
          email: '<EMAIL>',
          user: {devicePushTokens: [{deviceToken: 'token1'}]},
          notificationSettings: []
        },
        message: 'Test message',
        subject: 'Test subject',
        preferences: {},
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"}
      });

      expect(mockCtx.sendEmail).not.toHaveBeenCalled();
    });

    it('should handle undefined smsColumn in preferences', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person3",
          phoneNumber: '1234567890',
          user: {devicePushTokens: [{deviceToken: 'token1'}]},
          notificationSettings: []
        },
        message: 'Test message',
        subject: 'Test subject',
        preferences: {
          pushColumn: 'pushEnabled',
          // smsColumn intentionally omitted
          includeEmail: false
        },
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"}
      });

      expect(mockCtx.telnyx.messages.create).not.toHaveBeenCalled();
    });

    it('should handle undefined pushColumn in preferences', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person3",
          phoneNumber: '1234567890',
          user: {devicePushTokens: [{deviceToken: 'token1'}]},
          notificationSettings: [defaultNotificationSettings]
        },
        message: 'Test message',
        subject: 'Test subject',
        preferences: {
          // pushColumn intentionally omitted
          smsColumn: 'smsEnabled',
          includeEmail: false
        },
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"}
      });
      expect(mockCtx.pushNotificationService.sendNotification).not.toHaveBeenCalled();
    });

    it('should send push notification when preference pushColumn value is true', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person1",
          phoneNumber: '1234567890',
          user: {
            devicePushTokens: [{deviceToken: 'token1'}]
          },
          notificationSettings: [{
            ...defaultNotificationSettings,
            receiveActionableItemUpdates: true,
            receiveActionableItemUpdatesSMS: false
          }]
        },
        message: 'Test message',
        subject: 'Test subject',
        preferences: {
          pushColumn: 'receiveActionableItemUpdates',
          smsColumn: 'receiveActionableItemUpdatesSMS'
        },
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"}
      });

      expect(mockCtx.pushNotificationService.sendNotification).toHaveBeenCalledWith(
        ['token1'],
        {title: 'Test subject', body: 'Test message'}
      );
      expect(mockCtx.telnyx.messages.create).not.toHaveBeenCalled();
    });

    it('should send SMS when preference smsColumn value is true', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person2",
          phoneNumber: '1234567890',
          user: {
            devicePushTokens: [{deviceToken: 'token1'}]
          },
          notificationSettings: [{
            ...defaultNotificationSettings,
            receiveActionableItemUpdates: false,
            receiveActionableItemUpdatesSMS: true
          }]
        },
        message: 'Test message',
        subject: 'Test subject',
        preferences: {
          pushColumn: 'receiveActionableItemUpdates',
          smsColumn: 'receiveActionableItemUpdatesSMS'
        },
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"}
      });

      expect(mockCtx.telnyx.messages.create).toHaveBeenCalledWith({
        from: mockCtx.telnyxFromNumber,
        to: '1234567890',
        subject: 'Test subject',
        text: 'Test message',
        auto_detect: true,
        use_profile_webhooks: false,
        type: 'SMS',
      });
      expect(mockCtx.pushNotificationService.sendNotification).not.toHaveBeenCalled();
    });

    it('should send both SMS and push when both preference columns are true', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person3",
          phoneNumber: '1234567890',
          user: {
            devicePushTokens: [{deviceToken: 'token1'}]
          },
          notificationSettings: [{
            ...defaultNotificationSettings,
            receiveActionableItemUpdates: true,
            receiveActionableItemUpdatesSMS: true
          }]
        },
        message: 'Test message',
        subject: 'Test subject',
        preferences: {
          pushColumn: 'receiveActionableItemUpdates',
          smsColumn: 'receiveActionableItemUpdatesSMS'
        },
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"}
      });

      expect(mockCtx.pushNotificationService.sendNotification).toHaveBeenCalledWith(
        ['token1'],
        {title: 'Test subject', body: 'Test message'}
      );
      expect(mockCtx.telnyx.messages.create).toHaveBeenCalledWith({
        from: mockCtx.telnyxFromNumber,
        to: '1234567890',
        subject: 'Test subject',
        text: 'Test message',
        auto_detect: true,
        use_profile_webhooks: false,
        type: 'SMS',
      });
    });

    it('should not send any notifications when both preference columns are false', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person4",
          phoneNumber: '1234567890',
          user: {
            devicePushTokens: [{deviceToken: 'token1'}]
          },
          notificationSettings: [{
            ...defaultNotificationSettings,
            receiveActionableItemUpdates: false,
            receiveActionableItemUpdatesSMS: false
          }]
        },
        message: 'Test message',
        subject: 'Test subject',
        preferences: {
          pushColumn: 'receiveActionableItemUpdates',
          smsColumn: 'receiveActionableItemUpdatesSMS'
        },
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: {storeId: "s_123abc"}
      });

      expect(mockCtx.pushNotificationService.sendNotification).not.toHaveBeenCalled();
      expect(mockCtx.telnyx.messages.create).not.toHaveBeenCalled();
    });

    it('should use default transports and call onError when notification settings is empty', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person1",
          phoneNumber: '1234567890',
          user: { devicePushTokens: [{ deviceToken: 'token1' }] },
          notificationSettings: [] // Empty settings
        },
        message: 'Test message',
        subject: 'Test subject',
        preferences: {
          pushColumn: 'receiveActionableItemUpdates',
          smsColumn: 'receiveActionableItemUpdatesSMS'
        },
        onError: mockOnError,
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: { storeId: "s_123abc" }
      });

      // Verify that push notification was sent (default transport)
      expect(mockCtx.pushNotificationService.sendNotification).toHaveBeenCalledWith(
        ['token1'],
        { title: 'Test subject', body: 'Test message' }
      );

      // Verify other transports were not used
      expect(mockCtx.telnyx.messages.create).not.toHaveBeenCalled();
      expect(mockCtx.sendEmail).not.toHaveBeenCalled();
    });

    it('should immediately use default transports after error and not process preferences', async () => {
      await notifyPerson({
        ctx: mockCtx,
        person: {
          id: "person3",
          phoneNumber: '1234567890',
          email: '<EMAIL>',
          user: { devicePushTokens: [{ deviceToken: 'token1' }] },
          notificationSettings: [] // Empty settings
        },
        message: 'Test message',
        subject: 'Test subject',
        preferences: {
          pushColumn: 'receiveActionableItemUpdates',
          smsColumn: 'receiveActionableItemUpdatesSMS',
          includeEmail: true // This should be ignored after error
        },
        onError: mockOnError,
        deduplicationId: null,
        code: NotificationCode.uncategorized,
        entityIds: { storeId: "s_123abc" }
      });

      // Verify only push notification was sent (default transport)
      expect(mockCtx.pushNotificationService.sendNotification).toHaveBeenCalledWith(
        ['token1'],
        { title: 'Test subject', body: 'Test message' }
      );

      // Verify other transports were not used, even though includeEmail was true
      expect(mockCtx.sendEmail).not.toHaveBeenCalled();
      expect(mockCtx.telnyx.messages.create).not.toHaveBeenCalled();
    });
  });
});
