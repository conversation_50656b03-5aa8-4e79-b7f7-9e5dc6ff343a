import {ContextForServerSideProcess} from "./createContextForServerSideProcess";
import {SchedulePublishedEvent} from "./domainEvent.types";
import {
  calculateFutureNotificationOperations,
  calculateNotifications,
  calculateScheduleChanges
} from "./scheduleNotifications";
import {processBatchesWithConcurrency} from "./asyncBatchProcessing";
import * as Sentry from "@sentry/node";
import {notify<PERSON>erson} from "./notifications";
import {compact, filter, find, flatMap, map, some} from "lodash";
import {UTCDate} from "@date-fns/utc";
import {toStoreDto} from "./schema.converters";
import {genScheduleEventId} from "./schemas";
import {createGenerator, deleteGenerator, updateGenerator} from "./reminders";
import {
  createQstashMessage,
  deleteDbGenerator,
  deleteDbScheduledEvents,
  deleteQstashMessages,
  getScheduledEvents,
  insertGenerator,
  insertScheduledEvents,
  updateDbGenerator
} from "./effects";
import {getEmployeeStatus} from "./getEmployeeStatus";
import {getPersonStatusInfo, PersonWithStatusInfoIncludes} from "./person";

export async function handleSchedulePublishedMessage(event: SchedulePublishedEvent, ctx: ContextForServerSideProcess): Promise<true> {
  // generate notifications based on the schedule change
  const update = event.data.publishedScheduleUpdate;
  const storeId = update.newSchedule.storeId;
  const storeRow = await ctx.prisma.store.findUniqueOrThrow({
    where: {
      id: storeId
    },
    include: {
      image: true,
      address: true,
      areas: {
        where: {isActive: true},
        orderBy: {order: 'asc'},
        include: {
          storePositions: {
            // where: {isActive: true},
            orderBy: {order: 'asc'}
          }
        }
      },
    }
  });
  const store = toStoreDto({businessId: update.newSchedule.businessId, ctx, store: storeRow});
  const scheduleChanges = calculateScheduleChanges({
    nextSchedule: update.newSchedule,
    prevSchedule: update.currentSchedule,
    cancelledShiftOffers: update.cancelledShiftOffers,
  });

  const generatorPurpose = "shiftReminder";
  const currentGeneratorRows = await ctx.prisma.generator.findMany({
    where: {
      scheduleId: update.newSchedule.id,
      generatorPurpose: generatorPurpose
    },
    include: {
      personRecipients: {
        select: {
          id: true
        }
      },
    }
  });
  const currentGenerators = map(currentGeneratorRows, g => ({
    id: g.id,
    shiftId: g.shiftId ?? undefined,
    personId: g.personId ?? undefined,
  }));

  const storeTimezone = store.timezone ?? "America/New_York";
  const genOps = calculateFutureNotificationOperations({
    changes: scheduleChanges,
    now: new UTCDate(),
    timezone: storeTimezone,
    store,
    currentGenerators
  })

  const immediateNotifs = calculateNotifications({
    changes: scheduleChanges,
    now: new UTCDate(),
    timezone: storeTimezone,
    store,
    scheduleAreas: flatMap(update.newSchedule.days, d => d.areas)
  });

  const businessId = storeRow.businessId;
  const people = await ctx.prisma.person.findMany({
    where: {
      id: {
        in: compact([...map(immediateNotifs, n => n.personId), ...map(currentGenerators, n => n.personId)])
      },
      isArchived: false,
      user: {
        isSuspended: false,
      }
    },
    include: {
      user: {
        include: {
          devicePushTokens: true
        }
      },
      notificationSettings: {
        where: {
          storeId,
        }
      },
      employments: {
        where: {
          businessId,
          stores: {
            some: {}
          }
        },
        include: {
          job: true,
          userRoles: {
            include: {
              role: true
            }
          },
          stores: {
            include: {
              store: {}
            }
          }
        }
      },
      employmentRequests: {
        where: {
          job: {
            businessId
          },
          stores: {
            some: {
              isApproved: false
            }
          }
        },
        include: {
          stores: true,
          job: {
            include: {
              defaultRole: true
            }
          },
          trainingHistory: {
            include: {
              positionTraining: {
                include: {
                  storePosition: true
                }
              }
            }
          }
        }
      },
      invitations: {
        where: {
          businessId,
          sentAt: {not: null},
        }
      },
    }
  });

  const isPersonValid = (person: PersonWithStatusInfoIncludes): boolean => {
    // check if the person is active and employed at the store
    const employment = find(person.employments, e => some(e.stores, s => s.storeId === storeId) && !e.endedAt);
    const storeEmployment = find(employment?.stores, s => s.storeId === storeId);
    if (!employment || !storeEmployment) {
      // This could happen if the person got terminated but they're still on the schedule for the week and
      // the scheduler is trying to publish an incremental change to the schedule.
      Sentry.captureMessage("Person is not employed at the store for this schedule. Not sending notification.", {
        extra: {
          personId: person.id,
          storeId: storeId
        }
      });

      return false;
    }

    const personStatus = getEmployeeStatus(getPersonStatusInfo({person, storeId}));
    if (personStatus.label !== "Active") {
      Sentry.captureMessage("Person is not active at the store for this schedule. Not sending notification.", {
        extra: {
          personId: person.id,
          storeId: storeId
        }
      });
      return false;
    }

    return true;
  }

  // do immediate notifications first
  const notifsToSend = filter(immediateNotifs, notif => {
    if ("personId" in notif) {
      const person = find(people, p => p.id === notif.personId);
      if (!person) {
        Sentry.captureMessage("Person not found for notification. Not sending notification.", {
          extra: {
            personId: notif.personId,
            storeId: storeId
          }
        });
        return false;
      }
      return isPersonValid(person);
    }
    return true;
  })

  // send the immediate notifications
  await processBatchesWithConcurrency({
    items: notifsToSend,
    concurrency: 2,
    handleError: async (notif, e) => {
      console.error('Error sending notification', e, notif);
      Sentry.captureException(e);
    },
    batchProcessor: async (notif) => {
      const person = find(people, p => p.id === notif.personId);
      if (!person) {
        throw new Error(`Person not found for notification ${JSON.stringify(notif)}`);
      }

      await notifyPerson({
        ctx,
        ...notif,
        person,
        preferences: {
          pushColumn: "receiveScheduleUpdates",
          smsColumn: "receiveScheduleUpdatesSMS",
        },
        deduplicationId: null,
      })
    }
  })

  // sanitize the generators and notifications to only send to valid people
  const gensToApply = filter(genOps, gen => {
    if ("personId" in gen) {
      const genPerson = find(people, p => p.id === gen.personId);
      if (!genPerson) {
        Sentry.captureMessage("Person not found for generator operation. Not sending notification.", {
          extra: {
            personId: gen.personId,
            storeId: storeId
          }
        });
        return false;
      }
      return isPersonValid(genPerson);
    }
    return true;
  })

  await processBatchesWithConcurrency({
    items: gensToApply,
    concurrency: 5,
    handleError: async (gen, e) => {
      console.error('Error applying gen op', e, gen);
      Sentry.captureException(e);
    },
    batchProcessor: async (genOp) => {
      const now = new Date();

      switch (genOp.type) {
        case "createFutureNotification": {
          const gen = genOp.generator;
          await createGenerator({
            generator: gen,
            genScheduledEventId: genScheduleEventId,
            insertGenerator: (generator) => {
              return insertGenerator({
                generator,
                prisma: ctx.prisma,
                shiftId: genOp.shiftId,
                personId: genOp.personId,
                scheduleId: update.newSchedule.id,
                generatorPurpose: generatorPurpose,
              });
            },
            createQstashMessage: (event) => {
              return createQstashMessage({
                qstash: ctx.qstash,
                qstashApiUrl: ctx.qstashApiUrl,
                event
              });
            },
            now: new Date(),
            insertScheduledEvents: (events) => {
              return insertScheduledEvents({
                prisma: ctx.prisma,
                events
              });
            },
            context: {
              now: new Date(),
              timeZone: storeTimezone,
            }
          })
          return;
        }
        case "updateFutureNotification": {
          const gen = genOp.generator;
          await updateGenerator({
            now,
            generator: gen,
            genScheduledEventId: genScheduleEventId,
            updateDbGenerator: (generator) => {
              return updateDbGenerator({
                generator,
                prisma: ctx.prisma,
              });
            },
            getScheduledEvents: (generatorId) => {
              return getScheduledEvents({
                generatorId,
                prisma: ctx.prisma
              });
            },
            deleteQStashMessages: (messageIds) => {
              return deleteQstashMessages({
                qstashClient: ctx.qstash,
                messageIds
              });
            },
            deleteDbScheduledEvents: (events) => {
              return deleteDbScheduledEvents({
                events,
                prisma: ctx.prisma
              });
            },
            createQstashMessage: (event) => {
              return createQstashMessage({
                qstash: ctx.qstash,
                qstashApiUrl: ctx.qstashApiUrl,
                event
              });
            },
            insertScheduledEvents: (events) => {
              return insertScheduledEvents({
                prisma: ctx.prisma,
                events
              });
            },
            context: {
              now: now,
              timeZone: storeTimezone,
            }
          })
          return;
        }
        case "deleteFutureNotification": {
          await deleteGenerator({
            generatorId: genOp.generatorId,
            deleteDbGenerator: generatorId => {
              return deleteDbGenerator({
                generatorId,
                prisma: ctx.prisma
              });
            },
            getScheduledEvents: (generatorId) => {
              return getScheduledEvents({
                generatorId,
                prisma: ctx.prisma
              });
            },
            deleteDbScheduledEvents: (events) => {
              return deleteDbScheduledEvents({
                events,
                prisma: ctx.prisma
              });
            },
            deleteQStashMessages: (messageIds) => {
              return deleteQstashMessages({
                messageIds: messageIds,
                qstashClient: ctx.qstash
              });
            }
          });
          return;
        }
      }
    }
  })

  return true;
}
