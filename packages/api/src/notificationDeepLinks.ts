// notificationDeepLinks.ts
import {NotificationCode, NotificationEntityMap} from './notifications.types';
import {Notification} from "./notifications.schema";

type NoDeepLinkConfig = undefined;

type DeepLinkConfig = {
  screen: string;
  params: Record<string, any>;
};

type DeepLinkMap = {
  [K in keyof NotificationEntityMap]: (entityIds: NotificationEntityMap[K]) => DeepLinkConfig | NoDeepLinkConfig;
};

const deepLinkConfigs: DeepLinkMap = {
  [NotificationCode.announcement]: (_ids) => undefined,
  [NotificationCode.schedule.changed]: (ids) => ({
    screen: "/(signedin)/(drawer)/(tabs)/[storeId]/calendar",
    params: {
      storeId: ids.storeId,
    }
  }),
  [NotificationCode.shift.assigned]: (ids) => ({
    screen: "/(signedin)/(drawer)/(tabs)/[storeId]/calendar",
    params: {
      shiftId: ids.shiftId,
      storeId: ids.storeId,
      isoDate: ids.isoDate,
    }
  }),
  [NotificationCode.shift.unassigned]: (ids) => ({
    screen: "/(signedin)/(drawer)/(tabs)/[storeId]/calendar",
    params: {
      shiftId: ids.shiftId,
      storeId: ids.storeId,
      isoDate: ids.isoDate,
    }
  }),
  [NotificationCode.shift.rangeChange]: (ids) => ({
    screen: "/(signedin)/(drawer)/(tabs)/[storeId]/calendar",
    params: {
      shiftId: ids.shiftId,
      storeId: ids.storeId,
      isoDate: ids.isoDate,
    }
  }),
  [NotificationCode.shift.reminder]: (ids) => ({
    screen: "/(signedin)/(drawer)/(tabs)/[storeId]/calendar",
    params: {
      shiftId: ids.shiftId,
      storeId: ids.storeId,
      isoDate: ids.isoDate,
    }
  }),
  [NotificationCode.shiftOffer.available]: (ids) => ({
    // TODO: send them to the dashboard for now; it's the only place we're showing the shift offer atm
    screen: "/(signedin)/(drawer)/(tabs)/[storeId]",
    params: {
      storeId: ids.storeId,
      shiftOfferId: ids.shiftOfferId,
    }
  }),
  [NotificationCode.shiftOffer.accepted]: (ids) => ({
    screen: "/(signedin)/schedule/[storeId]/requests/shift-offer-details",
    params: {
      storeId: ids.storeId,
      shiftOfferId: ids.shiftOfferId,
    }
  }),
  [NotificationCode.shiftOffer.approved]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/view-shift-offer",
    params: {
      storeId: ids.storeId,
      shiftOfferId: ids.shiftOfferId,
    }
  }),
  // TODO: Show this person the updated shift?
  [NotificationCode.shiftOffer.acceptanceApproved]: (ids) => undefined,
  // Nothing to show them.  Currently not showing them their declined / cancelled offers.
  [NotificationCode.shiftOffer.declined]: (ids) => undefined,
  [NotificationCode.shiftOffer.cancelled]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/view-shift-offer",
    params: {
      storeId: ids.storeId,
      shiftOfferId: ids.shiftOfferId,
    }
  }),

  [NotificationCode.shiftSwap.available]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/shift-swap/[shiftSwapId]/view-offer",
    params: {
      storeId: ids.storeId,
      personId: ids.personId,
      shiftSwapId: ids.shiftSwapId,
    }
  }),
  [NotificationCode.shiftSwap.acceptedOfferor]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/shift-swap/[shiftSwapId]",
    params: {
      storeId: ids.storeId,
      personId: ids.personId,
      shiftSwapId: ids.shiftSwapId,
    }
  }),
  [NotificationCode.shiftSwap.rejectedOfferor]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/shift-swap/[shiftSwapId]",
    params: {
      storeId: ids.storeId,
      personId: ids.personId,
      shiftSwapId: ids.shiftSwapId,
    },
  }),
  [NotificationCode.shiftSwap.approvedOfferor]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/shift-swap/[shiftSwapId]",
    params: {
      storeId: ids.storeId,
      personId: ids.personId,
      shiftSwapId: ids.shiftSwapId,
    }
  }),
  [NotificationCode.shiftSwap.approvedOfferee]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/shift-swap/[shiftSwapId]/view-offer",
    params: {
      storeId: ids.storeId,
      personId: ids.personId,
      shiftSwapId: ids.shiftSwapId,
    }
  }),
  [NotificationCode.shiftSwap.declinedOfferor]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/shift-swap/[shiftSwapId]",
    params: {
      storeId: ids.storeId,
      personId: ids.personId,
      shiftSwapId: ids.shiftSwapId,
    },
  }),
  [NotificationCode.shiftSwap.declinedOfferee]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/shift-swap/[shiftSwapId]/view-offer",
    params: {
      storeId: ids.storeId,
      personId: ids.personId,
      shiftSwapId: ids.shiftSwapId,
    },
  }),
  [NotificationCode.shiftSwap.cancelledOfferor]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/shift-swap/[shiftSwapId]",
    params: {
      storeId: ids.storeId,
      personId: ids.personId,
      shiftSwapId: ids.shiftSwapId,
    },
  }),
  [NotificationCode.shiftSwap.cancelledOfferee]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/shift-swap/[shiftSwapId]/view-offer",
    params: {
      storeId: ids.storeId,
      personId: ids.personId,
      shiftSwapId: ids.shiftSwapId,
    },
  }),

  [NotificationCode.timeOff.approved]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/time-off-details",
    params: {
      storeId: ids.storeId,
      personId: ids.personId,
      personTimeOffId: ids.personTimeOffId,
    },
  }),
  [NotificationCode.timeOff.declined]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/time-off-details",
    params: {
      storeId: ids.storeId,
      personId: ids.personId,
      personTimeOffId: ids.personTimeOffId,
    },
  }),
  [NotificationCode.availability.approved]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/weekly-availability",
    params: {
      storeId: ids.storeId,
      availabilityId: ids.availabilityId
    }
  }),
  [NotificationCode.availability.declined]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/weekly-availability",
    params: {
      storeId: ids.storeId,
      availabilityId: ids.availabilityId
    }
  }),
  [NotificationCode.checklist.assigned]: (ids) => ({
    screen: "/(signedin)/checklist/[storeId]/event/[checklistEventId]/view/",
    params: {
      storeId: ids.storeId,
      checklistEventId: ids.checklistEventId
    },
  }),
  [NotificationCode.checklist.overdue]: (ids) => ({
    screen: "/(signedin)/checklist/[storeId]/event/[checklistEventId]/view",
    params: {
      storeId: ids.storeId,
      checklistEventId: ids.checklistEventId
    },
  }),
  [NotificationCode.checklist2.assigned]: (ids) => ({
    screen: "/(signedin)/checklist2/[storeId]/checklist/[checklistId]/view/",
    params: {
      storeId: ids.storeId,
      checklistId: ids.checklistId,
      recurrenceIdStr: ids.recurrenceId?.toISOString()
    },
  }),
  [NotificationCode.checklist2.overdue]: (ids) => ({
    screen: "/(signedin)/checklist2/[storeId]/checklist/[checklistId]/view",
    params: {
      storeId: ids.storeId,
      checklistId: ids.checklistId,
      recurrenceIdStr: ids.recurrenceId?.toISOString()
    },
  }),
  [NotificationCode.checklist2.completed]: (ids) => ({
    screen: "/(signedin)/checklist2/[storeId]/checklist/[checklistId]/view",
    params: {
      storeId: ids.storeId,
      checklistId: ids.checklistId,
      recurrenceIdStr: ids.recurrenceId?.toISOString()
    },
  }),
  [NotificationCode.checklist2.completedNonCompliant]: (ids) => ({
    screen: "/(signedin)/checklist2/[storeId]/checklist/[checklistId]/view",
    params: {
      storeId: ids.storeId,
      checklistId: ids.checklistId,
      recurrenceIdStr: ids.recurrenceId?.toISOString()
    },
  }),
  [NotificationCode.checklist2.upcomingDue]: (ids) => ({
    screen: "/(signedin)/checklist2/[storeId]/checklist/[checklistId]/view",
    params: {
      storeId: ids.storeId,
      checklistId: ids.checklistId,
      recurrenceIdStr: ids.recurrenceId?.toISOString()
    },
  }),
  [NotificationCode.setupSheet.assigned]: () => undefined,
  [NotificationCode.setupSheet.breakStarted]: () => undefined,
  [NotificationCode.setupSheet.breakSoon]: () => undefined,
  [NotificationCode.setupSheet.shiftReassigned]: () => undefined,
  [NotificationCode.teamMember.pendingApprovals]: () => undefined,
  [NotificationCode.correctiveAction.read]: () => undefined,
  [NotificationCode.correctiveAction.review]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/corrective-action/[correctiveActionId]/",
    params: {
      storeId: ids.storeId,
      personId: ids.recipientPersonId,
      correctiveActionId: ids.correctiveActionId,
    },
  }),
  [NotificationCode.correctiveAction.acknowledged]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/corrective-action/[correctiveActionId]/",
    params: {
      storeId: ids.storeId,
      personId: ids.recipientPersonId,
      correctiveActionId: ids.correctiveActionId,
    },
  }),
  [NotificationCode.business.invitation]: (_ids) => undefined,
  [NotificationCode.uncategorized]: () => undefined,
  [NotificationCode.teamMember.onboardingReminder]: () => undefined,
  [NotificationCode.positionScore.updated]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/view-position-evaluation",
    params: {
      storeId: ids.storeId,
      personId: ids.personId,
      positionId: ids.positionId,
    },
  }),
  [NotificationCode.note.created]: (ids) => ({
    screen: "/(signedin)/person/[storeId]/[personId]/note/[noteId]",
    params: {
      storeId: ids.storeId,
      personId: ids.personId,
      noteId: ids.entityNoteId,
    },
  }),
};

export function getDeepLink<T extends keyof NotificationEntityMap>(
  code: T,
  entityIds: NotificationEntityMap[T]
): DeepLinkConfig | undefined {
  const configFn = deepLinkConfigs[code];
  return configFn?.(entityIds);
}

export function getDeepLinkFromNotification(
  notification: Notification
): DeepLinkConfig | undefined {
  return getDeepLink(notification.code as keyof NotificationEntityMap, {
    ...notification,
  });
}
