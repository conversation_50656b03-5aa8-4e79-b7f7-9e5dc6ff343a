/*
  Warnings:

  - You are about to drop the column `completedAt` on the `DataFileJob` table. All the data in the column will be lost.
  - You are about to drop the column `isCompleted` on the `DataFileJob` table. All the data in the column will be lost.
  - You are about to drop the column `isError` on the `DataFileJob` table. All the data in the column will be lost.
  - You are about to drop the column `isStarted` on the `DataFileJob` table. All the data in the column will be lost.
  - You are about to drop the column `isSuccess` on the `DataFileJob` table. All the data in the column will be lost.
  - You are about to drop the column `orderedByPersonId` on the `DataFileJob` table. All the data in the column will be lost.
  - You are about to drop the `SalesForecast` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `SalesForecastDataPoint` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `timezone` to the `DataFile` table without a default value. This is not possible if the table is not empty.
  - Added the required column `createdByPersonId` to the `DataFileJob` table without a default value. This is not possible if the table is not empty.
  - Added the required column `status` to the `DataFileJob` table without a default value. This is not possible if the table is not empty.
  - Added the required column `storeId` to the `DataFileJob` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "DataFileJob" DROP CONSTRAINT "DataFileJob_orderedByPersonId_fkey";

-- DropForeignKey
ALTER TABLE "SalesForecast" DROP CONSTRAINT "SalesForecast_createdByJobId_fkey";

-- DropForeignKey
ALTER TABLE "SalesForecast" DROP CONSTRAINT "SalesForecast_storeId_fkey";

-- DropForeignKey
ALTER TABLE "SalesForecastDataPoint" DROP CONSTRAINT "SalesForecastDataPoint_salesForecastId_fkey";

-- AlterTable
ALTER TABLE "DataFile" ADD COLUMN     "archivedAt" TIMESTAMP(3),
ADD COLUMN     "archivedByPersonId" TEXT,
ADD COLUMN     "timezone" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "DataFileJob" DROP COLUMN "completedAt",
DROP COLUMN "isCompleted",
DROP COLUMN "isError",
DROP COLUMN "isStarted",
DROP COLUMN "isSuccess",
DROP COLUMN "orderedByPersonId",
ADD COLUMN     "createdByPersonId" TEXT NOT NULL,
ADD COLUMN     "rowsInserted" INTEGER,
ADD COLUMN     "status" TEXT NOT NULL,
ADD COLUMN     "storeId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "DomainEvent" ADD COLUMN     "eventOnVersion" BIGINT;

-- AlterTable
ALTER TABLE "InternalDomainEvent" ADD COLUMN     "scheduleHourlySalesForecastId" TEXT;

-- AlterTable
ALTER TABLE "SalesHourlyDataPoint" ADD COLUMN     "dataFileJobId" TEXT;

-- DropTable
DROP TABLE "SalesForecast";

-- DropTable
DROP TABLE "SalesForecastDataPoint";

-- CreateTable
CREATE TABLE "SalesForecastJob" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "storeId" TEXT NOT NULL,
    "algorithmData" JSONB,
    "createdByPersonId" TEXT NOT NULL,
    "jobType" TEXT NOT NULL,
    "dataRangeStart" TIMESTAMP(3),
    "dataRangeEnd" TIMESTAMP(3),
    "status" TEXT NOT NULL,
    "startedAt" TIMESTAMP(3),
    "successAt" TIMESTAMP(3),
    "successMessage" TEXT,
    "forecast" JSONB,
    "errorAt" TIMESTAMP(3),
    "errorCode" TEXT,
    "errorMessage" TEXT,

    CONSTRAINT "SalesForecastJob_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ScheduleHourlySalesForecast" (
    "storeId" TEXT NOT NULL,
    "scheduleId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "productivityGoal" INTEGER,
    "averagePayRate" INTEGER NOT NULL,
    "version" BIGINT NOT NULL,
    "createdFromSalesForecastJobId" TEXT,
    "dataPoints" JSONB NOT NULL,

    CONSTRAINT "ScheduleHourlySalesForecast_pkey" PRIMARY KEY ("scheduleId")
);

-- CreateIndex
CREATE INDEX "SalesForecastJob_storeId_idx" ON "SalesForecastJob"("storeId");

-- CreateIndex
CREATE INDEX "ScheduleHourlySalesForecast_scheduleId_idx" ON "ScheduleHourlySalesForecast"("scheduleId");

-- CreateIndex
CREATE INDEX "ScheduleHourlySalesForecast_storeId_scheduleId_idx" ON "ScheduleHourlySalesForecast"("storeId", "scheduleId");

-- CreateIndex
CREATE INDEX "DataFileJob_storeId_idx" ON "DataFileJob"("storeId");

-- CreateIndex
CREATE INDEX "DataFileJob_dataFileId_idx" ON "DataFileJob"("dataFileId");

-- CreateIndex
CREATE INDEX "InternalDomainEvent_scheduleHourlySalesForecastId_idx" ON "InternalDomainEvent"("scheduleHourlySalesForecastId");

-- AddForeignKey
ALTER TABLE "DataFile" ADD CONSTRAINT "DataFile_archivedByPersonId_fkey" FOREIGN KEY ("archivedByPersonId") REFERENCES "Person"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DataFileJob" ADD CONSTRAINT "DataFileJob_storeId_fkey" FOREIGN KEY ("storeId") REFERENCES "Store"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DataFileJob" ADD CONSTRAINT "DataFileJob_createdByPersonId_fkey" FOREIGN KEY ("createdByPersonId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalesForecastJob" ADD CONSTRAINT "SalesForecastJob_storeId_fkey" FOREIGN KEY ("storeId") REFERENCES "Store"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalesForecastJob" ADD CONSTRAINT "SalesForecastJob_createdByPersonId_fkey" FOREIGN KEY ("createdByPersonId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ScheduleHourlySalesForecast" ADD CONSTRAINT "ScheduleHourlySalesForecast_storeId_fkey" FOREIGN KEY ("storeId") REFERENCES "Store"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ScheduleHourlySalesForecast" ADD CONSTRAINT "ScheduleHourlySalesForecast_scheduleId_fkey" FOREIGN KEY ("scheduleId") REFERENCES "Schedule"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ScheduleHourlySalesForecast" ADD CONSTRAINT "ScheduleHourlySalesForecast_createdFromSalesForecastJobId_fkey" FOREIGN KEY ("createdFromSalesForecastJobId") REFERENCES "SalesForecastJob"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalesHourlyDataPoint" ADD CONSTRAINT "SalesHourlyDataPoint_dataFileJobId_fkey" FOREIGN KEY ("dataFileJobId") REFERENCES "DataFileJob"("id") ON DELETE SET NULL ON UPDATE CASCADE;
